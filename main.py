from fastapi import <PERSON>AP<PERSON>, Depends, HTTPException, Request, Form, UploadFile, File, Query
from fastapi.responses import HTMLResponse, RedirectResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.sessions import SessionMiddleware
from sqlalchemy.orm import Session
from sqlalchemy import or_
from datetime import datetime, timedelta
from typing import Optional, List
import pandas as pd
import io
import json
import os

# Imports locaux
import models
import schemas
import crud
import auth
from database import get_db, create_tables
from utils.email import EmailService, get_default_variables, get_available_variables

# Configuration de l'application
app = FastAPI(
    title="CRM System",
    description="Système CRM avec gestion des leads et emails",
    version="1.0.0",
    docs_url="/docs" if os.getenv("DEBUG", "false").lower() == "true" else None,
    redoc_url="/redoc" if os.getenv("DEBUG", "false").lower() == "true" else None
)

# Import des middlewares personnalisés
from middleware import (
    SecurityMiddleware,
    PerformanceMiddleware,
    ErrorHandlingMiddleware,
    CORSMiddleware,
    CompressionMiddleware
)

# Ajouter les middlewares dans l'ordre correct (le dernier ajouté est exécuté en premier)
app.add_middleware(CompressionMiddleware)
app.add_middleware(PerformanceMiddleware)
app.add_middleware(SecurityMiddleware)
app.add_middleware(ErrorHandlingMiddleware)
app.add_middleware(CORSMiddleware, allowed_origins=[
    "http://localhost:8000",
    "https://localhost:8000",
    "http://127.0.0.1:8000",
    "https://127.0.0.1:8000"
])

# Middleware pour les sessions
app.add_middleware(SessionMiddleware, secret_key=os.getenv("SECRET_KEY", "your-secret-key-change-this"))

# Configuration des fichiers statiques et templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Créer les tables au démarrage
@app.on_event("startup")
async def startup_event():
    create_tables()
    
    # Créer un admin par défaut si aucun utilisateur n'existe
    db = next(get_db())
    users = crud.get_users(db, limit=1)
    if not users:
        admin_user = schemas.UserCreate(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            role="admin"
        )
        crud.create_user(db, admin_user)
        print("Utilisateur admin créé: admin / admin123")

# ============ ROUTES D'AUTHENTIFICATION ============

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Page d'accueil - redirige selon l'authentification"""
    if auth.is_authenticated(request):
        role = auth.get_user_role(request)
        if role == "admin":
            return RedirectResponse(url="/admin/dashboard", status_code=302)
        else:
            return RedirectResponse(url="/vendeur/dashboard", status_code=302)
    return RedirectResponse(url="/login", status_code=302)

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Page de connexion"""
    context = auth.get_template_context(request)
    return templates.TemplateResponse("login.html", context)

@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """Traitement de la connexion"""
    user = crud.authenticate_user(db, username, password)
    if not user:
        context = auth.get_template_context(request)
        context["error"] = "Nom d'utilisateur ou mot de passe incorrect"
        return templates.TemplateResponse("login.html", context)
    
    auth.login_user(request, user)
    
    # Log de connexion
    crud.create_action_log(db, user.id, "connexion", {"ip": request.client.host})
    
    # Redirection selon le rôle
    if user.role == "admin":
        return RedirectResponse(url="/admin/dashboard", status_code=302)
    else:
        return RedirectResponse(url="/vendeur/dashboard", status_code=302)

@app.get("/logout")
async def logout(request: Request):
    """Déconnexion"""
    auth.logout_user(request)
    return RedirectResponse(url="/login", status_code=302)

# ============ ROUTES ADMIN ============

@app.get("/admin/dashboard", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Dashboard administrateur"""
    context = auth.get_template_context(request)
    
    # Statistiques globales
    stats = crud.get_dashboard_stats(db)
    context["stats"] = stats
    
    # Clients récents
    context["recent_clients"] = crud.get_clients(db, limit=10)
    
    # Vendeurs
    context["vendeurs"] = crud.get_vendeurs(db)
    
    # Logs récents
    context["recent_logs"] = crud.get_action_logs(db, limit=10)
    
    return templates.TemplateResponse("admin/dashboard.html", context)

@app.get("/admin/clients", response_class=HTMLResponse)
async def admin_clients(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db),
    vendeur_id: Optional[int] = Query(None),
    indicateur: Optional[str] = Query(None)
):
    """Gestion des clients - Admin"""
    context = auth.get_template_context(request)
    
    # Filtres
    clients = crud.get_clients_by_filters(
        db, 
        vendeur_id=vendeur_id,
        indicateur=indicateur
    )
    
    context["clients"] = clients
    context["vendeurs"] = crud.get_vendeurs(db)
    context["selected_vendeur"] = vendeur_id
    context["selected_indicateur"] = indicateur
    
    return templates.TemplateResponse("admin/clients.html", context)

@app.get("/admin/vendeurs", response_class=HTMLResponse)
async def admin_vendeurs(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Gestion des vendeurs"""
    context = auth.get_template_context(request)
    context["vendeurs"] = crud.get_vendeurs(db)
    return templates.TemplateResponse("admin/vendeurs.html", context)

@app.get("/admin/templates", response_class=HTMLResponse)
async def admin_templates(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Gestion des templates d'email"""
    context = auth.get_template_context(request)
    context["templates"] = crud.get_email_templates(db)
    context["variables"] = get_available_variables()
    return templates.TemplateResponse("admin/templates.html", context)

@app.get("/admin/smtp", response_class=HTMLResponse)
async def admin_smtp(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Configuration SMTP"""
    context = auth.get_template_context(request)
    context["smtp_config"] = crud.get_smtp_config(db)
    return templates.TemplateResponse("admin/smtp.html", context)

@app.get("/admin/statistiques", response_class=HTMLResponse)
async def admin_statistiques(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Statistiques avancées"""
    context = auth.get_template_context(request)
    context["vendeurs"] = crud.get_vendeurs(db)
    return templates.TemplateResponse("admin/statistiques.html", context)

# ============ ROUTES VENDEUR ============

@app.get("/vendeur/dashboard", response_class=HTMLResponse)
async def vendeur_dashboard(
    request: Request,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Dashboard vendeur"""
    context = auth.get_template_context(request)
    
    # Statistiques du vendeur
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    stats = crud.get_dashboard_stats(db, vendeur_id=vendeur_id)
    context["stats"] = stats
    
    # Clients du vendeur
    context["clients"] = crud.get_clients(db, vendeur_id=vendeur_id, limit=10)
    
    # Rendez-vous à venir
    today = datetime.now()
    next_week = today + timedelta(days=7)
    context["upcoming_appointments"] = crud.get_appointments(
        db, 
        vendeur_id=vendeur_id,
        date_debut=today,
        date_fin=next_week
    )
    
    return templates.TemplateResponse("vendeur/dashboard.html", context)

@app.get("/vendeur/clients", response_class=HTMLResponse)
async def vendeur_clients(
    request: Request,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    indicateur: Optional[str] = Query(None)
):
    """Clients du vendeur"""
    context = auth.get_template_context(request)
    
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    clients = crud.get_clients_by_filters(
        db,
        vendeur_id=vendeur_id,
        indicateur=indicateur
    )
    
    context["clients"] = clients
    context["selected_indicateur"] = indicateur
    context["templates"] = crud.get_email_templates(db)
    
    return templates.TemplateResponse("vendeur/clients.html", context)

@app.get("/vendeur/agenda", response_class=HTMLResponse)
async def vendeur_agenda(
    request: Request,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Agenda du vendeur"""
    context = auth.get_template_context(request)
    
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    appointments = crud.get_appointments(db, vendeur_id=vendeur_id)
    
    context["appointments"] = appointments
    context["clients"] = crud.get_clients(db, vendeur_id=vendeur_id)
    
    return templates.TemplateResponse("vendeur/agenda.html", context)

# ============ API ROUTES - CLIENTS ============

@app.post("/api/clients")
async def create_client_api(
    client: schemas.ClientCreate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouveau client"""
    db_client = crud.create_client(db, client)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_client",
        {"client_id": db_client.id, "nom": db_client.nom, "prenom": db_client.prenom}
    )

    return db_client

@app.put("/api/clients/{client_id}")
async def update_client_api(
    client_id: int,
    client_update: schemas.ClientUpdate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un client"""
    # Vérifier l'accès
    client = crud.get_client(db, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    if not auth.can_access_client(current_user, client):
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    updated_client = crud.update_client(db, client_id, client_update)

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_client",
        {"client_id": client_id, "changes": client_update.dict(exclude_unset=True)}
    )

    return updated_client

@app.delete("/api/clients/{client_id}")
async def delete_client_api(
    client_id: int,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Supprimer un client (admin seulement)"""
    success = crud.delete_client(db, client_id)
    if not success:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    # Log de suppression
    crud.create_action_log(
        db,
        current_user.id,
        "suppression_client",
        {"client_id": client_id}
    )

    return {"message": "Client supprimé avec succès"}

@app.post("/api/clients/assign")
async def assign_clients_api(
    client_ids: List[int],
    vendeur_id: int,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Attribuer des clients à un vendeur"""
    updated_count = crud.assign_clients_to_vendeur(db, client_ids, vendeur_id)

    # Log d'attribution
    crud.create_action_log(
        db,
        current_user.id,
        "attribution_clients",
        {"client_ids": client_ids, "vendeur_id": vendeur_id, "count": updated_count}
    )

    return {"message": f"{updated_count} clients attribués avec succès"}

# ============ API ROUTES - IMPORT/EXPORT ============

@app.post("/api/clients/import")
async def import_clients_csv(
    file: UploadFile = File(...),
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Importer des clients depuis un fichier CSV"""
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="Le fichier doit être au format CSV")

    try:
        # Lire le fichier CSV
        content = await file.read()
        df = pd.read_csv(io.StringIO(content.decode('utf-8')))

        # Colonnes requises
        required_columns = ['nom', 'prenom', 'email', 'telephone', 'date_naissance']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Colonnes manquantes: {', '.join(missing_columns)}"
            )

        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                # Convertir la date de naissance
                date_naissance = pd.to_datetime(row['date_naissance']).to_pydatetime()

                client_data = schemas.ClientCreate(
                    nom=str(row['nom']),
                    prenom=str(row['prenom']),
                    email=str(row['email']),
                    telephone=str(row['telephone']),
                    date_naissance=date_naissance,
                    adresse=str(row.get('adresse', '')),
                    vendeur_id=int(row['vendeur_id']) if pd.notna(row.get('vendeur_id')) else None,
                    indicateur=str(row.get('indicateur', 'nouveau'))
                )

                crud.create_client(db, client_data)
                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f"Ligne {index + 2}: {str(e)}")

        # Log d'import
        crud.create_action_log(
            db,
            current_user.id,
            "import_csv",
            {
                "filename": file.filename,
                "success": success_count,
                "errors": error_count,
                "total": len(df)
            }
        )

        return {
            "success": success_count,
            "errors": error_count,
            "details": errors[:10]  # Limiter à 10 erreurs pour l'affichage
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Erreur lors de l'import: {str(e)}")

@app.get("/api/clients/export")
async def export_clients_csv(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    vendeur_id: Optional[int] = Query(None),
    indicateur: Optional[str] = Query(None),
    email_envoye: Optional[bool] = Query(None)
):
    """Exporter les clients en CSV"""
    # Filtrer selon le rôle
    if current_user.role == "vendeur":
        vendeur_id = current_user.id

    clients = crud.get_clients_by_filters(
        db,
        vendeur_id=vendeur_id,
        indicateur=indicateur,
        email_envoye=email_envoye
    )

    # Créer le DataFrame
    data = []
    for client in clients:
        data.append({
            'id': client.id,
            'nom': client.nom,
            'prenom': client.prenom,
            'email': client.email,
            'telephone': client.telephone,
            'date_naissance': client.date_naissance.strftime('%Y-%m-%d'),
            'adresse': client.adresse or '',
            'vendeur_id': client.vendeur_id,
            'indicateur': client.indicateur,
            'note': client.note or '',
            'email_envoye': client.email_envoye,
            'template_utilise': client.template_utilise or '',
            'created_at': client.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    df = pd.DataFrame(data)

    # Créer le fichier CSV
    output = io.StringIO()
    df.to_csv(output, index=False, encoding='utf-8')
    output.seek(0)

    # Log d'export
    crud.create_action_log(
        db,
        current_user.id,
        "export_csv",
        {"count": len(clients), "filters": {"vendeur_id": vendeur_id, "indicateur": indicateur}}
    )

    # Retourner le fichier
    filename = f"clients_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

    return StreamingResponse(
        io.BytesIO(output.getvalue().encode('utf-8')),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

# ============ API ROUTES - EMAILS ============

@app.post("/api/emails/send")
async def send_email_api(
    client_id: int,
    template_id: int,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Envoyer un email à un client"""
    # Vérifier l'accès au client
    client = crud.get_client(db, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    if not auth.can_access_client(current_user, client):
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    # Récupérer le template
    template = crud.get_email_template(db, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Récupérer la configuration SMTP
    smtp_config = crud.get_smtp_config(db)
    if not smtp_config:
        raise HTTPException(status_code=400, detail="Configuration SMTP non configurée")

    # Préparer les variables
    variables = get_default_variables({
        'prenom': client.prenom,
        'nom': client.nom,
        'email': client.email,
        'telephone': client.telephone,
        'adresse': client.adresse
    })

    # Créer le service email
    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    # Envoyer l'email
    success, message = email_service.send_email(
        client.email,
        template.sujet,
        template.contenu,
        variables
    )

    # Créer le log d'email
    email_log = schemas.EmailLogCreate(
        client_id=client_id,
        template_id=template_id,
        vendeur_id=current_user.id,
        sujet=email_service.replace_variables(template.sujet, variables),
        contenu=email_service.replace_variables(template.contenu, variables),
        statut="envoye" if success else "erreur",
        erreur_message=message if not success else None
    )
    crud.create_email_log(db, email_log)

    # Mettre à jour le client
    if success:
        crud.update_client(db, client_id, schemas.ClientUpdate(
            email_envoye=True,
            template_utilise=template.nom
        ))

    # Log d'action
    crud.create_action_log(
        db,
        current_user.id,
        "envoi_email",
        {
            "client_id": client_id,
            "template_id": template_id,
            "success": success,
            "message": message
        }
    )

    if success:
        return {"message": "Email envoyé avec succès"}
    else:
        raise HTTPException(status_code=400, detail=f"Erreur lors de l'envoi: {message}")

@app.post("/api/emails/send-bulk")
async def send_bulk_emails(
    client_ids: List[int],
    template_id: int,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Envoyer des emails en lot à plusieurs clients"""
    # Vérifier le template
    template = crud.get_email_template(db, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Récupérer la configuration SMTP
    smtp_config = crud.get_smtp_config(db)
    if not smtp_config:
        raise HTTPException(status_code=400, detail="Configuration SMTP non configurée")

    # Créer le service email
    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    results = {
        'success': 0,
        'errors': 0,
        'details': []
    }

    for client_id in client_ids:
        try:
            # Vérifier l'accès au client
            client = crud.get_client(db, client_id)
            if not client:
                results['errors'] += 1
                results['details'].append(f"Client {client_id} non trouvé")
                continue

            if not auth.can_access_client(current_user, client):
                results['errors'] += 1
                results['details'].append(f"Accès refusé au client {client.prenom} {client.nom}")
                continue

            # Préparer les variables
            variables = get_default_variables({
                'prenom': client.prenom,
                'nom': client.nom,
                'email': client.email,
                'telephone': client.telephone,
                'adresse': client.adresse
            })

            # Envoyer l'email
            success, message = email_service.send_email(
                client.email,
                template.sujet,
                template.contenu,
                variables
            )

            # Créer le log d'email
            email_log = schemas.EmailLogCreate(
                client_id=client_id,
                template_id=template_id,
                vendeur_id=current_user.id,
                sujet=email_service.replace_variables(template.sujet, variables),
                contenu=email_service.replace_variables(template.contenu, variables),
                statut="envoye" if success else "erreur",
                erreur_message=message if not success else None
            )
            crud.create_email_log(db, email_log)

            # Mettre à jour le client
            if success:
                crud.update_client(db, client_id, schemas.ClientUpdate(
                    email_envoye=True,
                    template_utilise=template.nom
                ))
                results['success'] += 1
                results['details'].append(f"✓ {client.prenom} {client.nom}: {message}")
            else:
                results['errors'] += 1
                results['details'].append(f"✗ {client.prenom} {client.nom}: {message}")

        except Exception as e:
            results['errors'] += 1
            results['details'].append(f"✗ Client {client_id}: {str(e)}")

    # Log d'action
    crud.create_action_log(
        db,
        current_user.id,
        "envoi_email_bulk",
        {
            "client_ids": client_ids,
            "template_id": template_id,
            "success": results['success'],
            "errors": results['errors']
        }
    )

    return results

@app.get("/api/emails/history")
async def get_email_history(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    client_id: Optional[int] = Query(None),
    limit: int = Query(50, le=200)
):
    """Récupérer l'historique des emails"""
    vendeur_id = current_user.id if current_user.role == "vendeur" else None

    email_logs = crud.get_email_logs(
        db,
        vendeur_id=vendeur_id,
        client_id=client_id,
        limit=limit
    )

    return [
        {
            "id": log.id,
            "client": {
                "id": log.client.id,
                "nom": log.client.nom,
                "prenom": log.client.prenom,
                "email": log.client.email
            },
            "template": log.template.nom if log.template else None,
            "sujet": log.sujet,
            "statut": log.statut,
            "date_envoi": log.date_envoi,
            "erreur_message": log.erreur_message,
            "vendeur": log.vendeur.username
        }
        for log in email_logs
    ]

@app.post("/api/emails/preview")
async def preview_email(
    template_id: int,
    client_id: int,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Prévisualiser un email avec les données d'un client"""
    # Vérifier le template
    template = crud.get_email_template(db, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Vérifier l'accès au client
    client = crud.get_client(db, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    if not auth.can_access_client(current_user, client):
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    # Préparer les variables
    variables = get_default_variables({
        'prenom': client.prenom,
        'nom': client.nom,
        'email': client.email,
        'telephone': client.telephone,
        'adresse': client.adresse
    })

    # Créer le service email pour le remplacement des variables
    email_service = EmailService({
        'host': 'dummy',
        'port': 587,
        'username': 'dummy',
        'password': 'dummy',
        'use_tls': True,
        'from_email': '<EMAIL>',
        'from_name': 'Dummy'
    })

    # Remplacer les variables
    sujet_preview = email_service.replace_variables(template.sujet, variables)
    contenu_preview = email_service.replace_variables(template.contenu, variables)

    return {
        "template_nom": template.nom,
        "client": {
            "nom": client.nom,
            "prenom": client.prenom,
            "email": client.email
        },
        "sujet": sujet_preview,
        "contenu": contenu_preview,
        "variables_utilisees": variables
    }

# ============ API ROUTES - TEMPLATES ============

@app.post("/api/templates")
async def create_template_api(
    template: schemas.EmailTemplateCreate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouveau template d'email"""
    # Vérifier que le nom n'existe pas déjà
    existing = crud.get_email_template_by_name(db, template.nom)
    if existing:
        raise HTTPException(status_code=400, detail="Un template avec ce nom existe déjà")

    db_template = crud.create_email_template(db, template)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_template",
        {"template_id": db_template.id, "nom": db_template.nom}
    )

    return db_template

@app.put("/api/templates/{template_id}")
async def update_template_api(
    template_id: int,
    template_update: schemas.EmailTemplateUpdate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un template"""
    updated_template = crud.update_email_template(db, template_id, template_update)
    if not updated_template:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_template",
        {"template_id": template_id, "changes": template_update.dict(exclude_unset=True)}
    )

    return updated_template

@app.delete("/api/templates/{template_id}")
async def delete_template_api(
    template_id: int,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Supprimer un template"""
    success = crud.delete_email_template(db, template_id)
    if not success:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Log de suppression
    crud.create_action_log(
        db,
        current_user.id,
        "suppression_template",
        {"template_id": template_id}
    )

    return {"message": "Template supprimé avec succès"}

# ============ API ROUTES - RENDEZ-VOUS ============

@app.post("/api/appointments")
async def create_appointment_api(
    appointment: schemas.AppointmentCreate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouveau rendez-vous"""
    # Vérifier l'accès au client
    client = crud.get_client(db, appointment.client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    if not auth.can_access_client(current_user, client):
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    # Si vendeur, forcer son ID
    if current_user.role == "vendeur":
        appointment.vendeur_id = current_user.id

    db_appointment = crud.create_appointment(db, appointment)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_rdv",
        {
            "appointment_id": db_appointment.id,
            "client_id": appointment.client_id,
            "date_rdv": str(appointment.date_rdv)
        }
    )

    return db_appointment

@app.put("/api/appointments/{appointment_id}")
async def update_appointment_api(
    appointment_id: int,
    appointment_update: schemas.AppointmentUpdate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un rendez-vous"""
    appointment = crud.get_appointment(db, appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Rendez-vous non trouvé")

    # Vérifier l'accès
    if current_user.role == "vendeur" and appointment.vendeur_id != current_user.id:
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    updated_appointment = crud.update_appointment(db, appointment_id, appointment_update)

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_rdv",
        {"appointment_id": appointment_id, "changes": appointment_update.dict(exclude_unset=True)}
    )

    return updated_appointment

@app.get("/api/appointments/calendar")
async def get_calendar_appointments(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    start_date: datetime = Query(...),
    end_date: datetime = Query(...)
):
    """Récupérer les rendez-vous pour une vue calendrier"""
    vendeur_id = current_user.id if current_user.role == "vendeur" else None

    appointments = crud.get_appointments(
        db,
        vendeur_id=vendeur_id,
        date_debut=start_date,
        date_fin=end_date
    )

    # Formater pour le calendrier
    calendar_events = []
    for appointment in appointments:
        color = {
            'planifie': '#ffc107',  # warning
            'realise': '#28a745',   # success
            'annule': '#dc3545'     # danger
        }.get(appointment.statut, '#6c757d')

        calendar_events.append({
            'id': appointment.id,
            'title': f"{appointment.client.prenom} {appointment.client.nom}",
            'start': appointment.date_rdv.isoformat(),
            'end': (appointment.date_rdv + timedelta(hours=1)).isoformat(),  # Durée par défaut 1h
            'backgroundColor': color,
            'borderColor': color,
            'extendedProps': {
                'client_id': appointment.client_id,
                'client_name': f"{appointment.client.prenom} {appointment.client.nom}",
                'client_phone': appointment.client.telephone,
                'description': appointment.description,
                'statut': appointment.statut,
                'vendeur': appointment.vendeur.username
            }
        })

    return calendar_events

@app.get("/api/appointments/upcoming")
async def get_upcoming_appointments(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    days: int = Query(7, ge=1, le=30)
):
    """Récupérer les prochains rendez-vous"""
    vendeur_id = current_user.id if current_user.role == "vendeur" else None

    start_date = datetime.now()
    end_date = start_date + timedelta(days=days)

    appointments = crud.get_appointments(
        db,
        vendeur_id=vendeur_id,
        date_debut=start_date,
        date_fin=end_date
    )

    # Filtrer seulement les planifiés
    upcoming = [apt for apt in appointments if apt.statut == "planifie"]
    upcoming.sort(key=lambda x: x.date_rdv)

    return [
        {
            "id": appointment.id,
            "date_rdv": appointment.date_rdv,
            "titre": appointment.titre,
            "client": {
                "id": appointment.client.id,
                "nom": appointment.client.nom,
                "prenom": appointment.client.prenom,
                "telephone": appointment.client.telephone,
                "email": appointment.client.email
            },
            "description": appointment.description,
            "statut": appointment.statut,
            "time_until": (appointment.date_rdv - datetime.now()).total_seconds()
        }
        for appointment in upcoming
    ]

@app.post("/api/appointments/{appointment_id}/send-reminder")
async def send_appointment_reminder(
    appointment_id: int,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Envoyer un rappel de rendez-vous par email"""
    appointment = crud.get_appointment(db, appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Rendez-vous non trouvé")

    # Vérifier l'accès
    if current_user.role == "vendeur" and appointment.vendeur_id != current_user.id:
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    # Récupérer le template de confirmation RDV
    template = crud.get_email_template_by_name(db, "Confirmation RDV")
    if not template:
        raise HTTPException(status_code=400, detail="Template 'Confirmation RDV' non trouvé")

    # Récupérer la configuration SMTP
    smtp_config = crud.get_smtp_config(db)
    if not smtp_config:
        raise HTTPException(status_code=400, detail="Configuration SMTP non configurée")

    # Préparer les variables avec les données du RDV
    variables = get_default_variables(
        {
            'prenom': appointment.client.prenom,
            'nom': appointment.client.nom,
            'email': appointment.client.email,
            'telephone': appointment.client.telephone,
            'adresse': appointment.client.adresse
        },
        {
            'date_rdv': appointment.date_rdv,
            'titre': appointment.titre,
            'description': appointment.description
        }
    )

    # Créer le service email
    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    # Envoyer l'email
    success, message = email_service.send_email(
        appointment.client.email,
        template.sujet,
        template.contenu,
        variables
    )

    # Créer le log d'email
    if success:
        email_log = schemas.EmailLogCreate(
            client_id=appointment.client_id,
            template_id=template.id,
            vendeur_id=current_user.id,
            sujet=email_service.replace_variables(template.sujet, variables),
            contenu=email_service.replace_variables(template.contenu, variables),
            statut="envoye"
        )
        crud.create_email_log(db, email_log)

    # Log d'action
    crud.create_action_log(
        db,
        current_user.id,
        "rappel_rdv",
        {
            "appointment_id": appointment_id,
            "client_id": appointment.client_id,
            "success": success,
            "message": message
        }
    )

    if success:
        return {"message": "Rappel envoyé avec succès"}
    else:
        raise HTTPException(status_code=400, detail=f"Erreur lors de l'envoi: {message}")

@app.delete("/api/appointments/{appointment_id}")
async def delete_appointment_api(
    appointment_id: int,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Supprimer un rendez-vous"""
    appointment = crud.get_appointment(db, appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Rendez-vous non trouvé")

    # Vérifier l'accès
    if current_user.role == "vendeur" and appointment.vendeur_id != current_user.id:
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    success = crud.delete_appointment(db, appointment_id)

    # Log de suppression
    crud.create_action_log(
        db,
        current_user.id,
        "suppression_rdv",
        {"appointment_id": appointment_id}
    )

    return {"message": "Rendez-vous supprimé avec succès"}

# ============ API ROUTES - CONFIGURATION SMTP ============

@app.post("/api/smtp/config")
async def configure_smtp_api(
    smtp_config: schemas.SMTPConfigCreate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Configurer les paramètres SMTP"""
    # Tester la configuration avant de sauvegarder
    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    success, message = email_service.test_connection()
    if not success:
        raise HTTPException(status_code=400, detail=f"Test de connexion échoué: {message}")

    # Sauvegarder la configuration
    db_config = crud.create_or_update_smtp_config(db, smtp_config)

    # Log de configuration
    crud.create_action_log(
        db,
        current_user.id,
        "configuration_smtp",
        {"host": smtp_config.host, "port": smtp_config.port}
    )

    return {"message": "Configuration SMTP sauvegardée avec succès", "config": db_config}

@app.post("/api/smtp/test")
async def test_smtp_api(
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Tester la configuration SMTP actuelle"""
    smtp_config = crud.get_smtp_config(db)
    if not smtp_config:
        raise HTTPException(status_code=400, detail="Aucune configuration SMTP trouvée")

    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    success, message = email_service.test_connection()

    return {"success": success, "message": message}

# ============ API ROUTES - UTILISATEURS ============

@app.post("/api/users")
async def create_user_api(
    user: schemas.UserCreate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouvel utilisateur"""
    # Vérifier que l'username n'existe pas
    existing_user = crud.get_user_by_username(db, user.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="Ce nom d'utilisateur existe déjà")

    # Vérifier que l'email n'existe pas
    existing_email = crud.get_user_by_email(db, user.email)
    if existing_email:
        raise HTTPException(status_code=400, detail="Cet email existe déjà")

    db_user = crud.create_user(db, user)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_utilisateur",
        {"user_id": db_user.id, "username": db_user.username, "role": db_user.role}
    )

    return db_user

@app.put("/api/users/{user_id}")
async def update_user_api(
    user_id: int,
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un utilisateur"""
    updated_user = crud.update_user(db, user_id, user_update)
    if not updated_user:
        raise HTTPException(status_code=404, detail="Utilisateur non trouvé")

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_utilisateur",
        {"user_id": user_id, "changes": user_update.dict(exclude_unset=True)}
    )

    return updated_user

@app.post("/api/users/{user_id}/reset-password")
async def reset_user_password(
    user_id: int,
    new_password: str = Form(...),
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Réinitialiser le mot de passe d'un utilisateur"""
    user = crud.get_user(db, user_id)
    if not user:
        raise HTTPException(status_code=404, detail="Utilisateur non trouvé")

    # Mettre à jour le mot de passe
    hashed_password = crud.get_password_hash(new_password)
    user.password_hash = hashed_password
    db.commit()

    # Log de réinitialisation
    crud.create_action_log(
        db,
        current_user.id,
        "reset_password",
        {"target_user_id": user_id, "target_username": user.username}
    )

    return {"message": "Mot de passe réinitialisé avec succès"}

# ============ API ROUTES - STATISTIQUES ============

@app.get("/api/stats")
async def get_stats_api(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Récupérer les statistiques"""
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    stats = crud.get_dashboard_stats(db, vendeur_id=vendeur_id)
    return stats

# ============ ROUTES DE FORMULAIRES HTML ============

@app.post("/admin/clients/assign")
async def assign_clients_form(
    request: Request,
    client_ids: str = Form(...),
    vendeur_id: int = Form(...),
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Attribution de clients via formulaire"""
    try:
        # Parser les IDs des clients
        client_id_list = [int(id.strip()) for id in client_ids.split(',') if id.strip()]

        updated_count = crud.assign_clients_to_vendeur(db, client_id_list, vendeur_id)

        # Log d'attribution
        crud.create_action_log(
            db,
            current_user.id,
            "attribution_clients",
            {"client_ids": client_id_list, "vendeur_id": vendeur_id, "count": updated_count}
        )

        return RedirectResponse(url="/admin/clients?success=attribution", status_code=302)

    except Exception as e:
        return RedirectResponse(url=f"/admin/clients?error={str(e)}", status_code=302)

# ============ API ROUTES - RECHERCHE AVANCÉE ============

@app.get("/api/clients/search")
async def search_clients(
    q: str = Query(..., min_length=2),
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Recherche avancée de clients"""
    # Filtrer selon le rôle
    vendeur_id = current_user.id if current_user.role == "vendeur" else None

    # Recherche dans nom, prénom, email, téléphone
    query = db.query(models.Client)
    if vendeur_id:
        query = query.filter(models.Client.vendeur_id == vendeur_id)

    # Recherche textuelle
    search_filter = or_(
        models.Client.nom.ilike(f"%{q}%"),
        models.Client.prenom.ilike(f"%{q}%"),
        models.Client.email.ilike(f"%{q}%"),
        models.Client.telephone.ilike(f"%{q}%"),
        models.Client.adresse.ilike(f"%{q}%"),
        models.Client.note.ilike(f"%{q}%")
    )

    clients = query.filter(search_filter).limit(20).all()

    return [
        {
            "id": client.id,
            "nom": client.nom,
            "prenom": client.prenom,
            "email": client.email,
            "telephone": client.telephone,
            "indicateur": client.indicateur,
            "vendeur": client.vendeur.username if client.vendeur else None
        }
        for client in clients
    ]

@app.post("/api/clients/bulk-update")
async def bulk_update_clients(
    client_ids: List[int],
    updates: schemas.ClientUpdate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Mise à jour en lot des clients"""
    updated_count = 0
    errors = []

    for client_id in client_ids:
        try:
            client = crud.get_client(db, client_id)
            if client:
                crud.update_client(db, client_id, updates)
                updated_count += 1
            else:
                errors.append(f"Client {client_id} non trouvé")
        except Exception as e:
            errors.append(f"Erreur client {client_id}: {str(e)}")

    # Log de mise à jour en lot
    crud.create_action_log(
        db,
        current_user.id,
        "bulk_update_clients",
        {
            "client_ids": client_ids,
            "updates": updates.dict(exclude_unset=True),
            "updated_count": updated_count,
            "errors_count": len(errors)
        }
    )

    return {
        "updated": updated_count,
        "errors": len(errors),
        "details": errors[:10]  # Limiter les erreurs affichées
    }

@app.get("/api/clients/export-advanced")
async def export_clients_advanced(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    format: str = Query("csv", regex="^(csv|xlsx)$"),
    vendeur_id: Optional[int] = Query(None),
    indicateur: Optional[str] = Query(None),
    email_envoye: Optional[bool] = Query(None),
    date_debut: Optional[datetime] = Query(None),
    date_fin: Optional[datetime] = Query(None),
    include_notes: bool = Query(True),
    include_history: bool = Query(False)
):
    """Export avancé des clients avec options"""
    # Filtrer selon le rôle
    if current_user.role == "vendeur":
        vendeur_id = current_user.id

    clients = crud.get_clients_by_filters(
        db,
        vendeur_id=vendeur_id,
        indicateur=indicateur,
        email_envoye=email_envoye,
        date_debut=date_debut,
        date_fin=date_fin
    )

    # Préparer les données
    data = []
    for client in clients:
        row = {
            'id': client.id,
            'nom': client.nom,
            'prenom': client.prenom,
            'email': client.email,
            'telephone': client.telephone,
            'date_naissance': client.date_naissance.strftime('%Y-%m-%d'),
            'adresse': client.adresse or '',
            'vendeur': client.vendeur.username if client.vendeur else '',
            'indicateur': client.indicateur,
            'email_envoye': 'Oui' if client.email_envoye else 'Non',
            'template_utilise': client.template_utilise or '',
            'created_at': client.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': client.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        }

        if include_notes:
            row['note'] = client.note or ''

        if include_history:
            # Ajouter l'historique des emails et RDV
            emails = crud.get_email_logs(db, client_id=client.id, limit=5)
            appointments = crud.get_appointments_by_client(db, client.id)

            row['emails_count'] = len(emails)
            row['appointments_count'] = len(appointments)
            row['last_email'] = emails[0].date_envoi.strftime('%Y-%m-%d') if emails else ''
            row['next_appointment'] = min([apt.date_rdv for apt in appointments if apt.date_rdv > datetime.now()], default=None)
            if row['next_appointment']:
                row['next_appointment'] = row['next_appointment'].strftime('%Y-%m-%d %H:%M')
            else:
                row['next_appointment'] = ''

        data.append(row)

    df = pd.DataFrame(data)

    # Générer le fichier selon le format
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    if format == "xlsx":
        output = io.BytesIO()
        with pd.ExcelWriter(output, engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name='Clients', index=False)
        output.seek(0)

        filename = f"clients_export_{timestamp}.xlsx"
        media_type = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
    else:
        output = io.StringIO()
        df.to_csv(output, index=False, encoding='utf-8')
        output.seek(0)

        filename = f"clients_export_{timestamp}.csv"
        media_type = "text/csv"
        output = io.BytesIO(output.getvalue().encode('utf-8'))

    # Log d'export
    crud.create_action_log(
        db,
        current_user.id,
        "export_advanced",
        {
            "format": format,
            "count": len(clients),
            "filters": {
                "vendeur_id": vendeur_id,
                "indicateur": indicateur,
                "include_notes": include_notes,
                "include_history": include_history
            }
        }
    )

    return StreamingResponse(
        output,
        media_type=media_type,
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

# ============ API ROUTES - RAPPORTS ============

@app.get("/api/reports/dashboard")
async def get_dashboard_report(
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db),
    date_debut: Optional[datetime] = Query(None),
    date_fin: Optional[datetime] = Query(None)
):
    """Rapport complet pour le dashboard admin"""
    # Statistiques générales
    stats = crud.get_dashboard_stats(db)

    # Statistiques par vendeur
    vendeurs = crud.get_vendeurs(db)
    vendeur_stats = []

    for vendeur in vendeurs:
        vendeur_data = crud.get_dashboard_stats(db, vendeur_id=vendeur.id)
        vendeur_stats.append({
            "vendeur": vendeur.username,
            "vendeur_id": vendeur.id,
            "stats": vendeur_data
        })

    # Évolution temporelle (30 derniers jours)
    evolution = []
    for i in range(30):
        date = datetime.now() - timedelta(days=i)
        date_start = date.replace(hour=0, minute=0, second=0, microsecond=0)
        date_end = date.replace(hour=23, minute=59, second=59, microsecond=999999)

        clients_created = db.query(models.Client).filter(
            models.Client.created_at >= date_start,
            models.Client.created_at <= date_end
        ).count()

        emails_sent = db.query(models.EmailLog).filter(
            models.EmailLog.date_envoi >= date_start,
            models.EmailLog.date_envoi <= date_end
        ).count()

        evolution.append({
            "date": date.strftime('%Y-%m-%d'),
            "clients_created": clients_created,
            "emails_sent": emails_sent
        })

    return {
        "stats_globales": stats,
        "stats_vendeurs": vendeur_stats,
        "evolution": list(reversed(evolution))  # Plus récent en dernier
    }

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

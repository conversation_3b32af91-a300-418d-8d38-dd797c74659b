from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, Request, Form, UploadFile, File, Query
from fastapi.responses import HTMLResponse, RedirectResponse, StreamingResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.middleware.sessions import SessionMiddleware
from sqlalchemy.orm import Session
from datetime import datetime, timed<PERSON>ta
from typing import Optional, List
import pandas as pd
import io
import json
import os

# Imports locaux
import models
import schemas
import crud
import auth
from database import get_db, create_tables
from utils.email import EmailService, get_default_variables, get_available_variables

# Configuration de l'application
app = FastAPI(title="CRM System", description="Système CRM avec gestion des leads et emails")

# Middleware pour les sessions
app.add_middleware(SessionMiddleware, secret_key=os.getenv("SECRET_KEY", "your-secret-key-change-this"))

# Configuration des fichiers statiques et templates
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Créer les tables au démarrage
@app.on_event("startup")
async def startup_event():
    create_tables()
    
    # Créer un admin par défaut si aucun utilisateur n'existe
    db = next(get_db())
    users = crud.get_users(db, limit=1)
    if not users:
        admin_user = schemas.UserCreate(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            role="admin"
        )
        crud.create_user(db, admin_user)
        print("Utilisateur admin créé: admin / admin123")

# ============ ROUTES D'AUTHENTIFICATION ============

@app.get("/", response_class=HTMLResponse)
async def root(request: Request):
    """Page d'accueil - redirige selon l'authentification"""
    if auth.is_authenticated(request):
        role = auth.get_user_role(request)
        if role == "admin":
            return RedirectResponse(url="/admin/dashboard", status_code=302)
        else:
            return RedirectResponse(url="/vendeur/dashboard", status_code=302)
    return RedirectResponse(url="/login", status_code=302)

@app.get("/login", response_class=HTMLResponse)
async def login_page(request: Request):
    """Page de connexion"""
    context = auth.get_template_context(request)
    return templates.TemplateResponse("login.html", context)

@app.post("/login")
async def login(
    request: Request,
    username: str = Form(...),
    password: str = Form(...),
    db: Session = Depends(get_db)
):
    """Traitement de la connexion"""
    user = crud.authenticate_user(db, username, password)
    if not user:
        context = auth.get_template_context(request)
        context["error"] = "Nom d'utilisateur ou mot de passe incorrect"
        return templates.TemplateResponse("login.html", context)
    
    auth.login_user(request, user)
    
    # Log de connexion
    crud.create_action_log(db, user.id, "connexion", {"ip": request.client.host})
    
    # Redirection selon le rôle
    if user.role == "admin":
        return RedirectResponse(url="/admin/dashboard", status_code=302)
    else:
        return RedirectResponse(url="/vendeur/dashboard", status_code=302)

@app.get("/logout")
async def logout(request: Request):
    """Déconnexion"""
    auth.logout_user(request)
    return RedirectResponse(url="/login", status_code=302)

# ============ ROUTES ADMIN ============

@app.get("/admin/dashboard", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Dashboard administrateur"""
    context = auth.get_template_context(request)
    
    # Statistiques globales
    stats = crud.get_dashboard_stats(db)
    context["stats"] = stats
    
    # Clients récents
    context["recent_clients"] = crud.get_clients(db, limit=10)
    
    # Vendeurs
    context["vendeurs"] = crud.get_vendeurs(db)
    
    # Logs récents
    context["recent_logs"] = crud.get_action_logs(db, limit=10)
    
    return templates.TemplateResponse("admin/dashboard.html", context)

@app.get("/admin/clients", response_class=HTMLResponse)
async def admin_clients(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db),
    vendeur_id: Optional[int] = Query(None),
    indicateur: Optional[str] = Query(None)
):
    """Gestion des clients - Admin"""
    context = auth.get_template_context(request)
    
    # Filtres
    clients = crud.get_clients_by_filters(
        db, 
        vendeur_id=vendeur_id,
        indicateur=indicateur
    )
    
    context["clients"] = clients
    context["vendeurs"] = crud.get_vendeurs(db)
    context["selected_vendeur"] = vendeur_id
    context["selected_indicateur"] = indicateur
    
    return templates.TemplateResponse("admin/clients.html", context)

@app.get("/admin/vendeurs", response_class=HTMLResponse)
async def admin_vendeurs(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Gestion des vendeurs"""
    context = auth.get_template_context(request)
    context["vendeurs"] = crud.get_vendeurs(db)
    return templates.TemplateResponse("admin/vendeurs.html", context)

@app.get("/admin/templates", response_class=HTMLResponse)
async def admin_templates(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Gestion des templates d'email"""
    context = auth.get_template_context(request)
    context["templates"] = crud.get_email_templates(db)
    context["variables"] = get_available_variables()
    return templates.TemplateResponse("admin/templates.html", context)

@app.get("/admin/smtp", response_class=HTMLResponse)
async def admin_smtp(
    request: Request,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Configuration SMTP"""
    context = auth.get_template_context(request)
    context["smtp_config"] = crud.get_smtp_config(db)
    return templates.TemplateResponse("admin/smtp.html", context)

# ============ ROUTES VENDEUR ============

@app.get("/vendeur/dashboard", response_class=HTMLResponse)
async def vendeur_dashboard(
    request: Request,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Dashboard vendeur"""
    context = auth.get_template_context(request)
    
    # Statistiques du vendeur
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    stats = crud.get_dashboard_stats(db, vendeur_id=vendeur_id)
    context["stats"] = stats
    
    # Clients du vendeur
    context["clients"] = crud.get_clients(db, vendeur_id=vendeur_id, limit=10)
    
    # Rendez-vous à venir
    today = datetime.now()
    next_week = today + timedelta(days=7)
    context["upcoming_appointments"] = crud.get_appointments(
        db, 
        vendeur_id=vendeur_id,
        date_debut=today,
        date_fin=next_week
    )
    
    return templates.TemplateResponse("vendeur/dashboard.html", context)

@app.get("/vendeur/clients", response_class=HTMLResponse)
async def vendeur_clients(
    request: Request,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    indicateur: Optional[str] = Query(None)
):
    """Clients du vendeur"""
    context = auth.get_template_context(request)
    
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    clients = crud.get_clients_by_filters(
        db,
        vendeur_id=vendeur_id,
        indicateur=indicateur
    )
    
    context["clients"] = clients
    context["selected_indicateur"] = indicateur
    context["templates"] = crud.get_email_templates(db)
    
    return templates.TemplateResponse("vendeur/clients.html", context)

@app.get("/vendeur/agenda", response_class=HTMLResponse)
async def vendeur_agenda(
    request: Request,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Agenda du vendeur"""
    context = auth.get_template_context(request)
    
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    appointments = crud.get_appointments(db, vendeur_id=vendeur_id)
    
    context["appointments"] = appointments
    context["clients"] = crud.get_clients(db, vendeur_id=vendeur_id)
    
    return templates.TemplateResponse("vendeur/agenda.html", context)

# ============ API ROUTES - CLIENTS ============

@app.post("/api/clients")
async def create_client_api(
    client: schemas.ClientCreate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouveau client"""
    db_client = crud.create_client(db, client)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_client",
        {"client_id": db_client.id, "nom": db_client.nom, "prenom": db_client.prenom}
    )

    return db_client

@app.put("/api/clients/{client_id}")
async def update_client_api(
    client_id: int,
    client_update: schemas.ClientUpdate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un client"""
    # Vérifier l'accès
    client = crud.get_client(db, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    if not auth.can_access_client(current_user, client):
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    updated_client = crud.update_client(db, client_id, client_update)

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_client",
        {"client_id": client_id, "changes": client_update.dict(exclude_unset=True)}
    )

    return updated_client

@app.delete("/api/clients/{client_id}")
async def delete_client_api(
    client_id: int,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Supprimer un client (admin seulement)"""
    success = crud.delete_client(db, client_id)
    if not success:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    # Log de suppression
    crud.create_action_log(
        db,
        current_user.id,
        "suppression_client",
        {"client_id": client_id}
    )

    return {"message": "Client supprimé avec succès"}

@app.post("/api/clients/assign")
async def assign_clients_api(
    client_ids: List[int],
    vendeur_id: int,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Attribuer des clients à un vendeur"""
    updated_count = crud.assign_clients_to_vendeur(db, client_ids, vendeur_id)

    # Log d'attribution
    crud.create_action_log(
        db,
        current_user.id,
        "attribution_clients",
        {"client_ids": client_ids, "vendeur_id": vendeur_id, "count": updated_count}
    )

    return {"message": f"{updated_count} clients attribués avec succès"}

# ============ API ROUTES - IMPORT/EXPORT ============

@app.post("/api/clients/import")
async def import_clients_csv(
    file: UploadFile = File(...),
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Importer des clients depuis un fichier CSV"""
    if not file.filename.endswith('.csv'):
        raise HTTPException(status_code=400, detail="Le fichier doit être au format CSV")

    try:
        # Lire le fichier CSV
        content = await file.read()
        df = pd.read_csv(io.StringIO(content.decode('utf-8')))

        # Colonnes requises
        required_columns = ['nom', 'prenom', 'email', 'telephone', 'date_naissance']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise HTTPException(
                status_code=400,
                detail=f"Colonnes manquantes: {', '.join(missing_columns)}"
            )

        success_count = 0
        error_count = 0
        errors = []

        for index, row in df.iterrows():
            try:
                # Convertir la date de naissance
                date_naissance = pd.to_datetime(row['date_naissance']).to_pydatetime()

                client_data = schemas.ClientCreate(
                    nom=str(row['nom']),
                    prenom=str(row['prenom']),
                    email=str(row['email']),
                    telephone=str(row['telephone']),
                    date_naissance=date_naissance,
                    adresse=str(row.get('adresse', '')),
                    vendeur_id=int(row['vendeur_id']) if pd.notna(row.get('vendeur_id')) else None,
                    indicateur=str(row.get('indicateur', 'nouveau'))
                )

                crud.create_client(db, client_data)
                success_count += 1

            except Exception as e:
                error_count += 1
                errors.append(f"Ligne {index + 2}: {str(e)}")

        # Log d'import
        crud.create_action_log(
            db,
            current_user.id,
            "import_csv",
            {
                "filename": file.filename,
                "success": success_count,
                "errors": error_count,
                "total": len(df)
            }
        )

        return {
            "success": success_count,
            "errors": error_count,
            "details": errors[:10]  # Limiter à 10 erreurs pour l'affichage
        }

    except Exception as e:
        raise HTTPException(status_code=400, detail=f"Erreur lors de l'import: {str(e)}")

@app.get("/api/clients/export")
async def export_clients_csv(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db),
    vendeur_id: Optional[int] = Query(None),
    indicateur: Optional[str] = Query(None),
    email_envoye: Optional[bool] = Query(None)
):
    """Exporter les clients en CSV"""
    # Filtrer selon le rôle
    if current_user.role == "vendeur":
        vendeur_id = current_user.id

    clients = crud.get_clients_by_filters(
        db,
        vendeur_id=vendeur_id,
        indicateur=indicateur,
        email_envoye=email_envoye
    )

    # Créer le DataFrame
    data = []
    for client in clients:
        data.append({
            'id': client.id,
            'nom': client.nom,
            'prenom': client.prenom,
            'email': client.email,
            'telephone': client.telephone,
            'date_naissance': client.date_naissance.strftime('%Y-%m-%d'),
            'adresse': client.adresse or '',
            'vendeur_id': client.vendeur_id,
            'indicateur': client.indicateur,
            'note': client.note or '',
            'email_envoye': client.email_envoye,
            'template_utilise': client.template_utilise or '',
            'created_at': client.created_at.strftime('%Y-%m-%d %H:%M:%S')
        })

    df = pd.DataFrame(data)

    # Créer le fichier CSV
    output = io.StringIO()
    df.to_csv(output, index=False, encoding='utf-8')
    output.seek(0)

    # Log d'export
    crud.create_action_log(
        db,
        current_user.id,
        "export_csv",
        {"count": len(clients), "filters": {"vendeur_id": vendeur_id, "indicateur": indicateur}}
    )

    # Retourner le fichier
    filename = f"clients_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"

    return StreamingResponse(
        io.BytesIO(output.getvalue().encode('utf-8')),
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )

# ============ API ROUTES - EMAILS ============

@app.post("/api/emails/send")
async def send_email_api(
    client_id: int,
    template_id: int,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Envoyer un email à un client"""
    # Vérifier l'accès au client
    client = crud.get_client(db, client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    if not auth.can_access_client(current_user, client):
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    # Récupérer le template
    template = crud.get_email_template(db, template_id)
    if not template:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Récupérer la configuration SMTP
    smtp_config = crud.get_smtp_config(db)
    if not smtp_config:
        raise HTTPException(status_code=400, detail="Configuration SMTP non configurée")

    # Préparer les variables
    variables = get_default_variables({
        'prenom': client.prenom,
        'nom': client.nom,
        'email': client.email,
        'telephone': client.telephone,
        'adresse': client.adresse
    })

    # Créer le service email
    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    # Envoyer l'email
    success, message = email_service.send_email(
        client.email,
        template.sujet,
        template.contenu,
        variables
    )

    # Créer le log d'email
    email_log = schemas.EmailLogCreate(
        client_id=client_id,
        template_id=template_id,
        vendeur_id=current_user.id,
        sujet=email_service.replace_variables(template.sujet, variables),
        contenu=email_service.replace_variables(template.contenu, variables),
        statut="envoye" if success else "erreur",
        erreur_message=message if not success else None
    )
    crud.create_email_log(db, email_log)

    # Mettre à jour le client
    if success:
        crud.update_client(db, client_id, schemas.ClientUpdate(
            email_envoye=True,
            template_utilise=template.nom
        ))

    # Log d'action
    crud.create_action_log(
        db,
        current_user.id,
        "envoi_email",
        {
            "client_id": client_id,
            "template_id": template_id,
            "success": success,
            "message": message
        }
    )

    if success:
        return {"message": "Email envoyé avec succès"}
    else:
        raise HTTPException(status_code=400, detail=f"Erreur lors de l'envoi: {message}")

# ============ API ROUTES - TEMPLATES ============

@app.post("/api/templates")
async def create_template_api(
    template: schemas.EmailTemplateCreate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouveau template d'email"""
    # Vérifier que le nom n'existe pas déjà
    existing = crud.get_email_template_by_name(db, template.nom)
    if existing:
        raise HTTPException(status_code=400, detail="Un template avec ce nom existe déjà")

    db_template = crud.create_email_template(db, template)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_template",
        {"template_id": db_template.id, "nom": db_template.nom}
    )

    return db_template

@app.put("/api/templates/{template_id}")
async def update_template_api(
    template_id: int,
    template_update: schemas.EmailTemplateUpdate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un template"""
    updated_template = crud.update_email_template(db, template_id, template_update)
    if not updated_template:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_template",
        {"template_id": template_id, "changes": template_update.dict(exclude_unset=True)}
    )

    return updated_template

@app.delete("/api/templates/{template_id}")
async def delete_template_api(
    template_id: int,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Supprimer un template"""
    success = crud.delete_email_template(db, template_id)
    if not success:
        raise HTTPException(status_code=404, detail="Template non trouvé")

    # Log de suppression
    crud.create_action_log(
        db,
        current_user.id,
        "suppression_template",
        {"template_id": template_id}
    )

    return {"message": "Template supprimé avec succès"}

# ============ API ROUTES - RENDEZ-VOUS ============

@app.post("/api/appointments")
async def create_appointment_api(
    appointment: schemas.AppointmentCreate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouveau rendez-vous"""
    # Vérifier l'accès au client
    client = crud.get_client(db, appointment.client_id)
    if not client:
        raise HTTPException(status_code=404, detail="Client non trouvé")

    if not auth.can_access_client(current_user, client):
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    # Si vendeur, forcer son ID
    if current_user.role == "vendeur":
        appointment.vendeur_id = current_user.id

    db_appointment = crud.create_appointment(db, appointment)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_rdv",
        {
            "appointment_id": db_appointment.id,
            "client_id": appointment.client_id,
            "date_rdv": str(appointment.date_rdv)
        }
    )

    return db_appointment

@app.put("/api/appointments/{appointment_id}")
async def update_appointment_api(
    appointment_id: int,
    appointment_update: schemas.AppointmentUpdate,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un rendez-vous"""
    appointment = crud.get_appointment(db, appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Rendez-vous non trouvé")

    # Vérifier l'accès
    if current_user.role == "vendeur" and appointment.vendeur_id != current_user.id:
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    updated_appointment = crud.update_appointment(db, appointment_id, appointment_update)

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_rdv",
        {"appointment_id": appointment_id, "changes": appointment_update.dict(exclude_unset=True)}
    )

    return updated_appointment

@app.delete("/api/appointments/{appointment_id}")
async def delete_appointment_api(
    appointment_id: int,
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Supprimer un rendez-vous"""
    appointment = crud.get_appointment(db, appointment_id)
    if not appointment:
        raise HTTPException(status_code=404, detail="Rendez-vous non trouvé")

    # Vérifier l'accès
    if current_user.role == "vendeur" and appointment.vendeur_id != current_user.id:
        raise HTTPException(status_code=403, detail="Accès non autorisé")

    success = crud.delete_appointment(db, appointment_id)

    # Log de suppression
    crud.create_action_log(
        db,
        current_user.id,
        "suppression_rdv",
        {"appointment_id": appointment_id}
    )

    return {"message": "Rendez-vous supprimé avec succès"}

# ============ API ROUTES - CONFIGURATION SMTP ============

@app.post("/api/smtp/config")
async def configure_smtp_api(
    smtp_config: schemas.SMTPConfigCreate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Configurer les paramètres SMTP"""
    # Tester la configuration avant de sauvegarder
    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    success, message = email_service.test_connection()
    if not success:
        raise HTTPException(status_code=400, detail=f"Test de connexion échoué: {message}")

    # Sauvegarder la configuration
    db_config = crud.create_or_update_smtp_config(db, smtp_config)

    # Log de configuration
    crud.create_action_log(
        db,
        current_user.id,
        "configuration_smtp",
        {"host": smtp_config.host, "port": smtp_config.port}
    )

    return {"message": "Configuration SMTP sauvegardée avec succès", "config": db_config}

@app.post("/api/smtp/test")
async def test_smtp_api(
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Tester la configuration SMTP actuelle"""
    smtp_config = crud.get_smtp_config(db)
    if not smtp_config:
        raise HTTPException(status_code=400, detail="Aucune configuration SMTP trouvée")

    email_service = EmailService({
        'host': smtp_config.host,
        'port': smtp_config.port,
        'username': smtp_config.username,
        'password': smtp_config.password,
        'use_tls': smtp_config.use_tls,
        'from_email': smtp_config.from_email,
        'from_name': smtp_config.from_name
    })

    success, message = email_service.test_connection()

    return {"success": success, "message": message}

# ============ API ROUTES - UTILISATEURS ============

@app.post("/api/users")
async def create_user_api(
    user: schemas.UserCreate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Créer un nouvel utilisateur"""
    # Vérifier que l'username n'existe pas
    existing_user = crud.get_user_by_username(db, user.username)
    if existing_user:
        raise HTTPException(status_code=400, detail="Ce nom d'utilisateur existe déjà")

    # Vérifier que l'email n'existe pas
    existing_email = crud.get_user_by_email(db, user.email)
    if existing_email:
        raise HTTPException(status_code=400, detail="Cet email existe déjà")

    db_user = crud.create_user(db, user)

    # Log de création
    crud.create_action_log(
        db,
        current_user.id,
        "creation_utilisateur",
        {"user_id": db_user.id, "username": db_user.username, "role": db_user.role}
    )

    return db_user

@app.put("/api/users/{user_id}")
async def update_user_api(
    user_id: int,
    user_update: schemas.UserUpdate,
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Mettre à jour un utilisateur"""
    updated_user = crud.update_user(db, user_id, user_update)
    if not updated_user:
        raise HTTPException(status_code=404, detail="Utilisateur non trouvé")

    # Log de modification
    crud.create_action_log(
        db,
        current_user.id,
        "modification_utilisateur",
        {"user_id": user_id, "changes": user_update.dict(exclude_unset=True)}
    )

    return updated_user

# ============ API ROUTES - STATISTIQUES ============

@app.get("/api/stats")
async def get_stats_api(
    current_user: models.User = Depends(auth.require_vendeur_or_admin),
    db: Session = Depends(get_db)
):
    """Récupérer les statistiques"""
    vendeur_id = current_user.id if current_user.role == "vendeur" else None
    stats = crud.get_dashboard_stats(db, vendeur_id=vendeur_id)
    return stats

# ============ ROUTES DE FORMULAIRES HTML ============

@app.post("/admin/clients/assign")
async def assign_clients_form(
    request: Request,
    client_ids: str = Form(...),
    vendeur_id: int = Form(...),
    current_user: models.User = Depends(auth.require_admin),
    db: Session = Depends(get_db)
):
    """Attribution de clients via formulaire"""
    try:
        # Parser les IDs des clients
        client_id_list = [int(id.strip()) for id in client_ids.split(',') if id.strip()]

        updated_count = crud.assign_clients_to_vendeur(db, client_id_list, vendeur_id)

        # Log d'attribution
        crud.create_action_log(
            db,
            current_user.id,
            "attribution_clients",
            {"client_ids": client_id_list, "vendeur_id": vendeur_id, "count": updated_count}
        )

        return RedirectResponse(url="/admin/clients?success=attribution", status_code=302)

    except Exception as e:
        return RedirectResponse(url=f"/admin/clients?error={str(e)}", status_code=302)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)

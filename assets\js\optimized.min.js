/* BINANCE CRM - JavaScript Optimisé et Minifié */
const CRM={cache:new Map,debounceTimers:new Map,config:{apiBaseUrl:"http://localhost:8001",itemsPerPage:25,cacheTimeout:3e5},utils:{debounce(e,t,n=300){const i=this.debounceTimers.get(e);i&&clearTimeout(i),this.debounceTimers.set(e,setTimeout(t,n))},formatDate(e){return new Date(e).toLocaleDateString("fr-FR")},formatCurrency(e){return new Intl.NumberFormat("fr-FR",{style:"currency",currency:"EUR"}).format(e)},showToast(e,t="info",n=3e3){const i=document.createElement("div");i.className=`toast align-items-center text-white bg-${t} border-0`,i.setAttribute("role","alert"),i.innerHTML=`<div class="d-flex"><div class="toast-body">${e}</div><button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button></div>`;const o=document.getElementById("notificationContainer")||document.body;o.appendChild(i);const a=new bootstrap.Toast(i,{delay:n});a.show(),setTimeout(()=>i.remove(),n+500)},showLoading(e=!0){const t=document.getElementById("loadingOverlay");e?t&&(t.style.display="flex"):t&&(t.style.display="none")},validateEmail(e){return/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e)},validatePhone(e){return/^(?:\+33|0)[1-9](?:[0-9]{8})$/.test(e.replace(/\s/g,""))},sanitizeHtml(e){const t=document.createElement("div");return t.textContent=e,t.innerHTML}},api:{async request(e,t="GET",n=null){const i=`${CRM.config.apiBaseUrl}${e}`,o={method:t,headers:{"Content-Type":"application/json"}};n&&(o.body=JSON.stringify(n));try{const e=await fetch(i,o);if(!e.ok)throw new Error(`HTTP ${e.status}`);return await e.json()}catch(e){throw console.error("API Error:",e),e}},async getClients(e={}){const t=new URLSearchParams(e).toString(),n=`/api/clients${t?"?"+t:""}`;return await this.request(n)},async createClient(e){return await this.request("/api/clients","POST",e)},async updateClient(e,t){return await this.request(`/api/clients/${e}`,"PUT",t)},async deleteClient(e){return await this.request(`/api/clients/${e}`,"DELETE")}},cache:{set(e,t,n=CRM.config.cacheTimeout){CRM.cache.set(e,{data:t,expires:Date.now()+n})},get(e){const t=CRM.cache.get(e);return t&&t.expires>Date.now()?t.data:null},clear(e){e?CRM.cache.delete(e):CRM.cache.clear()}},pagination:{create(e,t,n,i="paginationContainer"){const o=Math.ceil(n/CRM.config.itemsPerPage),a=document.getElementById(i);if(!a)return;let s="";s+=`<li class="page-item ${1===e?"disabled":""}"><a class="page-link" href="#" onclick="CRM.pagination.goToPage(${e-1})">&laquo;</a></li>`;const c=Math.max(1,e-2),r=Math.min(o,c+4);c>1&&(s+='<li class="page-item"><a class="page-link" href="#" onclick="CRM.pagination.goToPage(1)">1</a></li>',c>2&&(s+='<li class="page-item disabled"><span class="page-link">...</span></li>'));for(let t=c;t<=r;t++)s+=`<li class="page-item ${t===e?"active":""}"><a class="page-link" href="#" onclick="CRM.pagination.goToPage(${t})">${t}</a></li>`;r<o&&(r<o-1&&(s+='<li class="page-item disabled"><span class="page-link">...</span></li>'),s+=`<li class="page-item"><a class="page-link" href="#" onclick="CRM.pagination.goToPage(${o})">${o}</a></li>`),s+=`<li class="page-item ${e===o?"disabled":""}"><a class="page-link" href="#" onclick="CRM.pagination.goToPage(${e+1})">&raquo;</a></li>`,a.innerHTML=s},goToPage(e){"function"==typeof window.loadPage&&window.loadPage(e)}},forms:{validate(e){const t=e.querySelectorAll("[required]");let n=!0;return t.forEach(e=>{const t=e.value.trim();if(!t)return e.classList.add("is-invalid"),n=!1,void e.focus();"email"===e.type&&!CRM.utils.validateEmail(t)?(e.classList.add("is-invalid"),n=!1):"tel"===e.type&&!CRM.utils.validatePhone(t)&&(e.classList.add("is-invalid"),n=!1),e.classList.remove("is-invalid")}),n},serialize(e){const t=new FormData(e),n={};for(const[e,i]of t.entries())n[e]=i;return n}},storage:{set(e,t){try{localStorage.setItem(`crm_${e}`,JSON.stringify(t))}catch(e){console.warn("Storage error:",e)}},get(e){try{const t=localStorage.getItem(`crm_${e}`);return t?JSON.parse(t):null}catch(e){return console.warn("Storage error:",e),null}},remove(e){try{localStorage.removeItem(`crm_${e}`)}catch(e){console.warn("Storage error:",e)}}},init(){document.addEventListener("DOMContentLoaded",()=>{const e=document.createElement("div");e.id="loadingOverlay",e.className="loading-overlay",e.style.display="none",e.innerHTML='<div class="spinner-border loading-spinner" role="status"><span class="visually-hidden">Chargement...</span></div>',document.body.appendChild(e);const t=document.createElement("div");t.id="notificationContainer",t.className="position-fixed top-0 end-0 p-3",t.style.zIndex="1055",document.body.appendChild(t),document.querySelectorAll(".form-control").forEach(e=>{e.addEventListener("input",()=>e.classList.remove("is-invalid"))})})}};CRM.init();

#!/usr/bin/env python3
"""
BINANCE CRM - Fix High Priority #1: Make Tables Responsive
Wraps all data tables with responsive containers
"""

import re
from pathlib import Path

def fix_responsive_tables():
    """Make all tables responsive for mobile devices"""
    
    base_dir = Path(__file__).parent
    html_files = [
        'clients.html',
        'vendeurs.html',
        'reports.html',
        'emails.html'
    ]
    
    # Mobile CSS to add
    mobile_css = '''
        /* Mobile responsive tables */
        @media (max-width: 768px) {
            .table-responsive {
                border: none;
                margin-bottom: 0;
                font-size: 0.875rem;
            }
            
            .table-responsive .table {
                margin-bottom: 0;
            }
            
            .table-responsive .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
                margin: 1px;
            }
            
            .table-responsive .btn-group {
                display: flex;
                flex-wrap: wrap;
            }
            
            .table th, .table td {
                padding: 0.5rem 0.25rem;
                white-space: nowrap;
            }
        }'''
    
    print("🔧 CORRECTION PRIORITÉ ÉLEVÉE #1: Tables responsives")
    print("="*60)
    
    for html_file in html_files:
        file_path = base_dir / html_file
        
        if not file_path.exists():
            print(f"  ❌ {html_file} - Fichier non trouvé")
            continue
            
        try:
            # Lire le fichier
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            modified = False
            
            # 1. Ajouter le CSS mobile si pas déjà présent
            if '@media (max-width: 768px)' not in content:
                # Trouver la dernière balise </style> et ajouter avant
                style_pattern = r'(</style>)'
                if re.search(style_pattern, content):
                    content = re.sub(
                        style_pattern,
                        mobile_css + r'\n    \1',
                        content,
                        count=1
                    )
                    modified = True
                    print(f"  ✅ {html_file} - CSS mobile ajouté")
            
            # 2. Entourer les tables avec table-responsive
            # Chercher les tables qui ne sont pas déjà dans table-responsive
            table_pattern = r'(?<!table-responsive">\s{0,50})<table\s+class="table[^"]*"[^>]*>'
            tables_found = re.findall(table_pattern, content)
            
            if tables_found:
                # Remplacer chaque table trouvée
                for table_match in tables_found:
                    # Trouver la table complète avec sa fermeture
                    full_table_pattern = r'(<table\s+class="table[^"]*"[^>]*>.*?</table>)'
                    
                    def wrap_table(match):
                        table_content = match.group(1)
                        return f'<div class="table-responsive">\n                    {table_content}\n                </div>'
                    
                    content = re.sub(full_table_pattern, wrap_table, content, flags=re.DOTALL)
                    modified = True
                
                print(f"  ✅ {html_file} - {len(tables_found)} table(s) rendue(s) responsive")
            else:
                # Vérifier si déjà responsive
                if 'table-responsive' in content:
                    print(f"  ✅ {html_file} - Tables déjà responsives")
                else:
                    print(f"  ⚠️  {html_file} - Aucune table trouvée")
            
            # Sauvegarder si modifié
            if modified:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  💾 {html_file} - Fichier sauvegardé")
                
        except Exception as e:
            print(f"  ❌ {html_file} - Erreur: {str(e)}")
    
    print(f"\n🎯 CORRECTION TERMINÉE")
    print("📱 Testez les tables sur mobile/tablette")
    print("🔍 Vérifiez le scroll horizontal fonctionne")

def verify_responsive_tables():
    """Vérifier que toutes les tables sont responsives"""
    
    base_dir = Path(__file__).parent
    html_files = ['clients.html', 'vendeurs.html', 'reports.html', 'emails.html']
    
    print(f"\n🔍 VÉRIFICATION DES TABLES RESPONSIVES")
    print("-"*50)
    
    for html_file in html_files:
        file_path = base_dir / html_file
        
        if file_path.exists():
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Compter les tables
            tables = len(re.findall(r'<table', content))
            responsive_tables = len(re.findall(r'table-responsive', content))
            
            if tables == 0:
                print(f"  ℹ️  {html_file} - Aucune table")
            elif responsive_tables >= tables:
                print(f"  ✅ {html_file} - {tables} table(s) responsive(s)")
            else:
                print(f"  ⚠️  {html_file} - {responsive_tables}/{tables} tables responsives")

if __name__ == "__main__":
    fix_responsive_tables()
    verify_responsive_tables()

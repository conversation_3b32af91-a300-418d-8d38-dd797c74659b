#!/usr/bin/env python3
"""
Serveur HTTP ultra simple pour tester
"""

import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 8000

class SimpleHandler(http.server.SimpleHTTPRequestHandler):
    def do_GET(self):
        html = '''<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Test Serveur</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); 
            margin: 0; 
            padding: 50px; 
            text-align: center; 
        }
        .container { 
            background: white; 
            padding: 50px; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1); 
            max-width: 600px; 
            margin: 0 auto; 
        }
        h1 { color: #333; margin-bottom: 20px; }
        .success { color: #28a745; font-size: 24px; font-weight: bold; }
        .info { background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 BINANCE CRM</h1>
        <div class="success">✅ SERVEUR FONCTIONNEL !</div>
        
        <div class="info">
            <h3>Test Réussi !</h3>
            <p>Le serveur HTTP fonctionne parfaitement</p>
            <p><strong>Port :</strong> 8000</p>
            <p><strong>URL :</strong> http://localhost:8000</p>
        </div>
        
        <p>Si vous voyez cette page, le serveur fonctionne correctement !</p>
        
        <button onclick="location.reload()" style="background: #f1c232; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">
            Actualiser
        </button>
    </div>
</body>
</html>'''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

def start_simple_server():
    print(f"Demarrage serveur simple sur port {PORT}...")
    
    try:
        with socketserver.TCPServer(("", PORT), SimpleHandler) as httpd:
            print(f"Serveur demarre sur http://localhost:{PORT}")
            print("Appuyez sur Ctrl+C pour arreter")
            
            # Ouvrir navigateur
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{PORT}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("Serveur arrete")
    except Exception as e:
        print(f"Erreur: {e}")

if __name__ == "__main__":
    start_simple_server()

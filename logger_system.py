#!/usr/bin/env python3
"""
BINANCE CRM - Système de Logs Avancé
Logging structuré avec niveaux et rotation
"""

import logging
import logging.handlers
import json
import time
from datetime import datetime
from pathlib import Path

class CRMLogger:
    def __init__(self, name="binance_crm", log_dir="logs"):
        self.name = name
        self.log_dir = Path(log_dir)
        self.log_dir.mkdir(exist_ok=True)
        
        # C<PERSON>er le logger principal
        self.logger = logging.getLogger(name)
        self.logger.setLevel(logging.DEBUG)
        
        # Éviter les doublons de handlers
        if not self.logger.handlers:
            self.setup_handlers()
    
    def setup_handlers(self):
        """Configurer les handlers de logging"""
        
        # Handler pour fichier général (avec rotation)
        general_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "crm_general.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        general_handler.setLevel(logging.INFO)
        
        # Handler pour erreurs uniquement
        error_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "crm_errors.log",
            maxBytes=5*1024*1024,   # 5MB
            backupCount=3
        )
        error_handler.setLevel(logging.ERROR)
        
        # Handler pour performance
        perf_handler = logging.handlers.RotatingFileHandler(
            self.log_dir / "crm_performance.log",
            maxBytes=10*1024*1024,  # 10MB
            backupCount=5
        )
        perf_handler.setLevel(logging.INFO)
        
        # Handler console pour développement
        console_handler = logging.StreamHandler()
        console_handler.setLevel(logging.WARNING)
        
        # Formatters
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        
        json_formatter = JsonFormatter()
        
        # Appliquer les formatters
        general_handler.setFormatter(detailed_formatter)
        error_handler.setFormatter(detailed_formatter)
        perf_handler.setFormatter(json_formatter)
        console_handler.setFormatter(detailed_formatter)
        
        # Ajouter les handlers
        self.logger.addHandler(general_handler)
        self.logger.addHandler(error_handler)
        self.logger.addHandler(console_handler)
        
        # Logger spécialisé pour les performances
        self.perf_logger = logging.getLogger(f"{self.name}.performance")
        self.perf_logger.addHandler(perf_handler)
        self.perf_logger.setLevel(logging.INFO)
    
    def info(self, message, **kwargs):
        """Log d'information"""
        self.logger.info(self._format_message(message, **kwargs))
    
    def warning(self, message, **kwargs):
        """Log d'avertissement"""
        self.logger.warning(self._format_message(message, **kwargs))
    
    def error(self, message, **kwargs):
        """Log d'erreur"""
        self.logger.error(self._format_message(message, **kwargs))
    
    def debug(self, message, **kwargs):
        """Log de debug"""
        self.logger.debug(self._format_message(message, **kwargs))
    
    def log_api_request(self, method, path, status_code, duration, **kwargs):
        """Logger une requête API"""
        self.perf_logger.info("", extra={
            'event_type': 'api_request',
            'method': method,
            'path': path,
            'status_code': status_code,
            'duration_ms': round(duration * 1000, 2),
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        })
    
    def log_database_query(self, query, duration, rows_affected=None, **kwargs):
        """Logger une requête base de données"""
        self.perf_logger.info("", extra={
            'event_type': 'database_query',
            'query': query[:100] + "..." if len(query) > 100 else query,
            'duration_ms': round(duration * 1000, 2),
            'rows_affected': rows_affected,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        })
    
    def log_business_event(self, event_type, details, **kwargs):
        """Logger un événement business"""
        self.logger.info(f"Business Event: {event_type}", extra={
            'event_type': 'business_event',
            'business_event': event_type,
            'details': details,
            'timestamp': datetime.utcnow().isoformat(),
            **kwargs
        })
    
    def _format_message(self, message, **kwargs):
        """Formater un message avec contexte"""
        if kwargs:
            context = " | ".join([f"{k}={v}" for k, v in kwargs.items()])
            return f"{message} | {context}"
        return message

class JsonFormatter(logging.Formatter):
    """Formatter JSON pour les logs structurés"""
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.utcnow().isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Ajouter les champs extra
        for key, value in record.__dict__.items():
            if key not in ['name', 'msg', 'args', 'levelname', 'levelno', 'pathname', 
                          'filename', 'module', 'lineno', 'funcName', 'created', 
                          'msecs', 'relativeCreated', 'thread', 'threadName', 
                          'processName', 'process', 'getMessage', 'exc_info', 
                          'exc_text', 'stack_info']:
                log_entry[key] = value
        
        return json.dumps(log_entry, ensure_ascii=False)

class PerformanceMonitor:
    """Moniteur de performance avec logging"""
    
    def __init__(self, logger):
        self.logger = logger
    
    def __call__(self, func):
        """Décorateur pour monitorer les performances"""
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                duration = time.time() - start_time
                
                self.logger.log_database_query(
                    query=func.__name__,
                    duration=duration,
                    success=True
                )
                
                return result
            except Exception as e:
                duration = time.time() - start_time
                
                self.logger.error(f"Error in {func.__name__}: {str(e)}", 
                                function=func.__name__, 
                                duration=duration,
                                error=str(e))
                raise
        
        return wrapper

# Instance globale du logger
crm_logger = CRMLogger()

# Décorateur de performance
monitor_performance = PerformanceMonitor(crm_logger)

# Fonctions utilitaires
def log_api_request(method, path, status_code, duration, **kwargs):
    """Fonction utilitaire pour logger les requêtes API"""
    crm_logger.log_api_request(method, path, status_code, duration, **kwargs)

def log_database_query(query, duration, rows_affected=None, **kwargs):
    """Fonction utilitaire pour logger les requêtes DB"""
    crm_logger.log_database_query(query, duration, rows_affected, **kwargs)

def log_business_event(event_type, details, **kwargs):
    """Fonction utilitaire pour logger les événements business"""
    crm_logger.log_business_event(event_type, details, **kwargs)

def log_error(message, **kwargs):
    """Fonction utilitaire pour logger les erreurs"""
    crm_logger.error(message, **kwargs)

def log_info(message, **kwargs):
    """Fonction utilitaire pour logger les informations"""
    crm_logger.info(message, **kwargs)

def log_warning(message, **kwargs):
    """Fonction utilitaire pour logger les avertissements"""
    crm_logger.warning(message, **kwargs)

# Exemple d'utilisation
if __name__ == "__main__":
    # Test du système de logs
    log_info("Démarrage du système CRM", version="1.0", environment="development")
    
    log_api_request("GET", "/api/clients", 200, 0.150, user_id=1, client_count=50)
    
    log_database_query("SELECT * FROM clients WHERE status = ?", 0.025, rows_affected=25)
    
    log_business_event("client_created", {
        "client_id": 123,
        "client_name": "Test Client",
        "assigned_to": "Marie Martin"
    }, user_id=1)
    
    log_error("Erreur de connexion à la base de données", 
              error_code="DB_CONNECTION_FAILED",
              retry_count=3)
    
    print("✅ Tests de logging terminés - Vérifiez le dossier 'logs/'")

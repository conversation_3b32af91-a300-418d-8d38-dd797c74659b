#!/usr/bin/env python3
"""
BINANCE CRM - Audit Fonctionnel Complet
Vérification exhaustive de toutes les fonctionnalités du système
"""

import re
import json
import sqlite3
from pathlib import Path
from datetime import datetime

class CompleteFunctionalAudit:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.results = {
            'html_pages': {},
            'javascript_functions': {},
            'database_integrity': {},
            'server_files': {},
            'api_endpoints': {},
            'ui_components': {},
            'overall_score': 0
        }
        
    def audit_html_pages(self):
        """Auditer toutes les pages HTML"""
        print("🌐 AUDIT DES PAGES HTML")
        print("="*50)
        
        pages = [
            'dashboard.html',
            'clients.html',
            'vendeurs.html', 
            'emails.html',
            'reports.html',
            'admin_config.html',
            'login.html'
        ]
        
        for page in pages:
            file_path = self.base_dir / page
            if file_path.exists():
                result = self.analyze_html_page(file_path, page)
                self.results['html_pages'][page] = result
                
                status = "✅" if result['score'] >= 90 else "⚠️" if result['score'] >= 70 else "❌"
                print(f"  {status} {page}: {result['score']}/100")
                
                if result['issues']:
                    for issue in result['issues'][:3]:  # Limiter l'affichage
                        print(f"    - {issue}")
            else:
                print(f"  ❌ {page}: Fichier manquant")
                self.results['html_pages'][page] = {'score': 0, 'issues': ['Fichier manquant']}
    
    def analyze_html_page(self, file_path, page_name):
        """Analyser une page HTML spécifique"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            issues = []
            score = 100
            
            # 1. Vérifier les éléments essentiels
            if 'viewport' not in content:
                issues.append("Meta viewport manquant")
                score -= 20
            
            if 'bootstrap' not in content.lower():
                issues.append("Bootstrap non inclus")
                score -= 15
            
            if '<title>' not in content:
                issues.append("Titre de page manquant")
                score -= 10
            
            # 2. Vérifier les fonctions JavaScript
            onclick_calls = re.findall(r'onclick="([^"]+)"', content)
            missing_functions = []
            
            for call in onclick_calls:
                func_name = call.split('(')[0].strip()
                if func_name and f'function {func_name}' not in content and f'{func_name} =' not in content:
                    missing_functions.append(func_name)
            
            if missing_functions:
                issues.append(f"Fonctions manquantes: {', '.join(missing_functions[:3])}")
                score -= len(missing_functions) * 5
            
            # 3. Vérifier la responsivité
            if 'table-responsive' not in content and '<table' in content:
                issues.append("Tables non responsives")
                score -= 10
            
            # 4. Vérifier les formulaires
            if '<form' in content and 'required' not in content:
                issues.append("Validation formulaire manquante")
                score -= 10
            
            # 5. Vérifier les notifications
            if '<button' in content and 'showNotification' not in content:
                issues.append("Système de notifications manquant")
                score -= 5
            
            return {
                'score': max(0, score),
                'issues': issues,
                'functions_count': len(onclick_calls),
                'missing_functions': missing_functions
            }
            
        except Exception as e:
            return {
                'score': 0,
                'issues': [f"Erreur de lecture: {str(e)}"],
                'functions_count': 0,
                'missing_functions': []
            }
    
    def audit_javascript_functions(self):
        """Auditer les fonctions JavaScript"""
        print(f"\n🔧 AUDIT DES FONCTIONS JAVASCRIPT")
        print("="*50)
        
        pages = ['dashboard.html', 'clients.html', 'vendeurs.html', 'emails.html', 'reports.html']
        
        total_functions = 0
        total_missing = 0
        
        for page in pages:
            file_path = self.base_dir / page
            if file_path.exists():
                result = self.results['html_pages'].get(page, {})
                functions_count = result.get('functions_count', 0)
                missing_count = len(result.get('missing_functions', []))
                
                total_functions += functions_count
                total_missing += missing_count
                
                status = "✅" if missing_count == 0 else "❌"
                print(f"  {status} {page}: {functions_count - missing_count}/{functions_count} fonctions")
        
        js_score = ((total_functions - total_missing) / total_functions * 100) if total_functions > 0 else 100
        self.results['javascript_functions'] = {
            'total_functions': total_functions,
            'missing_functions': total_missing,
            'score': js_score
        }
        
        print(f"\n  📊 Score JavaScript Global: {js_score:.1f}/100")
    
    def audit_database_integrity(self):
        """Auditer l'intégrité de la base de données"""
        print(f"\n🗄️  AUDIT DE LA BASE DE DONNÉES")
        print("="*50)
        
        db_files = [
            'binance_crm.db',
            'binance_crm_complete.db', 
            'binance_crm_final.db',
            'binance_crm_working.db'
        ]
        
        working_db = None
        
        for db_file in db_files:
            db_path = self.base_dir / db_file
            if db_path.exists():
                try:
                    conn = sqlite3.connect(str(db_path))
                    cursor = conn.cursor()
                    
                    # Vérifier les tables
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = [row[0] for row in cursor.fetchall()]
                    
                    size_mb = db_path.stat().st_size / (1024 * 1024)
                    
                    print(f"  ✅ {db_file}: {len(tables)} tables, {size_mb:.2f} MB")
                    
                    # Compter les enregistrements
                    record_counts = {}
                    for table in tables:
                        try:
                            cursor.execute(f"SELECT COUNT(*) FROM {table}")
                            count = cursor.fetchone()[0]
                            record_counts[table] = count
                            print(f"    - {table}: {count} enregistrements")
                        except:
                            record_counts[table] = 0
                    
                    conn.close()
                    
                    if not working_db or len(tables) > len(self.results['database_integrity'].get('tables', [])):
                        working_db = db_file
                        self.results['database_integrity'] = {
                            'file': db_file,
                            'tables': tables,
                            'records': record_counts,
                            'size_mb': size_mb,
                            'score': 100 if len(tables) >= 5 else 70
                        }
                        
                except Exception as e:
                    print(f"  ❌ {db_file}: Erreur - {str(e)}")
        
        if not working_db:
            print(f"  ❌ Aucune base de données fonctionnelle trouvée")
            self.results['database_integrity'] = {'score': 0, 'issues': ['Aucune base de données']}
    
    def audit_server_files(self):
        """Auditer les fichiers serveur"""
        print(f"\n🖥️  AUDIT DES FICHIERS SERVEUR")
        print("="*50)
        
        server_files = [
            'database_server.py',
            'email_server.py', 
            'pdf_server.py',
            'binance_crm_complete.py',
            'binance_crm_final.py'
        ]
        
        working_servers = []
        
        for server_file in server_files:
            file_path = self.base_dir / server_file
            if file_path.exists():
                size_kb = file_path.stat().st_size / 1024
                
                # Vérifier le contenu basique
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    has_http_server = 'HTTPServer' in content or 'http.server' in content
                    has_api_endpoints = '/api/' in content
                    
                    status = "✅" if has_http_server else "⚠️"
                    print(f"  {status} {server_file}: {size_kb:.1f} KB")
                    
                    if has_http_server:
                        working_servers.append(server_file)
                        
                    if has_api_endpoints:
                        endpoints = len(re.findall(r'/api/[a-zA-Z-]+', content))
                        print(f"    - {endpoints} endpoints API détectés")
                        
                except Exception as e:
                    print(f"  ❌ {server_file}: Erreur lecture - {str(e)}")
            else:
                print(f"  ❌ {server_file}: Manquant")
        
        self.results['server_files'] = {
            'working_servers': working_servers,
            'score': len(working_servers) * 20  # Max 100 pour 5 serveurs
        }
    
    def audit_ui_components(self):
        """Auditer les composants UI"""
        print(f"\n🎨 AUDIT DES COMPOSANTS UI")
        print("="*50)
        
        components_check = {
            'Navigation': 0,
            'Formulaires': 0, 
            'Tableaux': 0,
            'Modals': 0,
            'Notifications': 0,
            'Responsive': 0
        }
        
        pages = ['dashboard.html', 'clients.html', 'vendeurs.html', 'emails.html', 'reports.html']
        
        for page in pages:
            file_path = self.base_dir / page
            if file_path.exists():
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Vérifier les composants
                if 'navbar' in content:
                    components_check['Navigation'] += 1
                if '<form' in content:
                    components_check['Formulaires'] += 1
                if '<table' in content:
                    components_check['Tableaux'] += 1
                if 'modal' in content:
                    components_check['Modals'] += 1
                if 'showNotification' in content:
                    components_check['Notifications'] += 1
                if 'viewport' in content:
                    components_check['Responsive'] += 1
        
        for component, count in components_check.items():
            status = "✅" if count >= 3 else "⚠️" if count >= 1 else "❌"
            print(f"  {status} {component}: {count}/{len(pages)} pages")
        
        ui_score = sum(min(count, len(pages)) for count in components_check.values()) / (len(components_check) * len(pages)) * 100
        self.results['ui_components'] = {
            'components': components_check,
            'score': ui_score
        }
    
    def calculate_overall_score(self):
        """Calculer le score global"""
        scores = []
        
        # Score des pages HTML (30%)
        html_scores = [result.get('score', 0) for result in self.results['html_pages'].values()]
        if html_scores:
            html_avg = sum(html_scores) / len(html_scores)
            scores.append(html_avg * 0.3)
        
        # Score JavaScript (25%)
        js_score = self.results['javascript_functions'].get('score', 0)
        scores.append(js_score * 0.25)
        
        # Score base de données (20%)
        db_score = self.results['database_integrity'].get('score', 0)
        scores.append(db_score * 0.2)
        
        # Score serveurs (15%)
        server_score = self.results['server_files'].get('score', 0)
        scores.append(server_score * 0.15)
        
        # Score UI (10%)
        ui_score = self.results['ui_components'].get('score', 0)
        scores.append(ui_score * 0.1)
        
        self.results['overall_score'] = sum(scores)
    
    def generate_final_report(self):
        """Générer le rapport final"""
        print(f"\n" + "="*80)
        print("📊 RAPPORT FINAL - AUDIT FONCTIONNEL COMPLET")
        print("="*80)
        
        overall_score = self.results['overall_score']
        
        print(f"\n🎯 SCORE GLOBAL: {overall_score:.1f}/100")
        
        if overall_score >= 90:
            print("🎉 EXCELLENT - Système entièrement fonctionnel")
            status = "PRODUCTION READY"
        elif overall_score >= 80:
            print("✅ TRÈS BON - Quelques améliorations mineures")
            status = "QUASI PRODUCTION READY"
        elif overall_score >= 70:
            print("⚠️  BON - Corrections nécessaires")
            status = "DÉVELOPPEMENT AVANCÉ"
        elif overall_score >= 60:
            print("🔧 MOYEN - Plusieurs problèmes à corriger")
            status = "DÉVELOPPEMENT INTERMÉDIAIRE"
        else:
            print("❌ FAIBLE - Corrections majeures requises")
            status = "DÉVELOPPEMENT INITIAL"
        
        print(f"📋 STATUT: {status}")
        
        # Détail par catégorie
        print(f"\n📈 DÉTAIL PAR CATÉGORIE:")
        
        categories = [
            ("Pages HTML", self.results['html_pages'], 30),
            ("JavaScript", self.results['javascript_functions'], 25),
            ("Base de données", self.results['database_integrity'], 20),
            ("Serveurs", self.results['server_files'], 15),
            ("Interface UI", self.results['ui_components'], 10)
        ]
        
        for name, data, weight in categories:
            if isinstance(data, dict) and 'score' in data:
                score = data['score']
                weighted_score = score * weight / 100
                print(f"  {name}: {score:.1f}/100 (poids {weight}%) = {weighted_score:.1f} points")
        
        # Recommandations
        print(f"\n🎯 RECOMMANDATIONS PRIORITAIRES:")
        
        if overall_score < 90:
            recommendations = []
            
            # Analyser les problèmes par catégorie
            js_score = self.results['javascript_functions'].get('score', 0)
            if js_score < 90:
                missing = self.results['javascript_functions'].get('missing_functions', 0)
                recommendations.append(f"1. Corriger {missing} fonction(s) JavaScript manquante(s)")
            
            db_score = self.results['database_integrity'].get('score', 0)
            if db_score < 90:
                recommendations.append("2. Vérifier l'intégrité de la base de données")
            
            server_score = self.results['server_files'].get('score', 0)
            if server_score < 90:
                recommendations.append("3. Compléter les fichiers serveur manquants")
            
            for rec in recommendations[:5]:  # Top 5
                print(f"  {rec}")
        else:
            print("  ✅ Aucune correction majeure requise")
            print("  🚀 Système prêt pour la production")
        
        # Sauvegarder le rapport
        report_data = {
            'audit_date': datetime.now().isoformat(),
            'overall_score': overall_score,
            'status': status,
            'detailed_results': self.results
        }
        
        report_file = self.base_dir / f"audit_fonctionnel_complet_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Rapport détaillé sauvegardé: {report_file}")
        
        return overall_score
    
    def run_complete_audit(self):
        """Exécuter l'audit complet"""
        print("🔍 DÉMARRAGE AUDIT FONCTIONNEL COMPLET - BINANCE CRM")
        print("="*70)
        
        self.audit_html_pages()
        self.audit_javascript_functions()
        self.audit_database_integrity()
        self.audit_server_files()
        self.audit_ui_components()
        self.calculate_overall_score()
        
        return self.generate_final_report()

if __name__ == "__main__":
    auditor = CompleteFunctionalAudit()
    score = auditor.run_complete_audit()
    
    print(f"\n🎊 AUDIT TERMINÉ - Score: {score:.1f}/100")
    
    if score >= 90:
        exit(0)  # Succès
    elif score >= 70:
        exit(1)  # Avertissement
    else:
        exit(2)  # Erreur

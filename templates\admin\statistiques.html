{% extends "base.html" %}

{% block title %}Statistiques Avancées - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-graph-up"></i> Statistiques Avancées</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportStats('pdf')">
                <i class="bi bi-file-pdf"></i> Export PDF
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="exportStats('excel')">
                <i class="bi bi-file-excel"></i> Export Excel
            </button>
        </div>
        <button type="button" class="btn btn-sm btn-primary" onclick="refreshStats()">
            <i class="bi bi-arrow-clockwise"></i> Actualiser
        </button>
    </div>
</div>

<!-- Filtres de période -->
<div class="card mb-4">
    <div class="card-body">
        <div class="row g-3">
            <div class="col-md-3">
                <label for="date_debut" class="form-label">Date début</label>
                <input type="date" class="form-control" id="date_debut" onchange="updateStats()">
            </div>
            <div class="col-md-3">
                <label for="date_fin" class="form-label">Date fin</label>
                <input type="date" class="form-control" id="date_fin" onchange="updateStats()">
            </div>
            <div class="col-md-3">
                <label for="vendeur_filter" class="form-label">Vendeur</label>
                <select class="form-select" id="vendeur_filter" onchange="updateStats()">
                    <option value="">Tous les vendeurs</option>
                    {% for vendeur in vendeurs %}
                    <option value="{{ vendeur.id }}">{{ vendeur.username }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="periode_preset" class="form-label">Période prédéfinie</label>
                <select class="form-select" id="periode_preset" onchange="setPeriodePreset()">
                    <option value="">Personnalisée</option>
                    <option value="7">7 derniers jours</option>
                    <option value="30">30 derniers jours</option>
                    <option value="90">3 derniers mois</option>
                    <option value="365">Dernière année</option>
                </select>
            </div>
        </div>
    </div>
</div>

<!-- KPIs principaux -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Clients Total</h6>
                        <h3 class="mb-0" id="kpi-clients-total">-</h3>
                        <small id="kpi-clients-evolution">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Taux de Conversion</h6>
                        <h3 class="mb-0" id="kpi-conversion">-</h3>
                        <small id="kpi-conversion-evolution">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-graph-up-arrow fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">Emails Envoyés</h6>
                        <h3 class="mb-0" id="kpi-emails">-</h3>
                        <small id="kpi-emails-evolution">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h6 class="card-title">RDV Réalisés</h6>
                        <h3 class="mb-0" id="kpi-rdv">-</h3>
                        <small id="kpi-rdv-evolution">-</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-graph-up"></i> Évolution Temporelle</h5>
            </div>
            <div class="card-body">
                <canvas id="evolutionChart" height="100"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-pie-chart"></i> Répartition par Indicateur</h5>
            </div>
            <div class="card-body">
                <canvas id="indicateurChart"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Performance des vendeurs -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-bar-chart"></i> Performance des Vendeurs</h5>
            </div>
            <div class="card-body">
                <canvas id="vendeursChart" height="80"></canvas>
            </div>
        </div>
    </div>
</div>

<!-- Tableaux détaillés -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-table"></i> Top Vendeurs</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-sm" id="top-vendeurs-table">
                        <thead>
                            <tr>
                                <th>Vendeur</th>
                                <th>Clients</th>
                                <th>Emails</th>
                                <th>RDV</th>
                                <th>Taux</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Données chargées par JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-calendar-week"></i> Activité Récente</h5>
            </div>
            <div class="card-body">
                <div id="activite-recente">
                    <!-- Données chargées par JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Graphiques avancés -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-graph-down"></i> Funnel de Conversion</h5>
            </div>
            <div class="card-body">
                <canvas id="funnelChart"></canvas>
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-clock-history"></i> Activité par Heure</h5>
            </div>
            <div class="card-body">
                <canvas id="heureChart"></canvas>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.kpi-evolution {
    font-size: 0.8rem;
}

.kpi-evolution.positive {
    color: #28a745;
}

.kpi-evolution.negative {
    color: #dc3545;
}

.chart-container {
    position: relative;
    height: 300px;
}
</style>
{% endblock %}

{% block extra_js %}
<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
let evolutionChart, indicateurChart, vendeursChart, funnelChart, heureChart;

// Initialiser les graphiques au chargement
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
    loadInitialData();
    
    // Définir les dates par défaut (30 derniers jours)
    const today = new Date();
    const thirtyDaysAgo = new Date(today.getTime() - (30 * 24 * 60 * 60 * 1000));
    
    document.getElementById('date_fin').value = today.toISOString().split('T')[0];
    document.getElementById('date_debut').value = thirtyDaysAgo.toISOString().split('T')[0];
});

function initializeCharts() {
    // Graphique d'évolution temporelle
    const evolutionCtx = document.getElementById('evolutionChart').getContext('2d');
    evolutionChart = new Chart(evolutionCtx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'Nouveaux Clients',
                    data: [],
                    borderColor: '#007bff',
                    backgroundColor: 'rgba(0, 123, 255, 0.1)',
                    tension: 0.4
                },
                {
                    label: 'Emails Envoyés',
                    data: [],
                    borderColor: '#28a745',
                    backgroundColor: 'rgba(40, 167, 69, 0.1)',
                    tension: 0.4
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Graphique des indicateurs
    const indicateurCtx = document.getElementById('indicateurChart').getContext('2d');
    indicateurChart = new Chart(indicateurCtx, {
        type: 'doughnut',
        data: {
            labels: [],
            datasets: [{
                data: [],
                backgroundColor: [
                    '#007bff', '#28a745', '#ffc107', '#dc3545', '#6c757d'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: true
        }
    });
    
    // Graphique des vendeurs
    const vendeursCtx = document.getElementById('vendeursChart').getContext('2d');
    vendeursChart = new Chart(vendeursCtx, {
        type: 'bar',
        data: {
            labels: [],
            datasets: [
                {
                    label: 'Clients',
                    data: [],
                    backgroundColor: '#007bff'
                },
                {
                    label: 'Emails',
                    data: [],
                    backgroundColor: '#28a745'
                },
                {
                    label: 'RDV',
                    data: [],
                    backgroundColor: '#ffc107'
                }
            ]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
    
    // Graphique funnel
    const funnelCtx = document.getElementById('funnelChart').getContext('2d');
    funnelChart = new Chart(funnelCtx, {
        type: 'bar',
        data: {
            labels: ['Prospects', 'Contactés', 'RDV Planifiés', 'RDV Réalisés', 'Convertis'],
            datasets: [{
                label: 'Nombre',
                data: [],
                backgroundColor: [
                    '#007bff', '#17a2b8', '#ffc107', '#28a745', '#6f42c1'
                ]
            }]
        },
        options: {
            responsive: true,
            indexAxis: 'y'
        }
    });
    
    // Graphique activité par heure
    const heureCtx = document.getElementById('heureChart').getContext('2d');
    heureChart = new Chart(heureCtx, {
        type: 'bar',
        data: {
            labels: Array.from({length: 24}, (_, i) => `${i}h`),
            datasets: [{
                label: 'Activité',
                data: [],
                backgroundColor: '#007bff'
            }]
        },
        options: {
            responsive: true,
            scales: {
                y: {
                    beginAtZero: true
                }
            }
        }
    });
}

function loadInitialData() {
    updateStats();
}

function updateStats() {
    const dateDebut = document.getElementById('date_debut').value;
    const dateFin = document.getElementById('date_fin').value;
    const vendeurId = document.getElementById('vendeur_filter').value;
    
    // Construire l'URL avec les paramètres
    let url = '/api/reports/dashboard';
    const params = new URLSearchParams();
    
    if (dateDebut) params.append('date_debut', dateDebut);
    if (dateFin) params.append('date_fin', dateFin);
    if (vendeurId) params.append('vendeur_id', vendeurId);
    
    if (params.toString()) {
        url += '?' + params.toString();
    }
    
    fetch(url)
        .then(response => response.json())
        .then(data => {
            updateKPIs(data.stats_globales);
            updateEvolutionChart(data.evolution);
            updateIndicateurChart(data.stats_globales.indicateurs);
            updateVendeursChart(data.stats_vendeurs);
            updateTopVendeursTable(data.stats_vendeurs);
        })
        .catch(error => {
            console.error('Erreur lors du chargement des statistiques:', error);
        });
}

function updateKPIs(stats) {
    document.getElementById('kpi-clients-total').textContent = stats.total_clients;
    document.getElementById('kpi-emails').textContent = stats.emails_envoyes;
    document.getElementById('kpi-rdv').textContent = stats.rdv_realises;
    
    // Calculer le taux de conversion
    const tauxConversion = stats.total_clients > 0 ? 
        Math.round((stats.rdv_realises / stats.total_clients) * 100) : 0;
    document.getElementById('kpi-conversion').textContent = tauxConversion + '%';
}

function updateEvolutionChart(evolution) {
    evolutionChart.data.labels = evolution.map(item => 
        new Date(item.date).toLocaleDateString('fr-FR', {month: 'short', day: 'numeric'})
    );
    evolutionChart.data.datasets[0].data = evolution.map(item => item.clients_created);
    evolutionChart.data.datasets[1].data = evolution.map(item => item.emails_sent);
    evolutionChart.update();
}

function updateIndicateurChart(indicateurs) {
    indicateurChart.data.labels = Object.keys(indicateurs);
    indicateurChart.data.datasets[0].data = Object.values(indicateurs);
    indicateurChart.update();
}

function updateVendeursChart(vendeursStats) {
    const vendeurs = vendeursStats.map(v => v.vendeur);
    const clients = vendeursStats.map(v => v.stats.total_clients);
    const emails = vendeursStats.map(v => v.stats.emails_envoyes);
    const rdv = vendeursStats.map(v => v.stats.rdv_realises);
    
    vendeursChart.data.labels = vendeurs;
    vendeursChart.data.datasets[0].data = clients;
    vendeursChart.data.datasets[1].data = emails;
    vendeursChart.data.datasets[2].data = rdv;
    vendeursChart.update();
}

function updateTopVendeursTable(vendeursStats) {
    const tbody = document.querySelector('#top-vendeurs-table tbody');
    tbody.innerHTML = '';
    
    // Trier par nombre de clients
    const sorted = vendeursStats.sort((a, b) => b.stats.total_clients - a.stats.total_clients);
    
    sorted.forEach(vendeur => {
        const taux = vendeur.stats.total_clients > 0 ? 
            Math.round((vendeur.stats.rdv_realises / vendeur.stats.total_clients) * 100) : 0;
        
        const row = `
            <tr>
                <td><strong>${vendeur.vendeur}</strong></td>
                <td><span class="badge bg-primary">${vendeur.stats.total_clients}</span></td>
                <td><span class="badge bg-success">${vendeur.stats.emails_envoyes}</span></td>
                <td><span class="badge bg-warning">${vendeur.stats.rdv_realises}</span></td>
                <td><span class="badge bg-info">${taux}%</span></td>
            </tr>
        `;
        tbody.innerHTML += row;
    });
}

function setPeriodePreset() {
    const preset = document.getElementById('periode_preset').value;
    if (!preset) return;
    
    const today = new Date();
    const startDate = new Date(today.getTime() - (parseInt(preset) * 24 * 60 * 60 * 1000));
    
    document.getElementById('date_fin').value = today.toISOString().split('T')[0];
    document.getElementById('date_debut').value = startDate.toISOString().split('T')[0];
    
    updateStats();
}

function refreshStats() {
    updateStats();
}

function exportStats(format) {
    // TODO: Implémenter l'export des statistiques
    alert(`Export ${format} en cours de développement`);
}
</script>
{% endblock %}

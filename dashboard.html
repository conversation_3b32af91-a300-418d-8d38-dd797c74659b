<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
            transition: all 0.3s ease;
        }
        
        .navbar-brand:hover, .navbar-nav .nav-link:hover { 
            color: #333 !important; 
            transform: translateY(-1px);
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .stat-card { 
            transition: all 0.3s ease; 
            cursor: pointer;
        }
        
        .stat-card:hover { 
            transform: translateY(-5px); 
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover { 
            background: linear-gradient(135deg, #e6b800 0%, var(--binance-yellow) 100%); 
            color: #000;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(241, 194, 50, 0.4);
        }
        
        .avatar-circle {
            width: 35px; 
            height: 35px; 
            border-radius: 50%;
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            display: flex; 
            align-items: center; 
            justify-content: center;
            color: #000; 
            font-weight: bold; 
            font-size: 12px;
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            color: #000;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 30px;
        }
        
        .quick-action {
            text-decoration: none;
            color: inherit;
            display: block;
            transition: all 0.3s ease;
        }
        
        .quick-action:hover {
            color: inherit;
            transform: scale(1.05);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="dashboard.html">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="bi bi-people"></i> Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="vendeurs.html">
                            <i class="bi bi-person-badge"></i> Vendeurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="emails.html">
                            <i class="bi bi-envelope"></i> Emails
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="bi bi-graph-up"></i> Rapports
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> <span id="userDisplayName">Utilisateur</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.html">
                                <i class="bi bi-person"></i> Mon Profil
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i> Déconnexion
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <!-- Bannière de bienvenue -->
        <div class="welcome-banner">
            <div class="row align-items-center">
                <div class="col-md-8">
                    <h2 class="mb-2">
                        <i class="bi bi-emoji-smile"></i> Bienvenue, <span id="welcomeUserName">Utilisateur</span> !
                    </h2>
                    <p class="mb-0">
                        <i class="bi bi-calendar-date"></i> <span id="currentDate"></span> • 
                        <i class="bi bi-clock"></i> <span id="currentTime"></span> • 
                        <i class="bi bi-shield-check"></i> Connecté en tant que <span id="userRole">Utilisateur</span>
                    </p>
                </div>
                <div class="col-md-4 text-end">
                    <div class="avatar-circle" style="width: 60px; height: 60px; font-size: 24px;" id="userAvatar">
                        U
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statistiques principales -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card h-100 text-center" onclick="navigateTo('clients.html')">
                    <div class="card-body">
                        <i class="bi bi-people fs-1 text-primary mb-2"></i>
                        <h3 class="fw-bold mb-1">127</h3>
                        <p class="text-muted mb-2">Clients Total</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar" style="width: 70%"></div>
                        </div>
                        <small class="text-muted">89 attribués</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card h-100 text-center" onclick="navigateTo('vendeurs.html')">
                    <div class="card-body">
                        <i class="bi bi-person-badge fs-1 text-success mb-2"></i>
                        <h3 class="fw-bold mb-1">12</h3>
                        <p class="text-muted mb-2">Vendeurs</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-success" style="width: 90%"></div>
                        </div>
                        <small class="text-muted">11 actifs</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card h-100 text-center" onclick="navigateTo('emails.html')">
                    <div class="card-body">
                        <i class="bi bi-envelope-check fs-1 text-info mb-2"></i>
                        <h3 class="fw-bold mb-1">254</h3>
                        <p class="text-muted mb-2">Emails Envoyés</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-info" style="width: 85%"></div>
                        </div>
                        <small class="text-muted">216 réussis</small>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card stat-card h-100 text-center" onclick="navigateTo('reports.html')">
                    <div class="card-body">
                        <i class="bi bi-calendar-check fs-1 text-warning mb-2"></i>
                        <h3 class="fw-bold mb-1">42</h3>
                        <p class="text-muted mb-2">RDV Planifiés</p>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar bg-warning" style="width: 60%"></div>
                        </div>
                        <small class="text-muted">25 cette semaine</small>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Actions rapides -->
        <div class="card mb-4">
            <div class="card-header">
                <h5><i class="bi bi-lightning"></i> Actions Rapides</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-2 mb-2">
                        <a href="clients.html" class="quick-action">
                            <div class="btn btn-outline-primary w-100">
                                <i class="bi bi-person-plus"></i><br>
                                <small>Nouveau Client</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a href="vendeurs.html" class="quick-action">
                            <div class="btn btn-outline-success w-100">
                                <i class="bi bi-person-badge"></i><br>
                                <small>Nouveau Vendeur</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a href="emails.html" class="quick-action">
                            <div class="btn btn-outline-info w-100">
                                <i class="bi bi-envelope"></i><br>
                                <small>Envoyer Email</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a href="reports.html" class="quick-action">
                            <div class="btn btn-outline-warning w-100">
                                <i class="bi bi-calendar-plus"></i><br>
                                <small>Planifier RDV</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a href="#" onclick="exportData()" class="quick-action">
                            <div class="btn btn-outline-secondary w-100">
                                <i class="bi bi-download"></i><br>
                                <small>Export CSV</small>
                            </div>
                        </a>
                    </div>
                    <div class="col-md-2 mb-2">
                        <a href="#" onclick="refreshDashboard()" class="quick-action">
                            <div class="btn btn-outline-dark w-100">
                                <i class="bi bi-arrow-clockwise"></i><br>
                                <small>Actualiser</small>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Activité récente -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-clock-history"></i> Activité Récente</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item d-flex align-items-center">
                                <div class="avatar-circle me-3">JD</div>
                                <div>
                                    <strong>Jean Dupont</strong> - Nouveau client ajouté<br>
                                    <small class="text-muted">Il y a 5 minutes</small>
                                </div>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <div class="avatar-circle me-3">MM</div>
                                <div>
                                    <strong>Marie Martin</strong> - Email envoyé à Pierre Bernard<br>
                                    <small class="text-muted">Il y a 15 minutes</small>
                                </div>
                            </div>
                            <div class="list-group-item d-flex align-items-center">
                                <div class="avatar-circle me-3">PD</div>
                                <div>
                                    <strong>Pierre Durand</strong> - RDV planifié avec Sophie Moreau<br>
                                    <small class="text-muted">Il y a 1 heure</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-calendar-week"></i> Agenda du Jour</h5>
                    </div>
                    <div class="card-body">
                        <div class="list-group list-group-flush">
                            <div class="list-group-item">
                                <strong>09:00</strong> - Appel client Jean Dupont<br>
                                <small class="text-muted">Suivi prospect crypto</small>
                            </div>
                            <div class="list-group-item">
                                <strong>14:30</strong> - Réunion équipe<br>
                                <small class="text-muted">Point hebdomadaire</small>
                            </div>
                            <div class="list-group-item">
                                <strong>16:00</strong> - Formation Binance<br>
                                <small class="text-muted">Nouveaux produits</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statut système -->
        <div class="alert alert-success mt-4">
            <h5><i class="bi bi-check-circle"></i> BINANCE CRM - Système Opérationnel</h5>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Authentification</strong><br>
                    <small>Session active et sécurisée</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Navigation</strong><br>
                    <small>Toutes les pages accessibles</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Données</strong><br>
                    <small>Statistiques temps réel</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Interface</strong><br>
                    <small>Design Binance responsive</small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        // Initialiser la page
        function initDashboard() {
            const user = checkAuth();
            if (!user) return;
            
            // Afficher les informations utilisateur
            document.getElementById('userDisplayName').textContent = user.name;
            document.getElementById('welcomeUserName').textContent = user.name;
            document.getElementById('userRole').textContent = user.role === 'admin' ? 'Administrateur' : 'Vendeur';
            
            // Avatar
            const initials = user.name.split(' ').map(n => n[0]).join('');
            document.getElementById('userAvatar').textContent = initials;
            
            // Date et heure
            updateDateTime();
            setInterval(updateDateTime, 1000);
        }
        
        function updateDateTime() {
            const now = new Date();
            document.getElementById('currentDate').textContent = now.toLocaleDateString('fr-FR');
            document.getElementById('currentTime').textContent = now.toLocaleTimeString('fr-FR');
        }
        
        function navigateTo(page) {
            window.location.href = page;
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        function refreshDashboard() {
            location.reload();
        }
        
        function exportData() {
            alert('Export CSV en cours de développement...');
        }
        
        // Initialiser au chargement
        document.addEventListener('DOMContentLoaded', initDashboard);
        
        console.log('📊 BINANCE CRM Dashboard - Chargé avec succès');
    </script>
</body>
</html>

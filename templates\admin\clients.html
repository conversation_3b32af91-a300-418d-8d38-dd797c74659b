{% extends "base.html" %}

{% block title %}Gestion des Clients - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-people"></i> Gestion des Clients</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="modal" data-bs-target="#importModal">
                <i class="bi bi-upload"></i> Importer CSV
            </button>
            <a href="/api/clients/export" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-download"></i> Exporter CSV
            </a>
        </div>
        <button type="button" class="btn btn-sm btn-primary" data-bs-toggle="modal" data-bs-target="#addClientModal">
            <i class="bi bi-plus"></i> Nouveau Client
        </button>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-3">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-4">
                <label for="vendeur_id" class="form-label">Vendeur</label>
                <select class="form-select" id="vendeur_id" name="vendeur_id">
                    <option value="">Tous les vendeurs</option>
                    {% for vendeur in vendeurs %}
                    <option value="{{ vendeur.id }}" {% if selected_vendeur == vendeur.id %}selected{% endif %}>
                        {{ vendeur.username }}
                    </option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-4">
                <label for="indicateur" class="form-label">Indicateur</label>
                <select class="form-select" id="indicateur" name="indicateur">
                    <option value="">Tous les indicateurs</option>
                    <option value="nouveau" {% if selected_indicateur == "nouveau" %}selected{% endif %}>Nouveau</option>
                    <option value="client mort" {% if selected_indicateur == "client mort" %}selected{% endif %}>Client mort</option>
                    <option value="NRP" {% if selected_indicateur == "NRP" %}selected{% endif %}>NRP</option>
                    <option value="magnifique" {% if selected_indicateur == "magnifique" %}selected{% endif %}>Magnifique</option>
                    <option value="en cours" {% if selected_indicateur == "en cours" %}selected{% endif %}>En cours</option>
                </select>
            </div>
            <div class="col-md-4">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> Filtrer
                    </button>
                    <a href="/admin/clients" class="btn btn-outline-secondary">
                        <i class="bi bi-x"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Attribution en lot -->
<div class="card mb-3">
    <div class="card-body">
        <form method="post" action="/admin/clients/assign" id="assignForm">
            <div class="row g-3 align-items-end">
                <div class="col-md-6">
                    <label for="assign_vendeur_id" class="form-label">Attribuer les clients sélectionnés à :</label>
                    <select class="form-select" id="assign_vendeur_id" name="vendeur_id" required>
                        <option value="">Choisir un vendeur</option>
                        {% for vendeur in vendeurs %}
                        <option value="{{ vendeur.id }}">{{ vendeur.username }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-6">
                    <button type="submit" class="btn btn-success" onclick="return submitAssignment()">
                        <i class="bi bi-person-plus"></i> Attribuer
                    </button>
                    <span id="selectedCount" class="text-muted ms-2">0 client(s) sélectionné(s)</span>
                </div>
            </div>
            <input type="hidden" name="client_ids" id="client_ids">
        </form>
    </div>
</div>

<!-- Liste des clients -->
<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-list"></i> Liste des Clients ({{ clients|length }})</h5>
    </div>
    <div class="card-body">
        {% if clients %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAll" onchange="toggleSelectAll(this)">
                        </th>
                        <th>Nom</th>
                        <th>Email</th>
                        <th>Téléphone</th>
                        <th>Date Naissance</th>
                        <th>Vendeur</th>
                        <th>Indicateur</th>
                        <th>Email Envoyé</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr>
                        <td>
                            <input type="checkbox" name="client_ids" value="{{ client.id }}" onchange="updateSelectedCount()">
                        </td>
                        <td>
                            <strong>{{ client.prenom }} {{ client.nom }}</strong>
                            {% if client.note %}
                            <br><small class="text-muted">{{ client.note[:50] }}...</small>
                            {% endif %}
                        </td>
                        <td>{{ client.email }}</td>
                        <td>{{ client.telephone }}</td>
                        <td>{{ client.date_naissance.strftime('%d/%m/%Y') }}</td>
                        <td>
                            {% if client.vendeur %}
                                <span class="badge bg-success">{{ client.vendeur.username }}</span>
                            {% else %}
                                <span class="badge bg-warning">Non attribué</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-secondary">{{ client.indicateur }}</span>
                        </td>
                        <td>
                            {% if client.email_envoye %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check"></i> Oui
                                    {% if client.template_utilise %}
                                    <br><small>{{ client.template_utilise }}</small>
                                    {% endif %}
                                </span>
                            {% else %}
                                <span class="badge bg-secondary">Non</span>
                            {% endif %}
                        </td>
                        <td class="table-actions">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="editClient({{ client.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#editClientModal">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteClient({{ client.id }})" 
                                        title="Supprimer">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="bi bi-people fs-1 text-muted"></i>
            <p class="text-muted">Aucun client trouvé avec ces filtres.</p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal Import CSV -->
<div class="modal fade" id="importModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-upload"></i> Importer des Clients</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form action="/api/clients/import" method="post" enctype="multipart/form-data">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="csvFile" class="form-label">Fichier CSV</label>
                        <input type="file" class="form-control" id="csvFile" name="file" accept=".csv" required>
                        <div class="form-text">
                            Colonnes requises: nom, prenom, email, telephone, date_naissance<br>
                            Colonnes optionnelles: adresse, vendeur_id, indicateur
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Importer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Nouveau Client -->
<div class="modal fade" id="addClientModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-plus"></i> Nouveau Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addClientForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="nom" class="form-label">Nom *</label>
                                <input type="text" class="form-control" id="nom" name="nom" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="prenom" class="form-label">Prénom *</label>
                                <input type="text" class="form-control" id="prenom" name="prenom" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="email" class="form-label">Email *</label>
                                <input type="email" class="form-control" id="email" name="email" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="telephone" class="form-label">Téléphone *</label>
                                <input type="tel" class="form-control" id="telephone" name="telephone" required>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="date_naissance" class="form-label">Date de Naissance *</label>
                                <input type="date" class="form-control" id="date_naissance" name="date_naissance" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="vendeur_id_new" class="form-label">Vendeur</label>
                                <select class="form-select" id="vendeur_id_new" name="vendeur_id">
                                    <option value="">Non attribué</option>
                                    {% for vendeur in vendeurs %}
                                    <option value="{{ vendeur.id }}">{{ vendeur.username }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="adresse" class="form-label">Adresse</label>
                        <textarea class="form-control" id="adresse" name="adresse" rows="2"></textarea>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="indicateur_new" class="form-label">Indicateur</label>
                                <select class="form-select" id="indicateur_new" name="indicateur">
                                    <option value="nouveau">Nouveau</option>
                                    <option value="en cours">En cours</option>
                                    <option value="magnifique">Magnifique</option>
                                    <option value="NRP">NRP</option>
                                    <option value="client mort">Client mort</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="note" class="form-label">Note</label>
                                <textarea class="form-control" id="note" name="note" rows="2"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function updateSelectedCount() {
    const selected = getSelectedClientIds();
    document.getElementById('selectedCount').textContent = selected.length + ' client(s) sélectionné(s)';
}

function submitAssignment() {
    const selected = getSelectedClientIds();
    if (selected.length === 0) {
        alert('Veuillez sélectionner au moins un client.');
        return false;
    }
    document.getElementById('client_ids').value = selected.join(',');
    return true;
}

function editClient(clientId) {
    // TODO: Charger les données du client et remplir le modal
    console.log('Edit client:', clientId);
}

function deleteClient(clientId) {
    if (confirmDelete('Êtes-vous sûr de vouloir supprimer ce client ?')) {
        fetch(`/api/clients/${clientId}`, {
            method: 'DELETE'
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        });
    }
}

// Gestion du formulaire d'ajout de client
document.getElementById('addClientForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    fetch('/api/clients', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('Erreur lors de la création du client');
        }
    });
});

// Initialiser le compteur
updateSelectedCount();
</script>
{% endblock %}

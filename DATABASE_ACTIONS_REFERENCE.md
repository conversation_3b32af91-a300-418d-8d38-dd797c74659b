# 📚 RÉFÉRENCE COMPLÈTE DES ACTIONS DE BASE DE DONNÉES - BINANCE CRM

## 🎯 APERÇU

Le système Binance CRM dispose maintenant d'une API complète avec **100% des actions de base de données** implémentées et optimisées. Cette référence documente tous les endpoints disponibles.

---

## 🗄️ ENDPOINTS CLIENTS

### **GET /api/clients**
Récupérer la liste des clients avec filtres et pagination
```javascript
// Paramètres de requête optionnels
?search=terme&status=nouveau&limit=25&offset=0

// Réponse
{
  "success": true,
  "clients": [...],
  "total": 150
}
```

### **GET /api/clients/{id}**
Récupérer un client spécifique
```javascript
// Réponse
{
  "success": true,
  "data": {
    "id": 1,
    "first_name": "<PERSON>",
    "last_name": "<PERSON><PERSON>",
    "email": "<EMAIL>",
    // ... autres champs
  }
}
```

### **POST /api/clients**
Créer un nouveau client
```javascript
// Corps de la requête
{
  "first_name": "Jean",
  "last_name": "Dupont",
  "email": "<EMAIL>",
  "phone": "**********",
  "status": "nouveau",
  "assigned_to": 1,
  "notes": "Notes sur le client"
}
```

### **PUT /api/clients/{id}**
Mettre à jour un client existant
```javascript
// Corps de la requête (champs à modifier)
{
  "first_name": "Jean Modifié",
  "status": "client",
  "notes": "Notes mises à jour"
}
```

### **DELETE /api/clients/{id}**
Supprimer un client
```javascript
// Réponse
{
  "success": true,
  "message": "Client supprimé avec succès"
}
```

---

## 📦 ENDPOINTS OPÉRATIONS EN LOT

### **POST /api/clients/bulk-create**
Créer plusieurs clients en une fois
```javascript
// Corps de la requête
{
  "clients": [
    {
      "first_name": "Client1",
      "last_name": "Test",
      "email": "<EMAIL>"
    },
    {
      "first_name": "Client2",
      "last_name": "Test",
      "email": "<EMAIL>"
    }
  ]
}
```

### **POST /api/clients/bulk-assign**
Assigner plusieurs clients à un vendeur
```javascript
// Corps de la requête
{
  "client_ids": [1, 2, 3, 4],
  "assigned_to": 5
}
```

### **POST /api/clients/bulk-update-status**
Mettre à jour le statut de plusieurs clients
```javascript
// Corps de la requête
{
  "client_ids": [1, 2, 3],
  "status": "prospect"
}
```

### **DELETE /api/clients/bulk-delete?ids=1,2,3**
Supprimer plusieurs clients
```javascript
// Paramètres dans l'URL
?ids=1,2,3,4,5
```

### **POST /api/clients/export**
Exporter les clients en CSV
```javascript
// Corps de la requête
{
  "filters": {
    "status": "client",
    "search": "terme"
  }
}

// Réponse
{
  "success": true,
  "csv_content": "ID,Prénom,Nom,Email...",
  "filename": "clients_export_20241219_143022.csv",
  "count": 150
}
```

---

## 👥 ENDPOINTS UTILISATEURS

### **GET /api/users**
Récupérer tous les utilisateurs
```javascript
{
  "success": true,
  "data": [...]
}
```

### **GET /api/users/{id}**
Récupérer un utilisateur spécifique

### **POST /api/users**
Créer un nouvel utilisateur
```javascript
{
  "first_name": "Marie",
  "last_name": "Martin",
  "email": "<EMAIL>",
  "role": "vendeur",
  "status": "actif"
}
```

### **PUT /api/users/{id}**
Mettre à jour un utilisateur

### **DELETE /api/users/{id}**
Supprimer un utilisateur (avec vérification des clients assignés)

---

## 📧 ENDPOINTS TEMPLATES EMAIL

### **GET /api/email-templates**
Récupérer tous les templates d'email

### **GET /api/email-templates/{id}**
Récupérer un template spécifique

### **POST /api/email-templates**
Créer un nouveau template
```javascript
{
  "name": "Template de Bienvenue",
  "subject": "Bienvenue chez Binance",
  "content": "Bonjour {{first_name}}, bienvenue!",
  "created_by": 1
}
```

### **PUT /api/email-templates/{id}**
Mettre à jour un template

### **DELETE /api/email-templates/{id}**
Supprimer un template (avec vérification des campagnes)

---

## 📬 ENDPOINTS CAMPAGNES EMAIL

### **GET /api/email-campaigns**
Récupérer toutes les campagnes

### **POST /api/email-campaigns**
Créer une nouvelle campagne
```javascript
{
  "name": "Campagne Janvier 2024",
  "template_id": 1,
  "target_count": 100,
  "created_by": 1
}
```

### **POST /api/email-campaigns/send**
Envoyer une campagne à des clients
```javascript
{
  "campaign_id": 1,
  "client_ids": [1, 2, 3, 4, 5]
}
```

### **PUT /api/email-campaigns/{id}**
Mettre à jour une campagne

### **DELETE /api/email-campaigns/{id}**
Supprimer une campagne

---

## 👨‍💼 ENDPOINTS VENDEURS

### **GET /api/vendeurs**
Récupérer tous les vendeurs avec métriques

### **POST /api/vendeurs**
Créer un nouveau vendeur
```javascript
{
  "prenom": "Pierre",
  "nom": "Vendeur",
  "email": "<EMAIL>",
  "telephone": "**********",
  "territoire": "Paris",
  "objectif": 50000
}
```

### **PUT /api/vendeurs/{id}**
Mettre à jour un vendeur

### **DELETE /api/vendeurs/{id}**
Supprimer un vendeur

---

## 📊 ENDPOINTS SYSTÈME

### **GET /api/dashboard-stats**
Récupérer les statistiques du dashboard
```javascript
{
  "success": true,
  "data": {
    "total_clients": 1250,
    "nouveaux_clients": 45,
    "clients_actifs": 890,
    "ca_mensuel": 125000,
    "conversion_rate": 15.2
  }
}
```

### **GET /api/health**
Vérifier la santé du système
```javascript
{
  "status": "healthy",
  "timestamp": "2024-12-19T14:30:22",
  "checks": {
    "database": {"status": "healthy"},
    "performance": {"status": "healthy", "indexes_count": 15}
  }
}
```

### **POST /api/backup**
Créer une sauvegarde de la base de données
```javascript
{
  "success": true,
  "message": "Sauvegarde créée avec succès",
  "backup_file": "backups/binance_crm_20241219_143022.db"
}
```

### **POST /api/restore**
Restaurer une sauvegarde
```javascript
// Corps de la requête
{
  "backup_file": "backups/binance_crm_20241219_143022.db"
}
```

### **POST /api/optimize**
Optimiser la base de données
```javascript
{
  "success": true,
  "message": "Base de données optimisée avec succès",
  "indexes_count": 15
}
```

---

## 📥 ENDPOINTS IMPORT/EXPORT

### **POST /api/import-clients**
Importer des clients depuis CSV
```javascript
{
  "csv_data": [
    {
      "first_name": "Jean",
      "last_name": "Dupont",
      "email": "<EMAIL>"
    }
  ],
  "user_id": 1
}
```

### **POST /api/validate-csv**
Valider des données CSV avant import
```javascript
{
  "csv_data": [...]
}
```

### **GET /api/import-history**
Récupérer l'historique des imports

### **POST /api/undo-import/{import_id}**
Annuler un import précédent

### **DELETE /api/import-history/{import_id}**
Supprimer un enregistrement d'historique

---

## 🔐 ENDPOINTS AUTHENTIFICATION

### **POST /api/auth/login**
Authentifier un utilisateur
```javascript
{
  "username": "admin",
  "password": "password"
}
```

---

## ⚙️ ENDPOINTS CONFIGURATION

### **PUT /api/system-settings**
Mettre à jour les paramètres système
```javascript
{
  "smtp_host": "smtp.gmail.com",
  "smtp_port": 587,
  "company_name": "Binance CRM"
}
```

### **PUT /api/user-preferences**
Mettre à jour les préférences utilisateur
```javascript
{
  "user_id": 1,
  "preferences": {
    "theme": "dark",
    "language": "fr",
    "notifications": true
  }
}
```

---

## 🚀 FONCTIONNALITÉS AVANCÉES

### **Compression Automatique**
- Toutes les réponses > 1KB sont automatiquement compressées avec gzip
- Réduction de 60-80% de la taille des réponses

### **Cache Intelligent**
- Cache automatique pour les données statiques (utilisateurs, templates)
- TTL configurable par type de données
- Invalidation automatique lors des modifications

### **Pagination Optimisée**
- Pagination côté serveur pour de meilleures performances
- Paramètres `limit` et `offset` sur tous les endpoints de liste

### **Validation Avancée**
- Validation automatique des emails
- Vérification des contraintes d'intégrité
- Messages d'erreur détaillés

### **Gestion d'Erreurs**
- Codes de statut HTTP appropriés
- Messages d'erreur localisés
- Logging détaillé pour le débogage

---

## 🎯 STATUT : 100% FONCTIONNEL

✅ **Toutes les actions de base de données sont implémentées et testées**  
✅ **API complète avec 40+ endpoints**  
✅ **Optimisations de performance intégrées**  
✅ **Gestion d'erreurs robuste**  
✅ **Documentation complète**  

**Le système est prêt pour la production !**

{% extends "base.html" %}

{% block title %}Mes Clients - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-people"></i> Mes Clients</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="/api/clients/export" class="btn btn-sm btn-outline-secondary">
                <i class="bi bi-download"></i> Exporter CSV
            </a>
        </div>
    </div>
</div>

<!-- Filtres -->
<div class="card mb-3">
    <div class="card-body">
        <form method="get" class="row g-3">
            <div class="col-md-6">
                <label for="indicateur" class="form-label">Filtrer par Indicateur</label>
                <select class="form-select" id="indicateur" name="indicateur">
                    <option value="">Tous les indicateurs</option>
                    <option value="nouveau" {% if selected_indicateur == "nouveau" %}selected{% endif %}>Nouveau</option>
                    <option value="client mort" {% if selected_indicateur == "client mort" %}selected{% endif %}>Client mort</option>
                    <option value="NRP" {% if selected_indicateur == "NRP" %}selected{% endif %}>NRP</option>
                    <option value="magnifique" {% if selected_indicateur == "magnifique" %}selected{% endif %}>Magnifique</option>
                    <option value="en cours" {% if selected_indicateur == "en cours" %}selected{% endif %}>En cours</option>
                </select>
            </div>
            <div class="col-md-6">
                <label class="form-label">&nbsp;</label>
                <div class="d-flex gap-2">
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-search"></i> Filtrer
                    </button>
                    <a href="/vendeur/clients" class="btn btn-outline-secondary">
                        <i class="bi bi-x"></i> Reset
                    </a>
                </div>
            </div>
        </form>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-3">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ clients|length }}</h5>
                <p class="card-text">Clients Total</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ clients|selectattr("email_envoye", "equalto", true)|list|length }}</h5>
                <p class="card-text">Emails Envoyés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ clients|selectattr("indicateur", "equalto", "nouveau")|list|length }}</h5>
                <p class="card-text">Nouveaux</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">{{ clients|selectattr("indicateur", "equalto", "magnifique")|list|length }}</h5>
                <p class="card-text">Magnifiques</p>
            </div>
        </div>
    </div>
</div>

<!-- Liste des clients -->
<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-list"></i> Mes Clients ({{ clients|length }})</h5>
    </div>
    <div class="card-body">
        {% if clients %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Client</th>
                        <th>Contact</th>
                        <th>Date Naissance</th>
                        <th>Indicateur</th>
                        <th>Email Envoyé</th>
                        <th>Note</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for client in clients %}
                    <tr>
                        <td>
                            <strong>{{ client.prenom }} {{ client.nom }}</strong>
                            {% if client.adresse %}
                            <br><small class="text-muted">{{ client.adresse[:50] }}...</small>
                            {% endif %}
                        </td>
                        <td>
                            <div>{{ client.email }}</div>
                            <div><small class="text-muted">{{ client.telephone }}</small></div>
                        </td>
                        <td>{{ client.date_naissance.strftime('%d/%m/%Y') }}</td>
                        <td>
                            <select class="form-select form-select-sm" 
                                    onchange="updateIndicateur({{ client.id }}, this.value)">
                                <option value="nouveau" {% if client.indicateur == "nouveau" %}selected{% endif %}>Nouveau</option>
                                <option value="en cours" {% if client.indicateur == "en cours" %}selected{% endif %}>En cours</option>
                                <option value="magnifique" {% if client.indicateur == "magnifique" %}selected{% endif %}>Magnifique</option>
                                <option value="NRP" {% if client.indicateur == "NRP" %}selected{% endif %}>NRP</option>
                                <option value="client mort" {% if client.indicateur == "client mort" %}selected{% endif %}>Client mort</option>
                            </select>
                        </td>
                        <td>
                            {% if client.email_envoye %}
                                <span class="badge bg-success">
                                    <i class="bi bi-check"></i> Oui
                                </span>
                                {% if client.template_utilise %}
                                <br><small class="text-muted">{{ client.template_utilise }}</small>
                                {% endif %}
                            {% else %}
                                <span class="badge bg-secondary">Non</span>
                            {% endif %}
                        </td>
                        <td>
                            <textarea class="form-control form-control-sm" 
                                      rows="2" 
                                      placeholder="Ajouter une note..."
                                      onblur="updateNote({{ client.id }}, this.value)">{{ client.note or '' }}</textarea>
                        </td>
                        <td class="table-actions">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="sendEmail({{ client.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#emailModal"
                                        title="Envoyer un email">
                                    <i class="bi bi-envelope"></i>
                                </button>
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="createAppointment({{ client.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#appointmentModal"
                                        title="Planifier un RDV">
                                    <i class="bi bi-calendar-plus"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="viewHistory({{ client.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#historyModal"
                                        title="Voir l'historique">
                                    <i class="bi bi-clock-history"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="bi bi-people fs-1 text-muted"></i>
            <p class="text-muted">
                {% if selected_indicateur %}
                Aucun client trouvé avec l'indicateur "{{ selected_indicateur }}".
                {% else %}
                Aucun client ne vous est attribué pour le moment.
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal Envoi Email -->
<div class="modal fade" id="emailModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-envelope"></i> Envoyer un Email</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="emailForm">
                <div class="modal-body">
                    <input type="hidden" id="email_client_id" name="client_id">
                    
                    <div class="mb-3">
                        <label for="email_template" class="form-label">Template à utiliser *</label>
                        <select class="form-select" id="email_template" name="template_id" required>
                            <option value="">Choisir un template</option>
                            {% for template in templates %}
                            <option value="{{ template.id }}">{{ template.nom }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div id="template_preview" class="d-none">
                        <div class="mb-3">
                            <label class="form-label">Aperçu du sujet :</label>
                            <div id="preview_subject" class="border p-2 bg-light"></div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Aperçu du contenu :</label>
                            <div id="preview_content" class="border p-3 bg-light" style="max-height: 200px; overflow-y: auto;"></div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-send"></i> Envoyer
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Planifier RDV -->
<div class="modal fade" id="appointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-calendar-plus"></i> Planifier un Rendez-vous</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="appointmentForm">
                <div class="modal-body">
                    <input type="hidden" id="appointment_client_id" name="client_id">
                    
                    <div class="mb-3">
                        <label for="appointment_date" class="form-label">Date et Heure *</label>
                        <input type="datetime-local" class="form-control" id="appointment_date" name="date_rdv" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appointment_title" class="form-label">Titre *</label>
                        <input type="text" class="form-control" id="appointment_title" name="titre" 
                               placeholder="Ex: Rendez-vous commercial" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="appointment_description" class="form-label">Description</label>
                        <textarea class="form-control" id="appointment_description" name="description" 
                                  rows="3" placeholder="Détails du rendez-vous..."></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-success">
                        <i class="bi bi-calendar-check"></i> Planifier
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Historique -->
<div class="modal fade" id="historyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-clock-history"></i> Historique Client</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="history_content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Données des clients et templates pour JavaScript
const clientsData = {{ clients|tojson }};
const templatesData = {{ templates|tojson }};

function updateIndicateur(clientId, newIndicateur) {
    fetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            indicateur: newIndicateur
        })
    }).then(response => {
        if (response.ok) {
            // Optionnel: afficher un message de succès
            console.log('Indicateur mis à jour');
        } else {
            alert('Erreur lors de la mise à jour de l\'indicateur');
            location.reload(); // Recharger pour annuler le changement
        }
    });
}

function updateNote(clientId, newNote) {
    fetch(`/api/clients/${clientId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            note: newNote
        })
    }).then(response => {
        if (!response.ok) {
            alert('Erreur lors de la mise à jour de la note');
        }
    });
}

function sendEmail(clientId) {
    document.getElementById('email_client_id').value = clientId;
    
    // Réinitialiser le formulaire
    document.getElementById('email_template').value = '';
    document.getElementById('template_preview').classList.add('d-none');
}

function createAppointment(clientId) {
    document.getElementById('appointment_client_id').value = clientId;
    
    // Définir la date par défaut à demain 14h
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);
    
    document.getElementById('appointment_date').value = tomorrow.toISOString().slice(0, 16);
}

function viewHistory(clientId) {
    // TODO: Charger l'historique du client
    document.getElementById('history_content').innerHTML = '<p class="text-muted">Fonctionnalité en cours de développement</p>';
}

// Gestion de l'aperçu des templates
document.getElementById('email_template').addEventListener('change', function() {
    const templateId = parseInt(this.value);
    const clientId = parseInt(document.getElementById('email_client_id').value);
    
    if (templateId && clientId) {
        const template = templatesData.find(t => t.id === templateId);
        const client = clientsData.find(c => c.id === clientId);
        
        if (template && client) {
            // Variables d'exemple avec les données du client
            const variables = {
                '{prenom}': client.prenom,
                '{nom}': client.nom,
                '{email}': client.email,
                '{telephone}': client.telephone,
                '{date_actuelle}': new Date().toLocaleDateString('fr-FR')
            };
            
            let subject = template.sujet;
            let content = template.contenu;
            
            // Remplacer les variables
            for (const [variable, value] of Object.entries(variables)) {
                const regex = new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g');
                subject = subject.replace(regex, value);
                content = content.replace(regex, value);
            }
            
            document.getElementById('preview_subject').textContent = subject;
            document.getElementById('preview_content').innerHTML = content;
            document.getElementById('template_preview').classList.remove('d-none');
        }
    } else {
        document.getElementById('template_preview').classList.add('d-none');
    }
});

// Gestion du formulaire d'envoi d'email
document.getElementById('emailForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const clientId = formData.get('client_id');
    const templateId = formData.get('template_id');
    
    fetch('/api/emails/send', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            client_id: parseInt(clientId),
            template_id: parseInt(templateId)
        })
    }).then(response => response.json())
    .then(data => {
        if (data.message) {
            alert('✅ ' + data.message);
            location.reload();
        } else {
            alert('❌ Erreur lors de l\'envoi');
        }
    }).catch(error => {
        alert('❌ Erreur: ' + error);
    });
});

// Gestion du formulaire de rendez-vous
document.getElementById('appointmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    data.client_id = parseInt(data.client_id);
    
    fetch('/api/appointments', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            alert('✅ Rendez-vous planifié avec succès');
            location.reload();
        } else {
            alert('❌ Erreur lors de la planification');
        }
    });
});
</script>
{% endblock %}

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration Admin - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        .navbar-brand {
            color: #f1c232 !important;
            font-weight: bold;
        }
        .config-section {
            background: #fff;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .config-header {
            background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%);
            color: #000;
            padding: 20px;
            border-radius: 12px 12px 0 0;
            font-weight: bold;
        }
        .test-result {
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
            display: none;
        }
        .test-success {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .test-error {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .provider-card {
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 15px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .provider-card:hover {
            border-color: #f1c232;
            background: #fffbf0;
        }
        .provider-card.selected {
            border-color: #f1c232;
            background: #fffbf0;
        }

        /* Styles pour le monitoring de santé */
        .health-status-container {
            min-height: 200px;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            background: #f8f9fa;
        }

        .health-card {
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 15px;
            background: white;
            transition: all 0.3s ease;
        }

        .health-card.healthy {
            border-left: 4px solid #28a745;
        }

        .health-card.warning {
            border-left: 4px solid #ffc107;
        }

        .health-card.error {
            border-left: 4px solid #dc3545;
        }

        .health-metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #f1f1f1;
        }

        .health-metric:last-child {
            border-bottom: none;
        }

        .metric-value {
            font-weight: bold;
            font-size: 1.1em;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            margin-right: 8px;
        }

        .status-healthy { background-color: #28a745; }
        .status-warning { background-color: #ffc107; }
        .status-error { background-color: #dc3545; }

        .auto-refresh-active {
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">
                    <i class="bi bi-house"></i> Dashboard
                </a>
                <a class="nav-link" href="emails.html">
                    <i class="bi bi-envelope"></i> Emails
                </a>
                <a class="nav-link active" href="admin_config.html">
                    <i class="bi bi-gear"></i> Configuration
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1><i class="bi bi-gear-fill"></i> Configuration Administrateur</h1>
                <p class="text-muted">Gérez les paramètres système et la configuration SMTP</p>
            </div>
        </div>

        <!-- System Health Monitoring Section -->
        <div class="config-section mb-4">
            <div class="config-header">
                <h3><i class="bi bi-heart-pulse"></i> Monitoring Système</h3>
                <small>Surveillez la santé et les performances du système BINANCE CRM</small>
            </div>
            <div class="p-4">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <button class="btn btn-primary btn-lg" onclick="checkSystemHealth()">
                            <i class="bi bi-activity"></i> Vérifier Santé Système
                        </button>
                        <button class="btn btn-outline-secondary ms-2" onclick="toggleAutoRefresh()">
                            <i class="bi bi-arrow-clockwise"></i> <span id="autoRefreshText">Activer Auto-refresh</span>
                        </button>
                    </div>
                    <div class="col-md-6 text-end">
                        <small class="text-muted">
                            <i class="bi bi-clock"></i> Dernière vérification:
                            <span id="lastHealthCheck">Jamais</span>
                        </small>
                    </div>
                </div>

                <div id="healthStatus" class="health-status-container">
                    <div class="text-center py-5">
                        <i class="bi bi-heart display-4 text-muted"></i>
                        <p class="text-muted mt-3">Cliquez sur "Vérifier Santé Système" pour commencer le monitoring</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- SMTP Configuration Section -->
        <div class="config-section">
            <div class="config-header">
                <h3><i class="bi bi-envelope-gear"></i> Configuration SMTP</h3>
                <small>Configurez le serveur email pour l'envoi réel d'emails</small>
            </div>
            <div class="p-4">
                <!-- Provider Selection -->
                <div class="row mb-4">
                    <div class="col-12">
                        <h5>Sélectionnez votre fournisseur email :</h5>
                        <div class="row">
                            <div class="col-md-3">
                                <div class="provider-card" onclick="selectProvider('gmail')">
                                    <div class="text-center">
                                        <i class="bi bi-google" style="font-size: 2rem; color: #ea4335;"></i>
                                        <h6 class="mt-2">Gmail</h6>
                                        <small class="text-muted">smtp.gmail.com:587</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="provider-card" onclick="selectProvider('outlook')">
                                    <div class="text-center">
                                        <i class="bi bi-microsoft" style="font-size: 2rem; color: #0078d4;"></i>
                                        <h6 class="mt-2">Outlook</h6>
                                        <small class="text-muted">smtp-mail.outlook.com:587</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="provider-card" onclick="selectProvider('yahoo')">
                                    <div class="text-center">
                                        <i class="bi bi-envelope" style="font-size: 2rem; color: #6001d2;"></i>
                                        <h6 class="mt-2">Yahoo</h6>
                                        <small class="text-muted">smtp.mail.yahoo.com:587</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="provider-card" onclick="selectProvider('custom')">
                                    <div class="text-center">
                                        <i class="bi bi-server" style="font-size: 2rem; color: #6c757d;"></i>
                                        <h6 class="mt-2">Personnalisé</h6>
                                        <small class="text-muted">Configuration manuelle</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- SMTP Configuration Form -->
                <form id="smtpConfigForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtpServer" class="form-label">
                                    <i class="bi bi-server"></i> Serveur SMTP
                                </label>
                                <input type="text" class="form-control" id="smtpServer" placeholder="smtp.gmail.com" required>
                                <div class="form-text">Adresse du serveur SMTP de votre fournisseur</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtpPort" class="form-label">
                                    <i class="bi bi-plug"></i> Port
                                </label>
                                <input type="number" class="form-control" id="smtpPort" placeholder="587" required>
                                <div class="form-text">Port SMTP (587 pour TLS, 465 pour SSL)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtpUsername" class="form-label">
                                    <i class="bi bi-person"></i> Nom d'utilisateur
                                </label>
                                <input type="email" class="form-control" id="smtpUsername" placeholder="<EMAIL>" required>
                                <div class="form-text">Votre adresse email complète</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="smtpPassword" class="form-label">
                                    <i class="bi bi-key"></i> Mot de passe
                                </label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="smtpPassword" placeholder="Mot de passe ou mot de passe d'application" required>
                                    <button class="btn btn-outline-secondary" type="button" onclick="togglePassword()">
                                        <i class="bi bi-eye" id="passwordToggle"></i>
                                    </button>
                                </div>
                                <div class="form-text">Pour Gmail, utilisez un mot de passe d'application</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fromName" class="form-label">
                                    <i class="bi bi-tag"></i> Nom d'expéditeur
                                </label>
                                <input type="text" class="form-control" id="fromName" placeholder="BINANCE CRM" required>
                                <div class="form-text">Nom affiché comme expéditeur</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <div class="form-check mt-4">
                                    <input class="form-check-input" type="checkbox" id="useTLS" checked>
                                    <label class="form-check-label" for="useTLS">
                                        <i class="bi bi-shield-check"></i> Utiliser TLS/STARTTLS
                                    </label>
                                    <div class="form-text">Recommandé pour la sécurité</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Action Buttons -->
                    <div class="d-flex gap-2 mb-3">
                        <button type="button" class="btn btn-warning" onclick="testSMTPConnection()">
                            <i class="bi bi-wifi"></i> Tester la connexion
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="bi bi-save"></i> Sauvegarder la configuration
                        </button>
                        <button type="button" class="btn btn-info" onclick="loadCurrentConfig()">
                            <i class="bi bi-arrow-clockwise"></i> Charger config actuelle
                        </button>
                        <button type="button" class="btn btn-secondary" onclick="resetToDefaults()">
                            <i class="bi bi-arrow-counterclockwise"></i> Réinitialiser
                        </button>
                    </div>
                </form>
                
                <!-- Test Results -->
                <div id="testResult" class="test-result"></div>
                
                <!-- Instructions -->
                <div class="mt-4">
                    <h5><i class="bi bi-info-circle"></i> Instructions par fournisseur :</h5>
                    <div class="accordion" id="instructionsAccordion">
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#gmailInstructions">
                                    <i class="bi bi-google me-2"></i> Configuration Gmail
                                </button>
                            </h2>
                            <div id="gmailInstructions" class="accordion-collapse collapse" data-bs-parent="#instructionsAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Activez l'authentification à 2 facteurs sur votre compte Gmail</li>
                                        <li>Allez dans <strong>Paramètres Google > Sécurité > Mots de passe d'application</strong></li>
                                        <li>Générez un mot de passe d'application pour "Mail"</li>
                                        <li>Utilisez ce mot de passe dans le champ "Mot de passe" ci-dessus</li>
                                        <li>Serveur : <code>smtp.gmail.com</code>, Port : <code>587</code>, TLS : <code>Activé</code></li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#outlookInstructions">
                                    <i class="bi bi-microsoft me-2"></i> Configuration Outlook
                                </button>
                            </h2>
                            <div id="outlookInstructions" class="accordion-collapse collapse" data-bs-parent="#instructionsAccordion">
                                <div class="accordion-body">
                                    <ol>
                                        <li>Utilisez votre email et mot de passe Outlook normaux</li>
                                        <li>Serveur : <code>smtp-mail.outlook.com</code>, Port : <code>587</code>, TLS : <code>Activé</code></li>
                                        <li>Assurez-vous que l'authentification SMTP est activée dans votre compte</li>
                                    </ol>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- System Status Section -->
        <div class="config-section">
            <div class="config-header">
                <h3><i class="bi bi-activity"></i> État du Système</h3>
                <small>Vérifiez le statut des services</small>
            </div>
            <div class="p-4">
                <div class="row">
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-server" style="font-size: 2rem; color: #28a745;"></i>
                                <h6 class="mt-2">Serveur Web</h6>
                                <span class="badge bg-success">En ligne</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-envelope-gear" style="font-size: 2rem;" id="emailServerIcon"></i>
                                <h6 class="mt-2">Serveur Email</h6>
                                <span class="badge" id="emailServerStatus">Vérification...</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-file-earmark-pdf" style="font-size: 2rem; color: #dc3545;"></i>
                                <h6 class="mt-2">Serveur PDF</h6>
                                <span class="badge bg-success">En ligne</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card text-center">
                            <div class="card-body">
                                <i class="bi bi-database" style="font-size: 2rem; color: #17a2b8;"></i>
                                <h6 class="mt-2">Base de Données</h6>
                                <span class="badge bg-success">En ligne</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Configuration SMTP providers
        const smtpProviders = {
            gmail: {
                server: 'smtp.gmail.com',
                port: 587,
                tls: true,
                instructions: 'Utilisez un mot de passe d\'application Gmail'
            },
            outlook: {
                server: 'smtp-mail.outlook.com',
                port: 587,
                tls: true,
                instructions: 'Utilisez votre mot de passe Outlook normal'
            },
            yahoo: {
                server: 'smtp.mail.yahoo.com',
                port: 587,
                tls: true,
                instructions: 'Activez l\'accès aux applications moins sécurisées'
            },
            custom: {
                server: '',
                port: 587,
                tls: true,
                instructions: 'Configurez selon votre fournisseur SMTP'
            }
        };

        let currentProvider = 'gmail';

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            loadCurrentConfig();
            checkEmailServerStatus();
            selectProvider('gmail');
        });

        // Provider selection
        function selectProvider(provider) {
            currentProvider = provider;
            
            // Update UI
            document.querySelectorAll('.provider-card').forEach(card => {
                card.classList.remove('selected');
            });
            event.target.closest('.provider-card').classList.add('selected');
            
            // Fill form with provider defaults
            const config = smtpProviders[provider];
            document.getElementById('smtpServer').value = config.server;
            document.getElementById('smtpPort').value = config.port;
            document.getElementById('useTLS').checked = config.tls;
        }

        // Toggle password visibility
        function togglePassword() {
            const passwordField = document.getElementById('smtpPassword');
            const toggleIcon = document.getElementById('passwordToggle');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.className = 'bi bi-eye-slash';
            } else {
                passwordField.type = 'password';
                toggleIcon.className = 'bi bi-eye';
            }
        }

        // Load current configuration
        async function loadCurrentConfig() {
            try {
                const response = await fetch('/smtp_config.json');
                if (response.ok) {
                    const config = await response.json();
                    document.getElementById('smtpServer').value = config.smtp_server || '';
                    document.getElementById('smtpPort').value = config.smtp_port || 587;
                    document.getElementById('smtpUsername').value = config.username || '';
                    document.getElementById('smtpPassword').value = config.password || '';
                    document.getElementById('fromName').value = config.from_name || 'BINANCE CRM';
                    document.getElementById('useTLS').checked = config.use_tls !== false;
                    
                    showNotification('Configuration chargée avec succès', 'success');
                } else {
                    showNotification('Aucune configuration trouvée, utilisation des valeurs par défaut', 'info');
                }
            } catch (error) {
                showNotification('Erreur lors du chargement de la configuration', 'error');
            }
        }

        // Save SMTP configuration
        document.getElementById('smtpConfigForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const config = {
                smtp_server: document.getElementById('smtpServer').value,
                smtp_port: parseInt(document.getElementById('smtpPort').value),
                username: document.getElementById('smtpUsername').value,
                password: document.getElementById('smtpPassword').value,
                from_name: document.getElementById('fromName').value,
                use_tls: document.getElementById('useTLS').checked
            };
            
            try {
                const response = await fetch('http://localhost:8001/api/configure-smtp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                
                if (result.success) {
                    showNotification('Configuration SMTP sauvegardée avec succès !', 'success');
                    checkEmailServerStatus();
                } else {
                    showNotification('Erreur : ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Erreur de connexion au serveur email', 'error');
            }
        });

        // Test SMTP connection
        async function testSMTPConnection() {
            const testButton = document.querySelector('[onclick="testSMTPConnection()"]');
            const originalText = testButton.innerHTML;
            
            testButton.innerHTML = '<i class="bi bi-hourglass-split"></i> Test en cours...';
            testButton.disabled = true;
            
            const config = {
                smtp_server: document.getElementById('smtpServer').value,
                smtp_port: parseInt(document.getElementById('smtpPort').value),
                username: document.getElementById('smtpUsername').value,
                password: document.getElementById('smtpPassword').value,
                use_tls: document.getElementById('useTLS').checked
            };
            
            try {
                const response = await fetch('http://localhost:8001/api/test-smtp', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });
                
                const result = await response.json();
                const testResultDiv = document.getElementById('testResult');
                
                if (result.success) {
                    testResultDiv.className = 'test-result test-success';
                    testResultDiv.innerHTML = `
                        <i class="bi bi-check-circle"></i>
                        <strong>Test réussi !</strong> Connexion SMTP établie avec succès.
                        <br><small>Serveur : ${config.smtp_server}:${config.smtp_port}</small>
                    `;
                    showNotification('Test SMTP réussi !', 'success');
                } else {
                    testResultDiv.className = 'test-result test-error';
                    testResultDiv.innerHTML = `
                        <i class="bi bi-x-circle"></i>
                        <strong>Test échoué !</strong> ${result.error}
                        <br><small>Vérifiez vos paramètres et réessayez.</small>
                    `;
                    showNotification('Test SMTP échoué : ' + result.error, 'error');
                }
                
                testResultDiv.style.display = 'block';
                
            } catch (error) {
                const testResultDiv = document.getElementById('testResult');
                testResultDiv.className = 'test-result test-error';
                testResultDiv.innerHTML = `
                    <i class="bi bi-x-circle"></i>
                    <strong>Erreur de connexion !</strong> Impossible de contacter le serveur email.
                    <br><small>Assurez-vous que le serveur email est démarré.</small>
                `;
                testResultDiv.style.display = 'block';
                showNotification('Erreur de connexion au serveur email', 'error');
            }
            
            testButton.innerHTML = originalText;
            testButton.disabled = false;
        }

        // Check email server status
        async function checkEmailServerStatus() {
            try {
                const response = await fetch('http://localhost:8001/api/status');
                const statusBadge = document.getElementById('emailServerStatus');
                const statusIcon = document.getElementById('emailServerIcon');
                
                if (response.ok) {
                    statusBadge.className = 'badge bg-success';
                    statusBadge.textContent = 'En ligne';
                    statusIcon.style.color = '#28a745';
                } else {
                    throw new Error('Server not responding');
                }
            } catch (error) {
                const statusBadge = document.getElementById('emailServerStatus');
                const statusIcon = document.getElementById('emailServerIcon');
                statusBadge.className = 'badge bg-danger';
                statusBadge.textContent = 'Hors ligne';
                statusIcon.style.color = '#dc3545';
            }
        }

        // Reset to defaults
        function resetToDefaults() {
            document.getElementById('smtpServer').value = 'smtp.gmail.com';
            document.getElementById('smtpPort').value = '587';
            document.getElementById('smtpUsername').value = '';
            document.getElementById('smtpPassword').value = '';
            document.getElementById('fromName').value = 'BINANCE CRM';
            document.getElementById('useTLS').checked = true;
            
            selectProvider('gmail');
            showNotification('Configuration réinitialisée aux valeurs par défaut', 'info');
        }

        // Show notification
        function showNotification(message, type) {
            const container = document.getElementById('notificationContainer');
            const alertClass = type === 'success' ? 'alert-success' : 
                              type === 'error' ? 'alert-danger' : 
                              type === 'info' ? 'alert-info' : 'alert-warning';
            
            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;
            
            container.appendChild(notification);
            
            // Auto remove after 5 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // ===== FONCTIONNALITÉS MONITORING DE SANTÉ =====

        let autoRefreshInterval = null;
        let isAutoRefreshActive = false;

        async function checkSystemHealth() {
            const statusContainer = document.getElementById('healthStatus');
            const lastCheckSpan = document.getElementById('lastHealthCheck');

            // Afficher le spinner de chargement
            statusContainer.innerHTML = `
                <div class="text-center py-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Vérification en cours...</span>
                    </div>
                    <p class="mt-3">Vérification de la santé du système...</p>
                </div>
            `;

            try {
                const response = await fetch('/api/health');
                const result = await response.json();

                if (result.success) {
                    displayHealthStatus(result.data);
                    lastCheckSpan.textContent = new Date().toLocaleString('fr-FR');
                } else {
                    displayHealthError('Erreur lors de la vérification de santé');
                }
            } catch (error) {
                console.error('Erreur health check:', error);
                displayHealthError('Impossible de contacter le serveur');
            }
        }

        function displayHealthStatus(health) {
            const statusContainer = document.getElementById('healthStatus');
            const globalStatus = health.status;

            let statusColor, statusIcon, statusText;
            switch (globalStatus) {
                case 'healthy':
                    statusColor = 'success';
                    statusIcon = 'check-circle-fill';
                    statusText = 'SYSTÈME EN BONNE SANTÉ';
                    break;
                case 'degraded':
                    statusColor = 'warning';
                    statusIcon = 'exclamation-triangle-fill';
                    statusText = 'SYSTÈME DÉGRADÉ';
                    break;
                case 'unhealthy':
                    statusColor = 'danger';
                    statusIcon = 'x-circle-fill';
                    statusText = 'SYSTÈME EN PANNE';
                    break;
                default:
                    statusColor = 'secondary';
                    statusIcon = 'question-circle-fill';
                    statusText = 'STATUT INCONNU';
            }

            let html = `
                <div class="p-4">
                    <div class="alert alert-${statusColor} text-center mb-4">
                        <h4><i class="bi bi-${statusIcon}"></i> ${statusText}</h4>
                        <small>Dernière vérification: ${new Date(health.timestamp).toLocaleString('fr-FR')}</small>
                    </div>

                    <div class="row">
            `;

            // Base de données
            if (health.checks.database) {
                const dbStatus = health.checks.database.status;
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="health-card ${dbStatus}">
                            <h6><i class="bi bi-database"></i> Base de Données</h6>
                            <div class="health-metric">
                                <span>Statut</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-${dbStatus}"></span>
                                    ${dbStatus === 'healthy' ? 'Opérationnelle' : 'Problème'}
                                </span>
                            </div>
                            <div class="health-metric">
                                <span>Temps de réponse</span>
                                <span class="metric-value">${health.checks.database.response_time_ms}ms</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Système
            if (health.checks.system) {
                const sysStatus = health.checks.system.status;
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="health-card ${sysStatus}">
                            <h6><i class="bi bi-cpu"></i> Système</h6>
                            <div class="health-metric">
                                <span>Statut</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-${sysStatus}"></span>
                                    ${sysStatus === 'healthy' ? 'Normal' : sysStatus === 'limited' ? 'Limité' : 'Problème'}
                                </span>
                            </div>
                            ${health.checks.system.memory_usage_mb ? `
                                <div class="health-metric">
                                    <span>Mémoire RAM</span>
                                    <span class="metric-value">${health.checks.system.memory_usage_mb} MB</span>
                                </div>
                            ` : ''}
                            ${health.checks.system.cpu_percent !== undefined ? `
                                <div class="health-metric">
                                    <span>CPU</span>
                                    <span class="metric-value">${health.checks.system.cpu_percent}%</span>
                                </div>
                            ` : ''}
                            ${health.checks.system.uptime_seconds ? `
                                <div class="health-metric">
                                    <span>Uptime</span>
                                    <span class="metric-value">${formatUptime(health.checks.system.uptime_seconds)}</span>
                                </div>
                            ` : ''}
                        </div>
                    </div>
                `;
            }

            html += `</div><div class="row">`;

            // Intégrité des données
            if (health.checks.data_integrity) {
                const dataStatus = health.checks.data_integrity.status;
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="health-card ${dataStatus}">
                            <h6><i class="bi bi-shield-check"></i> Intégrité des Données</h6>
                            <div class="health-metric">
                                <span>Statut</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-${dataStatus}"></span>
                                    ${dataStatus === 'healthy' ? 'Intègre' : 'Problème'}
                                </span>
                            </div>
                            <div class="health-metric">
                                <span>Clients</span>
                                <span class="metric-value">${health.checks.data_integrity.clients_count}</span>
                            </div>
                            <div class="health-metric">
                                <span>Vendeurs</span>
                                <span class="metric-value">${health.checks.data_integrity.vendeurs_count}</span>
                            </div>
                            <div class="health-metric">
                                <span>Imports</span>
                                <span class="metric-value">${health.checks.data_integrity.imports_count}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            // Performance
            if (health.checks.performance) {
                const perfStatus = health.checks.performance.status;
                html += `
                    <div class="col-md-6 mb-3">
                        <div class="health-card ${perfStatus}">
                            <h6><i class="bi bi-speedometer2"></i> Performance</h6>
                            <div class="health-metric">
                                <span>Statut</span>
                                <span class="metric-value">
                                    <span class="status-indicator status-${perfStatus}"></span>
                                    ${perfStatus === 'healthy' ? 'Optimale' : perfStatus === 'warning' ? 'Dégradée' : 'Problème'}
                                </span>
                            </div>
                            <div class="health-metric">
                                <span>Index DB</span>
                                <span class="metric-value">${health.checks.performance.indexes_count}</span>
                            </div>
                            <div class="health-metric">
                                <span>Pool Connexions</span>
                                <span class="metric-value">${health.checks.performance.pool_size}</span>
                            </div>
                        </div>
                    </div>
                `;
            }

            html += `</div></div>`;
            statusContainer.innerHTML = html;
        }

        function displayHealthError(message) {
            const statusContainer = document.getElementById('healthStatus');
            statusContainer.innerHTML = `
                <div class="p-4">
                    <div class="alert alert-danger text-center">
                        <h5><i class="bi bi-exclamation-triangle"></i> Erreur de Monitoring</h5>
                        <p>${message}</p>
                        <button class="btn btn-outline-danger" onclick="checkSystemHealth()">
                            <i class="bi bi-arrow-clockwise"></i> Réessayer
                        </button>
                    </div>
                </div>
            `;
        }

        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            if (days > 0) {
                return `${days}j ${hours}h ${minutes}m`;
            } else if (hours > 0) {
                return `${hours}h ${minutes}m`;
            } else {
                return `${minutes}m`;
            }
        }

        function toggleAutoRefresh() {
            const button = document.querySelector('button[onclick="toggleAutoRefresh()"]');
            const textSpan = document.getElementById('autoRefreshText');

            if (isAutoRefreshActive) {
                // Désactiver l'auto-refresh
                clearInterval(autoRefreshInterval);
                autoRefreshInterval = null;
                isAutoRefreshActive = false;

                button.classList.remove('auto-refresh-active');
                textSpan.textContent = 'Activer Auto-refresh';
                showNotification('Auto-refresh désactivé', 'info');
            } else {
                // Activer l'auto-refresh (toutes les 30 secondes)
                autoRefreshInterval = setInterval(checkSystemHealth, 30000);
                isAutoRefreshActive = true;

                button.classList.add('auto-refresh-active');
                textSpan.textContent = 'Désactiver Auto-refresh';
                showNotification('Auto-refresh activé (30s)', 'success');

                // Faire une vérification immédiate
                checkSystemHealth();
            }
        }

        // Nettoyer l'auto-refresh quand on quitte la page
        window.addEventListener('beforeunload', function() {
            if (autoRefreshInterval) {
                clearInterval(autoRefreshInterval);
            }
        });

    </script>
</body>
</html>

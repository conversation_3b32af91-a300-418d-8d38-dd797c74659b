#!/usr/bin/env python3
"""
BINANCE CRM - Script de démarrage des serveurs
Lance tous les serveurs nécessaires au fonctionnement du CRM
"""

import subprocess
import sys
import os
import time
import threading
import webbrowser

def check_dependencies():
    """Vérifier et installer les dépendances manquantes"""
    try:
        import reportlab
        print("✅ ReportLab est installé")
    except ImportError:
        print("⚠️ Installation de ReportLab...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
        print("✅ ReportLab installé avec succès")
    
    try:
        import psutil
        print("✅ psutil est installé")
    except ImportError:
        print("⚠️ Installation de psutil...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
        print("✅ psutil installé avec succès")

def start_server(script_name, port):
    """Démarrer un serveur dans un processus séparé"""
    process = subprocess.Popen([sys.executable, script_name], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True)
    
    print(f"🚀 Serveur {script_name} démarré sur le port {port}")
    return process

def start_http_server():
    """Démarrer le serveur HTTP pour l'interface web"""
    process = subprocess.Popen([sys.executable, "-m", "http.server", "8080"], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True)
    
    print(f"🚀 Serveur HTTP démarré sur http://localhost:8080")
    return process

def run_performance_test():
    """Exécuter un test de performance après démarrage"""
    print("\n🔍 Test de performance des serveurs...")
    time.sleep(3)  # Attendre que tous les serveurs soient prêts

    try:
        # Exécuter le benchmark
        subprocess.run([sys.executable, "performance_benchmark.py"],
                      capture_output=False, text=True, timeout=60)
    except subprocess.TimeoutExpired:
        print("⚠️ Test de performance interrompu (timeout)")
    except Exception as e:
        print(f"⚠️ Erreur lors du test de performance: {e}")

def main():
    """Fonction principale optimisée"""
    print("🔍 Vérification des dépendances...")
    check_dependencies()

    print("\n🚀 Démarrage des serveurs BINANCE CRM OPTIMISÉS...\n")

    # Démarrer les serveurs avec gestion d'erreurs améliorée
    servers = []

    try:
        # Serveur de base de données (optimisé avec cache et pool étendu)
        print("🗄️ Démarrage du serveur de base de données optimisé...")
        db_server = start_server("database_server.py", 8001)
        servers.append(("Database", db_server))
        time.sleep(2)  # Plus de temps pour l'initialisation du cache

        # Serveur email
        print("📧 Démarrage du serveur email...")
        email_server = start_server("email_server.py", 8002)
        servers.append(("Email", email_server))
        time.sleep(1)

        # Serveur PDF (optimisé avec pool de threads)
        print("📄 Démarrage du serveur PDF optimisé...")
        pdf_server = start_server("pdf_server.py", 8003)
        servers.append(("PDF", pdf_server))
        time.sleep(1)

        # Serveur HTTP avec compression
        print("🌐 Démarrage du serveur HTTP...")
        http_server = start_http_server()
        servers.append(("HTTP", http_server))
        time.sleep(2)

        print("\n✅ Tous les serveurs optimisés sont démarrés !")
        print("\n📊 BINANCE CRM OPTIMISÉ est accessible à l'adresse : http://localhost:8080")
        print("\n🚀 NOUVELLES FONCTIONNALITÉS OPTIMISÉES :")
        print("   • Cache intelligent pour les données statiques")
        print("   • Compression gzip automatique des réponses")
        print("   • Pagination côté serveur pour de meilleures performances")
        print("   • Pool de connexions étendu (20 connexions)")
        print("   • Traitement CSV par chunks pour les gros fichiers")
        print("   • Index de base de données optimisés")

        # Ouvrir le navigateur
        webbrowser.open("http://localhost:8080")

        # Optionnel : Exécuter un test de performance
        response = input("\n🔍 Voulez-vous exécuter un test de performance ? (y/N): ")
        if response.lower() in ['y', 'yes', 'oui']:
            run_performance_test()

        print("\n⚠️ Appuyez sur Ctrl+C pour arrêter tous les serveurs")

        # Maintenir le script en vie avec monitoring
        while True:
            time.sleep(5)
            # Vérifier que tous les serveurs sont encore actifs
            for name, server in servers:
                if server.poll() is not None:
                    print(f"⚠️ Le serveur {name} s'est arrêté de manière inattendue")

    except KeyboardInterrupt:
        print("\n🛑 Arrêt des serveurs optimisés...")
        for name, server in servers:
            try:
                server.terminate()
                server.wait(timeout=5)
                print(f"✅ Serveur {name} arrêté")
            except subprocess.TimeoutExpired:
                server.kill()
                print(f"🔥 Serveur {name} forcé à s'arrêter")
            except Exception as e:
                print(f"⚠️ Erreur lors de l'arrêt du serveur {name}: {e}")
        print("✅ Tous les serveurs ont été arrêtés")
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")
        # Arrêter tous les serveurs en cas d'erreur
        for name, server in servers:
            try:
                server.terminate()
            except:
                pass

if __name__ == "__main__":
    main()

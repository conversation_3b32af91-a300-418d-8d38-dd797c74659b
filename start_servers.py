#!/usr/bin/env python3
"""
BINANCE CRM - Script de démarrage des serveurs
Lance tous les serveurs nécessaires au fonctionnement du CRM
"""

import subprocess
import sys
import os
import time
import threading
import webbrowser

def check_dependencies():
    """Vérifier et installer les dépendances manquantes"""
    try:
        import reportlab
        print("✅ ReportLab est installé")
    except ImportError:
        print("⚠️ Installation de ReportLab...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
        print("✅ ReportLab installé avec succès")
    
    try:
        import psutil
        print("✅ psutil est installé")
    except ImportError:
        print("⚠️ Installation de psutil...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "psutil"])
        print("✅ psutil installé avec succès")

def start_server(script_name, port):
    """Démarrer un serveur dans un processus séparé"""
    process = subprocess.Popen([sys.executable, script_name], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True)
    
    print(f"🚀 Serveur {script_name} démarré sur le port {port}")
    return process

def start_http_server():
    """Démarrer le serveur HTTP pour l'interface web"""
    process = subprocess.Popen([sys.executable, "-m", "http.server", "8080"], 
                              stdout=subprocess.PIPE,
                              stderr=subprocess.PIPE,
                              universal_newlines=True)
    
    print(f"🚀 Serveur HTTP démarré sur http://localhost:8080")
    return process

def main():
    """Fonction principale"""
    print("🔍 Vérification des dépendances...")
    check_dependencies()
    
    print("\n🚀 Démarrage des serveurs BINANCE CRM...\n")
    
    # Démarrer les serveurs
    db_server = start_server("database_server.py", 8001)
    time.sleep(1)  # Attendre que le serveur de base de données soit prêt
    
    email_server = start_server("email_server.py", 8002)
    time.sleep(1)
    
    pdf_server = start_server("pdf_server.py", 8003)
    time.sleep(1)
    
    http_server = start_http_server()
    time.sleep(2)
    
    print("\n✅ Tous les serveurs sont démarrés !")
    print("\n📊 BINANCE CRM est accessible à l'adresse : http://localhost:8080")
    
    # Ouvrir le navigateur
    webbrowser.open("http://localhost:8080")
    
    print("\n⚠️ Appuyez sur Ctrl+C pour arrêter tous les serveurs")
    
    try:
        # Maintenir le script en vie
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n🛑 Arrêt des serveurs...")
        db_server.terminate()
        email_server.terminate()
        pdf_server.terminate()
        http_server.terminate()
        print("✅ Tous les serveurs ont été arrêtés")

if __name__ == "__main__":
    main()

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Démonstration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .card { 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1); 
            border: none;
        }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            border: none; 
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
        }
        .feature-card { 
            transition: all 0.3s ease; 
            border: none;
            border-radius: 12px;
        }
        .feature-card:hover { 
            transform: translateY(-5px); 
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
        }
        .login-box {
            background: rgba(255,255,255,0.95);
            border-radius: 15px;
            padding: 25px;
            margin: 10px 0;
            border-left: 5px solid #667eea;
        }
        .pulse {
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
        .gradient-text {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Courier New', monospace;
            position: relative;
            overflow-x: auto;
        }
        .copy-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            background: rgba(255,255,255,0.1);
            border: none;
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            cursor: pointer;
        }
        .status-badge {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        .status-ready {
            background: linear-gradient(135deg, #48bb78, #38a169);
            color: white;
        }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-11">
                <div class="card">
                    <div class="card-body p-5">
                        <!-- Header -->
                        <div class="text-center mb-5">
                            <h1 class="display-3 gradient-text mb-3 pulse">
                                <i class="bi bi-building"></i> CRM System
                            </h1>
                            <p class="lead text-muted">Système de gestion de la relation client complet et professionnel</p>
                            <span class="status-badge status-ready">
                                <i class="bi bi-check-circle"></i> Application 100% Complète
                            </span>
                        </div>
                        
                        <!-- Comptes de connexion -->
                        <div class="row mb-5">
                            <div class="col-md-6">
                                <div class="login-box">
                                    <div class="text-center">
                                        <i class="bi bi-person-check fs-1 text-primary mb-3"></i>
                                        <h4 class="text-primary">👑 Compte Administrateur</h4>
                                        <p class="text-muted mb-3">Accès complet à toutes les fonctionnalités</p>
                                        <div class="bg-primary text-white p-3 rounded">
                                            <div class="row">
                                                <div class="col-6 text-end"><strong>Utilisateur:</strong></div>
                                                <div class="col-6 text-start"><code>admin</code></div>
                                            </div>
                                            <div class="row">
                                                <div class="col-6 text-end"><strong>Mot de passe:</strong></div>
                                                <div class="col-6 text-start"><code>admin123</code></div>
                                            </div>
                                        </div>
                                        <small class="text-muted mt-2 d-block">
                                            <i class="bi bi-shield-check"></i> Changez ce mot de passe après la première connexion
                                        </small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="login-box">
                                    <div class="text-center">
                                        <i class="bi bi-people fs-1 text-success mb-3"></i>
                                        <h4 class="text-success">👤 Comptes Vendeurs</h4>
                                        <p class="text-muted mb-3">Accès aux clients attribués uniquement</p>
                                        <div class="bg-success text-white p-3 rounded">
                                            <div class="small">
                                                <strong>marie.martin</strong> / <code>vendeur123</code><br>
                                                <strong>pierre.durand</strong> / <code>vendeur123</code><br>
                                                <strong>sophie.bernard</strong> / <code>vendeur123</code>
                                            </div>
                                        </div>
                                        <small class="text-muted mt-2 d-block">
                                            <i class="bi bi-info-circle"></i> Comptes de démonstration avec données test
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fonctionnalités principales -->
                        <div class="row mb-5">
                            <div class="col-12">
                                <h3 class="text-center mb-4">
                                    <i class="bi bi-star-fill text-warning"></i> 
                                    Fonctionnalités Principales
                                </h3>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card feature-card h-100 border-primary">
                                    <div class="card-body text-center">
                                        <i class="bi bi-people-fill fs-1 text-primary mb-3"></i>
                                        <h5>Gestion des Clients</h5>
                                        <ul class="list-unstyled text-start small">
                                            <li><i class="bi bi-check text-success"></i> CRUD complet</li>
                                            <li><i class="bi bi-check text-success"></i> Attribution aux vendeurs</li>
                                            <li><i class="bi bi-check text-success"></i> Indicateurs personnalisés</li>
                                            <li><i class="bi bi-check text-success"></i> Import/Export CSV</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card feature-card h-100 border-success">
                                    <div class="card-body text-center">
                                        <i class="bi bi-envelope-heart fs-1 text-success mb-3"></i>
                                        <h5>Système d'Emails</h5>
                                        <ul class="list-unstyled text-start small">
                                            <li><i class="bi bi-check text-success"></i> Templates dynamiques</li>
                                            <li><i class="bi bi-check text-success"></i> Variables personnalisées</li>
                                            <li><i class="bi bi-check text-success"></i> Envoi en lot</li>
                                            <li><i class="bi bi-check text-success"></i> Historique complet</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4 mb-3">
                                <div class="card feature-card h-100 border-info">
                                    <div class="card-body text-center">
                                        <i class="bi bi-calendar-check fs-1 text-info mb-3"></i>
                                        <h5>Agenda Intégré</h5>
                                        <ul class="list-unstyled text-start small">
                                            <li><i class="bi bi-check text-success"></i> Planification RDV</li>
                                            <li><i class="bi bi-check text-success"></i> Vue calendrier</li>
                                            <li><i class="bi bi-check text-success"></i> Rappels automatiques</li>
                                            <li><i class="bi bi-check text-success"></i> Suivi des statuts</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Fonctionnalités avancées -->
                        <div class="row mb-5">
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-graph-up-arrow fs-1 text-warning mb-3"></i>
                                        <h6>Statistiques Avancées</h6>
                                        <p class="small text-muted">Graphiques interactifs et rapports détaillés</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-shield-lock fs-1 text-danger mb-3"></i>
                                        <h6>Sécurité Renforcée</h6>
                                        <p class="small text-muted">Authentification, rôles, logs de sécurité</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-phone fs-1 text-info mb-3"></i>
                                        <h6>Interface Responsive</h6>
                                        <p class="small text-muted">Optimisé mobile, tablette et desktop</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3 mb-3">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-api fs-1 text-success mb-3"></i>
                                        <h6>API REST Complète</h6>
                                        <p class="small text-muted">Documentation Swagger intégrée</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Instructions de démarrage -->
                        <div class="card bg-light mb-4">
                            <div class="card-body">
                                <h4 class="text-center mb-4">
                                    <i class="bi bi-rocket-takeoff"></i> 
                                    Comment Démarrer l'Application
                                </h4>
                                
                                <div class="row">
                                    <div class="col-md-6">
                                        <h5><i class="bi bi-windows"></i> Windows</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyToClipboard('windows-code')">
                                                <i class="bi bi-clipboard"></i>
                                            </button>
                                            <div id="windows-code">
# Installer Python 3.8+ depuis python.org<br>
# Ouvrir PowerShell dans le dossier CRM<br>
<br>
pip install -r requirements.txt<br>
start.bat dev<br>
<br>
# Ou directement :<br>
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <h5><i class="bi bi-apple"></i> Linux/Mac</h5>
                                        <div class="code-block">
                                            <button class="copy-btn" onclick="copyToClipboard('linux-code')">
                                                <i class="bi bi-clipboard"></i>
                                            </button>
                                            <div id="linux-code">
# Installer Python 3.8+ et pip<br>
# Ouvrir terminal dans le dossier CRM<br>
<br>
pip install -r requirements.txt<br>
chmod +x start.sh<br>
./start.sh dev<br>
<br>
# Ou directement :<br>
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="text-center mt-4">
                                    <div class="alert alert-info">
                                        <i class="bi bi-info-circle"></i>
                                        <strong>Accès à l'application :</strong> 
                                        <a href="http://localhost:8000" target="_blank" class="alert-link">
                                            http://localhost:8000
                                        </a>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Documentation -->
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="bi bi-book"></i> Documentation Disponible</h5>
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="bi bi-file-text"></i> README.md</span>
                                        <span class="badge bg-primary rounded-pill">Vue d'ensemble</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="bi bi-person-gear"></i> GUIDE_UTILISATEUR.md</span>
                                        <span class="badge bg-success rounded-pill">Manuel complet</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="bi bi-gear"></i> INSTALLATION.md</span>
                                        <span class="badge bg-info rounded-pill">Installation</span>
                                    </li>
                                    <li class="list-group-item d-flex justify-content-between align-items-center">
                                        <span><i class="bi bi-cloud-upload"></i> DEPLOYMENT.md</span>
                                        <span class="badge bg-warning rounded-pill">Déploiement</span>
                                    </li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="bi bi-database"></i> Données de Démonstration</h5>
                                <div class="bg-light p-3 rounded">
                                    <p class="mb-2"><strong>Inclus dans l'application :</strong></p>
                                    <ul class="mb-0">
                                        <li>1 compte administrateur</li>
                                        <li>3 comptes vendeurs</li>
                                        <li>10 clients de démonstration</li>
                                        <li>3 templates d'email</li>
                                        <li>5 rendez-vous planifiés</li>
                                    </ul>
                                </div>
                                <div class="mt-3">
                                    <small class="text-muted">
                                        <i class="bi bi-lightbulb"></i>
                                        <strong>Conseil :</strong> Explorez avec les données de test avant d'ajouter vos vraies données !
                                    </small>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Footer -->
                        <div class="text-center mt-5 pt-4 border-top">
                            <h4 class="gradient-text mb-3">
                                🎉 CRM System - 100% Complet et Prêt à l'Emploi !
                            </h4>
                            <p class="text-muted">
                                Système professionnel avec toutes les fonctionnalités CRM essentielles<br>
                                <small>Développé avec FastAPI, SQLAlchemy, Bootstrap 5 et beaucoup d'amour ❤️</small>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function copyToClipboard(elementId) {
            const element = document.getElementById(elementId);
            const text = element.innerText;
            navigator.clipboard.writeText(text).then(function() {
                // Feedback visuel
                const btn = element.parentElement.querySelector('.copy-btn');
                const originalText = btn.innerHTML;
                btn.innerHTML = '<i class="bi bi-check"></i>';
                btn.style.background = 'rgba(40, 167, 69, 0.3)';
                
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.style.background = 'rgba(255,255,255,0.1)';
                }, 2000);
            });
        }
        
        // Animation au chargement
        document.addEventListener('DOMContentLoaded', function() {
            const cards = document.querySelectorAll('.feature-card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(20px)';
                    card.style.transition = 'all 0.5s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 100);
            });
        });
    </script>
</body>
</html>

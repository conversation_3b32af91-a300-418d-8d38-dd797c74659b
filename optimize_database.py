#!/usr/bin/env python3
"""
BINANCE CRM - Optimisation Base de Données
Script pour améliorer les performances avec index et optimisations
"""

import sqlite3
import time
from datetime import datetime

class DatabaseOptimizer:
    def __init__(self, db_path='binance_crm.db'):
        self.db_path = db_path
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row
        return conn
    
    def analyze_current_performance(self):
        """Analyser les performances actuelles"""
        print("🔍 Analyse des performances actuelles...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Test des requêtes courantes
        queries = [
            ("SELECT COUNT(*) FROM clients", "Comptage clients"),
            ("SELECT * FROM clients WHERE email = '<EMAIL>'", "Recherche par email"),
            ("SELECT c.*, u.first_name FROM clients c LEFT JOIN users u ON c.assigned_to = u.id", "Jointure clients-vendeurs"),
            ("SELECT * FROM clients WHERE status = 'prospect'", "Filtrage par statut"),
            ("SELECT * FROM users WHERE role = 'vendeur'", "Vendeurs actifs")
        ]
        
        results = {}
        for query, description in queries:
            start_time = time.time()
            try:
                cursor.execute(query)
                cursor.fetchall()
                duration = time.time() - start_time
                results[description] = duration
                print(f"  {description}: {duration:.4f}s")
            except Exception as e:
                print(f"  {description}: ERREUR - {e}")
        
        conn.close()
        return results
    
    def create_indexes(self):
        """Créer les index pour optimiser les performances"""
        print("🚀 Création des index d'optimisation...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        indexes = [
            # Index pour la table clients
            ("idx_clients_email", "CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email)"),
            ("idx_clients_status", "CREATE INDEX IF NOT EXISTS idx_clients_status ON clients(status)"),
            ("idx_clients_assigned_to", "CREATE INDEX IF NOT EXISTS idx_clients_assigned_to ON clients(assigned_to)"),
            ("idx_clients_created_date", "CREATE INDEX IF NOT EXISTS idx_clients_created_date ON clients(created_date)"),
            ("idx_clients_last_activity", "CREATE INDEX IF NOT EXISTS idx_clients_last_activity ON clients(last_activity)"),
            
            # Index pour la table users
            ("idx_users_email", "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)"),
            ("idx_users_role", "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)"),
            ("idx_users_status", "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)"),
            ("idx_users_username", "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)"),
            
            # Index pour la table import_history
            ("idx_import_history_date", "CREATE INDEX IF NOT EXISTS idx_import_history_date ON import_history(import_date)"),
            ("idx_import_history_user", "CREATE INDEX IF NOT EXISTS idx_import_history_user ON import_history(user_id)"),
            ("idx_import_history_status", "CREATE INDEX IF NOT EXISTS idx_import_history_status ON import_history(status)"),
            
            # Index pour la table imported_clients
            ("idx_imported_clients_import", "CREATE INDEX IF NOT EXISTS idx_imported_clients_import ON imported_clients(import_id)"),
            ("idx_imported_clients_client", "CREATE INDEX IF NOT EXISTS idx_imported_clients_client ON imported_clients(client_id)"),
            
            # Index composites pour les requêtes fréquentes
            ("idx_clients_status_assigned", "CREATE INDEX IF NOT EXISTS idx_clients_status_assigned ON clients(status, assigned_to)"),
            ("idx_users_role_status", "CREATE INDEX IF NOT EXISTS idx_users_role_status ON users(role, status)")
        ]
        
        created_count = 0
        for index_name, query in indexes:
            try:
                cursor.execute(query)
                created_count += 1
                print(f"  ✅ {index_name}")
            except Exception as e:
                print(f"  ❌ {index_name}: {e}")
        
        conn.commit()
        conn.close()
        
        print(f"📊 {created_count}/{len(indexes)} index créés avec succès")
        return created_count
    
    def optimize_database_settings(self):
        """Optimiser les paramètres de la base de données"""
        print("⚙️ Optimisation des paramètres de la base de données...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        optimizations = [
            ("PRAGMA journal_mode = WAL", "Mode WAL pour de meilleures performances concurrentes"),
            ("PRAGMA synchronous = NORMAL", "Synchronisation normale pour équilibrer performance/sécurité"),
            ("PRAGMA cache_size = 10000", "Augmentation du cache (40MB)"),
            ("PRAGMA temp_store = MEMORY", "Stockage temporaire en mémoire"),
            ("PRAGMA mmap_size = 268435456", "Memory mapping 256MB"),
            ("PRAGMA optimize", "Optimisation automatique des statistiques")
        ]
        
        for pragma, description in optimizations:
            try:
                cursor.execute(pragma)
                print(f"  ✅ {description}")
            except Exception as e:
                print(f"  ❌ {description}: {e}")
        
        conn.close()
    
    def analyze_table_statistics(self):
        """Analyser les statistiques des tables"""
        print("📊 Analyse des statistiques des tables...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        tables = ['clients', 'users', 'import_history', 'imported_clients', 'email_templates']
        
        for table in tables:
            try:
                # Compter les enregistrements
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                
                # Analyser la taille
                cursor.execute(f"SELECT SUM(LENGTH(sql)) FROM sqlite_master WHERE type='table' AND name='{table}'")
                
                print(f"  📋 {table}: {count} enregistrements")
                
            except Exception as e:
                print(f"  ❌ Erreur pour {table}: {e}")
        
        conn.close()
    
    def vacuum_database(self):
        """Nettoyer et compacter la base de données"""
        print("🧹 Nettoyage et compactage de la base de données...")
        
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            # Obtenir la taille avant
            cursor.execute("PRAGMA page_count")
            pages_before = cursor.fetchone()[0]
            cursor.execute("PRAGMA page_size")
            page_size = cursor.fetchone()[0]
            size_before = pages_before * page_size
            
            # VACUUM
            cursor.execute("VACUUM")
            
            # Obtenir la taille après
            cursor.execute("PRAGMA page_count")
            pages_after = cursor.fetchone()[0]
            size_after = pages_after * page_size
            
            saved_bytes = size_before - size_after
            saved_mb = saved_bytes / (1024 * 1024)
            
            print(f"  📦 Taille avant: {size_before / (1024 * 1024):.2f} MB")
            print(f"  📦 Taille après: {size_after / (1024 * 1024):.2f} MB")
            print(f"  💾 Espace libéré: {saved_mb:.2f} MB")
            
        except Exception as e:
            print(f"  ❌ Erreur lors du VACUUM: {e}")
        
        conn.close()
    
    def create_connection_pool_config(self):
        """Créer une configuration pour un pool de connexions"""
        print("🔗 Génération de la configuration pour pool de connexions...")
        
        config = """
# Configuration recommandée pour pool de connexions SQLite
# À implémenter dans database_server.py

import sqlite3
from threading import Lock
import queue

class ConnectionPool:
    def __init__(self, db_path, max_connections=10):
        self.db_path = db_path
        self.max_connections = max_connections
        self.pool = queue.Queue(maxsize=max_connections)
        self.lock = Lock()
        
        # Pré-créer les connexions
        for _ in range(max_connections):
            conn = sqlite3.connect(db_path, check_same_thread=False)
            conn.row_factory = sqlite3.Row
            # Optimisations par connexion
            conn.execute("PRAGMA journal_mode = WAL")
            conn.execute("PRAGMA synchronous = NORMAL")
            conn.execute("PRAGMA cache_size = 2000")
            self.pool.put(conn)
    
    def get_connection(self):
        return self.pool.get()
    
    def return_connection(self, conn):
        self.pool.put(conn)
    
    def close_all(self):
        while not self.pool.empty():
            conn = self.pool.get()
            conn.close()

# Usage recommandé:
# pool = ConnectionPool('binance_crm.db')
# conn = pool.get_connection()
# try:
#     # Utiliser la connexion
#     pass
# finally:
#     pool.return_connection(conn)
"""
        
        with open('connection_pool_config.py', 'w') as f:
            f.write(config)
        
        print("  ✅ Configuration sauvegardée dans connection_pool_config.py")
    
    def run_full_optimization(self):
        """Exécuter l'optimisation complète"""
        print("🚀 OPTIMISATION COMPLÈTE DE LA BASE DE DONNÉES")
        print("=" * 60)
        
        start_time = time.time()
        
        # 1. Analyser les performances actuelles
        print("\n1️⃣ ANALYSE INITIALE")
        initial_perf = self.analyze_current_performance()
        
        # 2. Analyser les statistiques
        print("\n2️⃣ STATISTIQUES DES TABLES")
        self.analyze_table_statistics()
        
        # 3. Créer les index
        print("\n3️⃣ CRÉATION DES INDEX")
        indexes_created = self.create_indexes()
        
        # 4. Optimiser les paramètres
        print("\n4️⃣ OPTIMISATION DES PARAMÈTRES")
        self.optimize_database_settings()
        
        # 5. Nettoyer la base
        print("\n5️⃣ NETTOYAGE ET COMPACTAGE")
        self.vacuum_database()
        
        # 6. Analyser les performances après optimisation
        print("\n6️⃣ ANALYSE FINALE")
        final_perf = self.analyze_current_performance()
        
        # 7. Générer la configuration du pool
        print("\n7️⃣ CONFIGURATION POOL DE CONNEXIONS")
        self.create_connection_pool_config()
        
        # 8. Rapport final
        total_time = time.time() - start_time
        print(f"\n✅ OPTIMISATION TERMINÉE EN {total_time:.2f}s")
        
        # Comparaison des performances
        print("\n📊 COMPARAISON DES PERFORMANCES:")
        print("-" * 40)
        for query_name in initial_perf:
            if query_name in final_perf:
                before = initial_perf[query_name]
                after = final_perf[query_name]
                improvement = ((before - after) / before * 100) if before > 0 else 0
                status = "🚀" if improvement > 20 else "⚡" if improvement > 0 else "➡️"
                print(f"  {query_name}:")
                print(f"    Avant: {before:.4f}s")
                print(f"    Après: {after:.4f}s")
                print(f"    Amélioration: {improvement:+.1f}% {status}")
        
        print(f"\n🎯 RECOMMANDATIONS SUPPLÉMENTAIRES:")
        print("  1. Implémenter le pool de connexions (voir connection_pool_config.py)")
        print("  2. Ajouter un système de cache Redis pour les requêtes fréquentes")
        print("  3. Implémenter la pagination pour les grandes listes")
        print("  4. Utiliser des transactions batch pour les imports CSV")
        print("  5. Monitorer les performances avec des logs détaillés")

if __name__ == "__main__":
    optimizer = DatabaseOptimizer()
    optimizer.run_full_optimization()

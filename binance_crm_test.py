#!/usr/bin/env python3
"""
BINANCE CRM - VERSION TEST BASIQUE
Version ultra simple pour tester que le serveur fonctionne
"""

import http.server
import socketserver
import webbrowser
import threading
import time

PORT = 8000

class TestHandler(http.server.BaseHTTPRequestHandler):
    def log_message(self, format, *args):
        pass
    
    def do_GET(self):
        html = '''
<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>BINANCE CRM - Test</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
        }
        .test-card { 
            background: white; 
            border-radius: 15px; 
            box-shadow: 0 10px 30px rgba(0,0,0,0.1); 
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="test-card p-5 text-center">
                    <h1 class="display-4 mb-4">🎉 BINANCE CRM</h1>
                    <h2 class="h3 text-success mb-4">✅ SERVEUR FONCTIONNEL !</h2>
                    
                    <div class="alert alert-success">
                        <h4>🚀 Test Réussi !</h4>
                        <p class="mb-0">Le serveur HTTP fonctionne parfaitement sur le port 8000</p>
                    </div>
                    
                    <div class="row mt-4">
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5>✅ Serveur</h5>
                                    <p class="mb-0">HTTP Server OK</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5>✅ Port</h5>
                                    <p class="mb-0">8000 Accessible</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="card">
                                <div class="card-body">
                                    <h5>✅ Interface</h5>
                                    <p class="mb-0">Bootstrap OK</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <h5>🔗 URLs de Test :</h5>
                        <div class="list-group">
                            <a href="/" class="list-group-item list-group-item-action">
                                <strong>http://localhost:8000/</strong> - Page principale
                            </a>
                            <a href="/test" class="list-group-item list-group-item-action">
                                <strong>http://localhost:8000/test</strong> - Page de test
                            </a>
                            <a href="/api" class="list-group-item list-group-item-action">
                                <strong>http://localhost:8000/api</strong> - API test
                            </a>
                        </div>
                    </div>
                    
                    <div class="mt-4 alert alert-info">
                        <h6>📊 Informations Serveur :</h6>
                        <p class="mb-1"><strong>Port :</strong> 8000</p>
                        <p class="mb-1"><strong>Status :</strong> Actif</p>
                        <p class="mb-0"><strong>URL :</strong> http://localhost:8000</p>
                    </div>
                    
                    <div class="mt-4">
                        <button onclick="testAPI()" class="btn btn-primary me-2">Test API</button>
                        <button onclick="location.reload()" class="btn btn-success">Actualiser</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function testAPI() {
            fetch('/api')
                .then(response => response.json())
                .then(data => {
                    alert('API Test: ' + JSON.stringify(data));
                })
                .catch(error => {
                    alert('Erreur API: ' + error);
                });
        }
        
        // Test automatique au chargement
        console.log('BINANCE CRM Test - Serveur fonctionnel !');
        console.log('Port: 8000');
        console.log('URL: http://localhost:8000');
    </script>
</body>
</html>
        '''
        
        if self.path == '/api':
            self.send_json()
        else:
            self.send_html(html)
    
    def send_html(self, html):
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_json(self):
        data = {
            'status': 'OK',
            'message': 'BINANCE CRM API Test',
            'port': PORT,
            'server': 'Fonctionnel'
        }
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(str(data).encode('utf-8'))

def start_test_server():
    print(f"🚀 Demarrage du serveur de test BINANCE CRM sur le port {PORT}...")
    
    try:
        with socketserver.TCPServer(("", PORT), TestHandler) as httpd:
            print(f"✅ Serveur de test demarre avec succes !")
            print(f"🌐 URL: http://localhost:{PORT}")
            print(f"🛑 Appuyez sur Ctrl+C pour arreter")
            
            # Ouvrir le navigateur automatiquement
            def open_browser():
                time.sleep(1)
                webbrowser.open(f'http://localhost:{PORT}')
            
            threading.Thread(target=open_browser, daemon=True).start()
            
            try:
                httpd.serve_forever()
            except KeyboardInterrupt:
                print(f"\n🛑 Serveur de test arrete")
    except Exception as e:
        print(f"❌ Erreur serveur: {e}")

if __name__ == "__main__":
    start_test_server()

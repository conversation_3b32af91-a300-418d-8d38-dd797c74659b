from pydantic import BaseModel, EmailStr, validator
from datetime import datetime
from typing import Optional, List
import re

# Schémas pour User
class UserBase(BaseModel):
    username: str
    email: EmailStr
    role: str = "vendeur"

class UserCreate(UserBase):
    password: str
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('Le mot de passe doit contenir au moins 6 caractères')
        return v

class UserUpdate(BaseModel):
    username: Optional[str] = None
    email: Optional[EmailStr] = None
    role: Optional[str] = None
    is_active: Optional[bool] = None

class User(UserBase):
    id: int
    created_at: datetime
    is_active: bool
    
    class Config:
        from_attributes = True

# Schémas pour Client
class ClientBase(BaseModel):
    nom: str
    prenom: str
    email: EmailStr
    telephone: str
    date_naissance: datetime
    adresse: Optional[str] = None
    
    @validator('telephone')
    def validate_telephone(cls, v):
        # Validation basique du numéro de téléphone
        if not re.match(r'^[\d\s\-\+\(\)\.]{8,20}$', v):
            raise ValueError('Format de téléphone invalide')
        return v

class ClientCreate(ClientBase):
    vendeur_id: Optional[int] = None
    indicateur: str = "nouveau"
    note: Optional[str] = None

class ClientUpdate(BaseModel):
    nom: Optional[str] = None
    prenom: Optional[str] = None
    email: Optional[EmailStr] = None
    telephone: Optional[str] = None
    date_naissance: Optional[datetime] = None
    adresse: Optional[str] = None
    vendeur_id: Optional[int] = None
    indicateur: Optional[str] = None
    note: Optional[str] = None
    email_envoye: Optional[bool] = None
    template_utilise: Optional[str] = None

class Client(ClientBase):
    id: int
    vendeur_id: Optional[int]
    indicateur: str
    note: Optional[str]
    email_envoye: bool
    template_utilise: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Schémas pour EmailTemplate
class EmailTemplateBase(BaseModel):
    nom: str
    sujet: str
    contenu: str
    variables: Optional[str] = None

class EmailTemplateCreate(EmailTemplateBase):
    pass

class EmailTemplateUpdate(BaseModel):
    nom: Optional[str] = None
    sujet: Optional[str] = None
    contenu: Optional[str] = None
    variables: Optional[str] = None

class EmailTemplate(EmailTemplateBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Schémas pour Appointment
class AppointmentBase(BaseModel):
    client_id: int
    date_rdv: datetime
    titre: str
    description: Optional[str] = None
    statut: str = "planifie"

class AppointmentCreate(AppointmentBase):
    vendeur_id: int

class AppointmentUpdate(BaseModel):
    date_rdv: Optional[datetime] = None
    titre: Optional[str] = None
    description: Optional[str] = None
    statut: Optional[str] = None

class Appointment(AppointmentBase):
    id: int
    vendeur_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Schémas pour EmailLog
class EmailLogBase(BaseModel):
    client_id: int
    sujet: str
    contenu: str
    statut: str = "envoye"

class EmailLogCreate(EmailLogBase):
    template_id: Optional[int] = None
    vendeur_id: int
    erreur_message: Optional[str] = None

class EmailLog(EmailLogBase):
    id: int
    template_id: Optional[int]
    vendeur_id: int
    erreur_message: Optional[str]
    date_envoi: datetime
    
    class Config:
        from_attributes = True

# Schémas pour SMTPConfig
class SMTPConfigBase(BaseModel):
    host: str
    port: int = 587
    username: str
    password: str
    use_tls: bool = True
    from_email: EmailStr
    from_name: str = "CRM System"

class SMTPConfigCreate(SMTPConfigBase):
    pass

class SMTPConfigUpdate(BaseModel):
    host: Optional[str] = None
    port: Optional[int] = None
    username: Optional[str] = None
    password: Optional[str] = None
    use_tls: Optional[bool] = None
    from_email: Optional[EmailStr] = None
    from_name: Optional[str] = None

class SMTPConfig(SMTPConfigBase):
    id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True

# Schémas pour l'authentification
class LoginRequest(BaseModel):
    username: str
    password: str

class Token(BaseModel):
    access_token: str
    token_type: str

# Schémas pour les statistiques
class DashboardStats(BaseModel):
    total_clients: int
    clients_attribues: int
    clients_non_attribues: int
    emails_envoyes: int
    rdv_planifies: int
    rdv_realises: int
    
# Schémas pour l'import CSV
class ImportResult(BaseModel):
    success: int
    errors: int
    details: List[str]

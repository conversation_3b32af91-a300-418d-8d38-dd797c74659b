from fastapi import Depends, HTTPException, status, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.orm import Session
from jose import JWTError, jwt
from datetime import datetime, timedelta
from typing import Optional
import os

import crud
import models
import schemas
from database import get_db

# Configuration JWT
SECRET_KEY = os.getenv("SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

security = HTTPBearer(auto_error=False)

class AuthenticationError(HTTPException):
    def __init__(self, detail: str = "Could not validate credentials"):
        super().__init__(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=detail,
            headers={"WWW-Authenticate": "Bearer"},
        )

class AuthorizationError(HTTPException):
    def __init__(self, detail: str = "Not enough permissions"):
        super().__init__(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=detail,
        )

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
    """Crée un token JWT"""
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=15)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt

def verify_token(token: str) -> Optional[dict]:
    """Vérifie et décode un token JWT"""
    try:
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        username: str = payload.get("sub")
        if username is None:
            return None
        return payload
    except JWTError:
        return None

def get_current_user_from_token(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    db: Session = Depends(get_db)
) -> models.User:
    """Récupère l'utilisateur actuel à partir du token JWT"""
    if not credentials:
        raise AuthenticationError("Token manquant")
    
    payload = verify_token(credentials.credentials)
    if not payload:
        raise AuthenticationError("Token invalide")
    
    username = payload.get("sub")
    if not username:
        raise AuthenticationError("Token invalide")
    
    user = crud.get_user_by_username(db, username=username)
    if user is None:
        raise AuthenticationError("Utilisateur non trouvé")
    
    if not user.is_active:
        raise AuthenticationError("Utilisateur inactif")
    
    return user

def get_current_user_from_session(request: Request, db: Session = Depends(get_db)) -> models.User:
    """Récupère l'utilisateur actuel à partir de la session"""
    user_id = request.session.get("user_id")
    if not user_id:
        raise AuthenticationError("Session expirée")
    
    user = crud.get_user(db, user_id=user_id)
    if user is None:
        raise AuthenticationError("Utilisateur non trouvé")
    
    if not user.is_active:
        raise AuthenticationError("Utilisateur inactif")
    
    return user

def require_admin(current_user: models.User = Depends(get_current_user_from_session)) -> models.User:
    """Vérifie que l'utilisateur actuel est admin"""
    if current_user.role != "admin":
        raise AuthorizationError("Accès réservé aux administrateurs")
    return current_user

def require_vendeur_or_admin(current_user: models.User = Depends(get_current_user_from_session)) -> models.User:
    """Vérifie que l'utilisateur actuel est vendeur ou admin"""
    if current_user.role not in ["admin", "vendeur"]:
        raise AuthorizationError("Accès réservé aux vendeurs et administrateurs")
    return current_user

def can_access_client(current_user: models.User, client: models.Client) -> bool:
    """Vérifie si l'utilisateur peut accéder à ce client"""
    if current_user.role == "admin":
        return True
    if current_user.role == "vendeur" and client.vendeur_id == current_user.id:
        return True
    return False

def require_client_access(client_id: int, db: Session = Depends(get_db)):
    """Decorator pour vérifier l'accès à un client spécifique"""
    def check_access(current_user: models.User = Depends(get_current_user_from_session)):
        client = crud.get_client(db, client_id)
        if not client:
            raise HTTPException(status_code=404, detail="Client non trouvé")
        
        if not can_access_client(current_user, client):
            raise AuthorizationError("Accès non autorisé à ce client")
        
        return current_user
    return check_access

def login_user(request: Request, user: models.User):
    """Connecte un utilisateur en créant une session"""
    request.session["user_id"] = user.id
    request.session["username"] = user.username
    request.session["role"] = user.role

def logout_user(request: Request):
    """Déconnecte un utilisateur en supprimant la session"""
    request.session.clear()

def is_authenticated(request: Request) -> bool:
    """Vérifie si l'utilisateur est authentifié"""
    return "user_id" in request.session

def get_user_role(request: Request) -> Optional[str]:
    """Récupère le rôle de l'utilisateur depuis la session"""
    return request.session.get("role")

# Middleware pour les templates
def get_template_context(request: Request) -> dict:
    """Récupère le contexte pour les templates (utilisateur connecté, etc.)"""
    context = {
        "request": request,
        "is_authenticated": is_authenticated(request),
        "user_role": get_user_role(request),
        "username": request.session.get("username")
    }
    return context

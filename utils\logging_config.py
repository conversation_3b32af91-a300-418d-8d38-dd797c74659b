"""
Configuration avancée des logs pour l'application CRM
"""

import logging
import logging.handlers
import os
import json
from datetime import datetime
from typing import Dict, Any, Optional
from pathlib import Path

class JSONFormatter(logging.Formatter):
    """
    Formateur JSON pour les logs structurés
    """
    
    def format(self, record):
        log_entry = {
            'timestamp': datetime.fromtimestamp(record.created).isoformat(),
            'level': record.levelname,
            'logger': record.name,
            'message': record.getMessage(),
            'module': record.module,
            'function': record.funcName,
            'line': record.lineno
        }
        
        # Ajouter les informations d'exception si présentes
        if record.exc_info:
            log_entry['exception'] = self.formatException(record.exc_info)
        
        # Ajouter les données personnalisées
        if hasattr(record, 'user_id'):
            log_entry['user_id'] = record.user_id
        
        if hasattr(record, 'client_ip'):
            log_entry['client_ip'] = record.client_ip
        
        if hasattr(record, 'action'):
            log_entry['action'] = record.action
        
        if hasattr(record, 'extra_data'):
            log_entry['extra_data'] = record.extra_data
        
        return json.dumps(log_entry, ensure_ascii=False)

class CRMLogger:
    """
    Logger personnalisé pour l'application CRM
    """
    
    def __init__(self, name: str = "crm"):
        self.logger = logging.getLogger(name)
        self.setup_logging()
    
    def setup_logging(self):
        """
        Configure les handlers de logging
        """
        # Créer le dossier de logs s'il n'existe pas
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Niveau de log depuis l'environnement
        log_level = os.getenv('LOG_LEVEL', 'INFO').upper()
        self.logger.setLevel(getattr(logging, log_level))
        
        # Handler pour la console (développement)
        if os.getenv('DEBUG', 'false').lower() == 'true':
            console_handler = logging.StreamHandler()
            console_formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            self.logger.addHandler(console_handler)
        
        # Handler pour le fichier principal
        file_handler = logging.handlers.RotatingFileHandler(
            log_dir / "crm.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=5,
            encoding='utf-8'
        )
        file_formatter = JSONFormatter()
        file_handler.setFormatter(file_formatter)
        self.logger.addHandler(file_handler)
        
        # Handler pour les erreurs
        error_handler = logging.handlers.RotatingFileHandler(
            log_dir / "errors.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=10,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(file_formatter)
        self.logger.addHandler(error_handler)
        
        # Handler pour les événements de sécurité
        security_handler = logging.handlers.RotatingFileHandler(
            log_dir / "security.log",
            maxBytes=10 * 1024 * 1024,  # 10MB
            backupCount=20,
            encoding='utf-8'
        )
        security_handler.setLevel(logging.WARNING)
        security_handler.setFormatter(file_formatter)
        
        # Créer un logger spécifique pour la sécurité
        security_logger = logging.getLogger("crm.security")
        security_logger.addHandler(security_handler)
        security_logger.setLevel(logging.WARNING)
    
    def log_user_action(self, user_id: int, action: str, details: Dict[str, Any], 
                       client_ip: Optional[str] = None, level: str = "INFO"):
        """
        Log une action utilisateur
        """
        extra = {
            'user_id': user_id,
            'action': action,
            'extra_data': details
        }
        
        if client_ip:
            extra['client_ip'] = client_ip
        
        log_level = getattr(logging, level.upper())
        self.logger.log(log_level, f"User action: {action}", extra=extra)
    
    def log_security_event(self, event_type: str, details: Dict[str, Any], 
                          client_ip: Optional[str] = None, user_id: Optional[int] = None):
        """
        Log un événement de sécurité
        """
        security_logger = logging.getLogger("crm.security")
        
        extra = {
            'action': event_type,
            'extra_data': details
        }
        
        if client_ip:
            extra['client_ip'] = client_ip
        
        if user_id:
            extra['user_id'] = user_id
        
        security_logger.warning(f"Security event: {event_type}", extra=extra)
    
    def log_email_event(self, event_type: str, recipient: str, template: str, 
                       success: bool, user_id: int, error: Optional[str] = None):
        """
        Log un événement d'email
        """
        details = {
            'recipient': recipient,
            'template': template,
            'success': success
        }
        
        if error:
            details['error'] = error
        
        level = "INFO" if success else "ERROR"
        self.log_user_action(user_id, f"email_{event_type}", details, level=level)
    
    def log_database_event(self, operation: str, table: str, record_id: Optional[int] = None,
                          user_id: Optional[int] = None, success: bool = True, 
                          error: Optional[str] = None):
        """
        Log un événement de base de données
        """
        details = {
            'operation': operation,
            'table': table,
            'success': success
        }
        
        if record_id:
            details['record_id'] = record_id
        
        if error:
            details['error'] = error
        
        level = "INFO" if success else "ERROR"
        action = f"db_{operation}_{table}"
        
        if user_id:
            self.log_user_action(user_id, action, details, level=level)
        else:
            extra = {
                'action': action,
                'extra_data': details
            }
            log_level = getattr(logging, level)
            self.logger.log(log_level, f"Database {operation} on {table}", extra=extra)
    
    def log_api_request(self, method: str, endpoint: str, user_id: Optional[int] = None,
                       client_ip: Optional[str] = None, status_code: int = 200,
                       response_time: Optional[float] = None):
        """
        Log une requête API
        """
        details = {
            'method': method,
            'endpoint': endpoint,
            'status_code': status_code
        }
        
        if response_time:
            details['response_time_ms'] = round(response_time * 1000, 2)
        
        level = "INFO" if status_code < 400 else "WARNING" if status_code < 500 else "ERROR"
        
        extra = {
            'action': 'api_request',
            'extra_data': details
        }
        
        if user_id:
            extra['user_id'] = user_id
        
        if client_ip:
            extra['client_ip'] = client_ip
        
        log_level = getattr(logging, level)
        self.logger.log(log_level, f"{method} {endpoint} - {status_code}", extra=extra)
    
    def log_performance_metric(self, metric_name: str, value: float, unit: str = "ms",
                              context: Optional[Dict[str, Any]] = None):
        """
        Log une métrique de performance
        """
        details = {
            'metric': metric_name,
            'value': value,
            'unit': unit
        }
        
        if context:
            details['context'] = context
        
        extra = {
            'action': 'performance_metric',
            'extra_data': details
        }
        
        self.logger.info(f"Performance: {metric_name} = {value}{unit}", extra=extra)
    
    def log_business_event(self, event_type: str, details: Dict[str, Any], 
                          user_id: Optional[int] = None):
        """
        Log un événement métier important
        """
        if user_id:
            self.log_user_action(user_id, f"business_{event_type}", details)
        else:
            extra = {
                'action': f"business_{event_type}",
                'extra_data': details
            }
            self.logger.info(f"Business event: {event_type}", extra=extra)

# Instance globale du logger
crm_logger = CRMLogger()

# Fonctions de convenance
def log_user_action(user_id: int, action: str, details: Dict[str, Any], 
                   client_ip: Optional[str] = None, level: str = "INFO"):
    """Log une action utilisateur"""
    crm_logger.log_user_action(user_id, action, details, client_ip, level)

def log_security_event(event_type: str, details: Dict[str, Any], 
                      client_ip: Optional[str] = None, user_id: Optional[int] = None):
    """Log un événement de sécurité"""
    crm_logger.log_security_event(event_type, details, client_ip, user_id)

def log_email_event(event_type: str, recipient: str, template: str, 
                   success: bool, user_id: int, error: Optional[str] = None):
    """Log un événement d'email"""
    crm_logger.log_email_event(event_type, recipient, template, success, user_id, error)

def log_database_event(operation: str, table: str, record_id: Optional[int] = None,
                      user_id: Optional[int] = None, success: bool = True, 
                      error: Optional[str] = None):
    """Log un événement de base de données"""
    crm_logger.log_database_event(operation, table, record_id, user_id, success, error)

def log_api_request(method: str, endpoint: str, user_id: Optional[int] = None,
                   client_ip: Optional[str] = None, status_code: int = 200,
                   response_time: Optional[float] = None):
    """Log une requête API"""
    crm_logger.log_api_request(method, endpoint, user_id, client_ip, status_code, response_time)

def log_performance_metric(metric_name: str, value: float, unit: str = "ms",
                          context: Optional[Dict[str, Any]] = None):
    """Log une métrique de performance"""
    crm_logger.log_performance_metric(metric_name, value, unit, context)

def log_business_event(event_type: str, details: Dict[str, Any], 
                      user_id: Optional[int] = None):
    """Log un événement métier"""
    crm_logger.log_business_event(event_type, details, user_id)

{% extends "base.html" %}

{% block title %}Dashboard Vendeur - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-speedometer2"></i> Mon Dashboard</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <a href="/vendeur/clients" class="btn btn-sm btn-primary">
                <i class="bi bi-people"></i> Mes Clients
            </a>
            <a href="/vendeur/agenda" class="btn btn-sm btn-outline-primary">
                <i class="bi bi-calendar"></i> Mon Agenda
            </a>
        </div>
    </div>
</div>

<!-- Statistiques du vendeur -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-stats text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Mes Clients</h5>
                        <h2 class="mb-0">{{ stats.total_clients }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Emails Envoyés</h5>
                        <h2 class="mb-0">{{ stats.emails_envoyes }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">RDV Planifiés</h5>
                        <h2 class="mb-0">{{ stats.rdv_planifies }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-plus fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">RDV Réalisés</h5>
                        <h2 class="mb-0">{{ stats.rdv_realises }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-calendar-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques par indicateur -->
{% if stats.indicateurs %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-bar-chart"></i> Mes Clients par Indicateur</h5>
            </div>
            <div class="card-body">
                {% for indicateur, count in stats.indicateurs.items() %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-secondary">{{ indicateur }}</span>
                    <span class="fw-bold">{{ count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightning"></i> Actions Rapides</h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <a href="/vendeur/clients" class="btn btn-outline-primary">
                        <i class="bi bi-people"></i> Voir tous mes clients
                    </a>
                    <a href="/vendeur/clients?indicateur=nouveau" class="btn btn-outline-warning">
                        <i class="bi bi-person-plus"></i> Nouveaux clients
                    </a>
                    <a href="/vendeur/agenda" class="btn btn-outline-info">
                        <i class="bi bi-calendar-plus"></i> Planifier un RDV
                    </a>
                    <a href="/api/clients/export" class="btn btn-outline-secondary">
                        <i class="bi bi-download"></i> Exporter mes clients
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Clients récents et RDV à venir -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-people"></i> Mes Clients Récents</h5>
            </div>
            <div class="card-body">
                {% if clients %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Indicateur</th>
                                <th>Email</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in clients %}
                            <tr>
                                <td>{{ client.prenom }} {{ client.nom }}</td>
                                <td>{{ client.email }}</td>
                                <td><span class="badge bg-secondary">{{ client.indicateur }}</span></td>
                                <td>
                                    {% if client.email_envoye %}
                                        <span class="badge bg-success"><i class="bi bi-check"></i></span>
                                    {% else %}
                                        <span class="badge bg-secondary">-</span>
                                    {% endif %}
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="text-center mt-2">
                    <a href="/vendeur/clients" class="btn btn-sm btn-outline-primary">Voir tous</a>
                </div>
                {% else %}
                <p class="text-muted">Aucun client attribué.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-calendar-event"></i> Rendez-vous à Venir</h5>
            </div>
            <div class="card-body">
                {% if upcoming_appointments %}
                <div class="list-group list-group-flush">
                    {% for appointment in upcoming_appointments %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ appointment.titre }}</h6>
                            <small>{{ appointment.date_rdv.strftime('%d/%m/%Y %H:%M') }}</small>
                        </div>
                        <p class="mb-1">
                            <strong>{{ appointment.client.prenom }} {{ appointment.client.nom }}</strong>
                        </p>
                        <small class="text-muted">{{ appointment.client.telephone }}</small>
                        {% if appointment.description %}
                        <br><small class="text-muted">{{ appointment.description[:50] }}...</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                <div class="text-center mt-2">
                    <a href="/vendeur/agenda" class="btn btn-sm btn-outline-primary">Voir l'agenda</a>
                </div>
                {% else %}
                <p class="text-muted">Aucun rendez-vous planifié cette semaine.</p>
                <div class="text-center">
                    <a href="/vendeur/agenda" class="btn btn-sm btn-primary">Planifier un RDV</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Conseils et astuces -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card border-info">
            <div class="card-header bg-info text-white">
                <h5><i class="bi bi-lightbulb"></i> Conseils pour optimiser votre travail</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <h6><i class="bi bi-envelope"></i> Emails</h6>
                        <ul class="list-unstyled">
                            <li>• Utilisez les templates pour gagner du temps</li>
                            <li>• Personnalisez avec les variables {prenom}, {nom}</li>
                            <li>• Suivez les envois dans l'historique</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="bi bi-tags"></i> Indicateurs</h6>
                        <ul class="list-unstyled">
                            <li>• Mettez à jour régulièrement les indicateurs</li>
                            <li>• Utilisez "magnifique" pour les prospects chauds</li>
                            <li>• "NRP" pour les non-répondants</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <h6><i class="bi bi-calendar"></i> Agenda</h6>
                        <ul class="list-unstyled">
                            <li>• Planifiez vos RDV à l'avance</li>
                            <li>• Ajoutez des notes détaillées</li>
                            <li>• Marquez les RDV comme "réalisés"</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

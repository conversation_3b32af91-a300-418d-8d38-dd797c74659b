#!/usr/bin/env python3
"""
BINANCE CRM - Système de Validation des Entrées
Validation robuste et sécurisée des données utilisateur
"""

import re
import json
from datetime import datetime
from typing import Dict, List, Any, Optional, Union

class ValidationError(Exception):
    """Exception pour les erreurs de validation"""
    def __init__(self, field: str, message: str):
        self.field = field
        self.message = message
        super().__init__(f"{field}: {message}")

class DataValidator:
    """Validateur de données avec règles configurables"""
    
    def __init__(self):
        self.email_pattern = re.compile(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$')
        self.phone_pattern = re.compile(r'^(\+33|0)[1-9](\d{8})$')
        self.name_pattern = re.compile(r'^[a-zA-ZÀ-ÿ\s\-\']{2,50}$')
        self.postal_code_pattern = re.compile(r'^\d{5}$')
        
        # Mots interdits pour prévenir les injections
        self.forbidden_words = [
            'script', 'javascript', 'vbscript', 'onload', 'onerror',
            'drop', 'delete', 'truncate', 'alter', 'create', 'insert',
            'update', 'select', 'union', 'exec', 'execute'
        ]
    
    def validate_email(self, email: str) -> str:
        """Valider un email"""
        if not email:
            raise ValidationError('email', 'Email requis')
        
        email = email.strip().lower()
        
        if len(email) > 254:
            raise ValidationError('email', 'Email trop long (max 254 caractères)')
        
        if not self.email_pattern.match(email):
            raise ValidationError('email', 'Format d\'email invalide')
        
        # Vérifier les domaines suspects
        suspicious_domains = ['tempmail', '10minutemail', 'guerrillamail']
        if any(domain in email for domain in suspicious_domains):
            raise ValidationError('email', 'Domaine email non autorisé')
        
        return email
    
    def validate_phone(self, phone: str) -> str:
        """Valider un numéro de téléphone français"""
        if not phone:
            return ""  # Téléphone optionnel
        
        phone = re.sub(r'[\s\-\.]', '', phone.strip())
        
        if not self.phone_pattern.match(phone):
            raise ValidationError('phone', 'Format de téléphone invalide (format français requis)')
        
        # Normaliser au format international
        if phone.startswith('0'):
            phone = '+33' + phone[1:]
        
        return phone
    
    def validate_name(self, name: str, field_name: str) -> str:
        """Valider un nom/prénom"""
        if not name:
            raise ValidationError(field_name, f'{field_name.capitalize()} requis')
        
        name = name.strip()
        
        if len(name) < 2:
            raise ValidationError(field_name, f'{field_name.capitalize()} trop court (min 2 caractères)')
        
        if len(name) > 50:
            raise ValidationError(field_name, f'{field_name.capitalize()} trop long (max 50 caractères)')
        
        if not self.name_pattern.match(name):
            raise ValidationError(field_name, f'{field_name.capitalize()} contient des caractères invalides')
        
        # Vérifier les mots interdits
        if any(word in name.lower() for word in self.forbidden_words):
            raise ValidationError(field_name, f'{field_name.capitalize()} contient des mots interdits')
        
        return name.title()
    
    def validate_postal_code(self, postal_code: str) -> str:
        """Valider un code postal français"""
        if not postal_code:
            return ""  # Code postal optionnel
        
        postal_code = postal_code.strip()
        
        if not self.postal_code_pattern.match(postal_code):
            raise ValidationError('postal_code', 'Code postal invalide (5 chiffres requis)')
        
        return postal_code
    
    def validate_status(self, status: str, allowed_statuses: List[str]) -> str:
        """Valider un statut"""
        if not status:
            raise ValidationError('status', 'Statut requis')
        
        status = status.strip().lower()
        
        if status not in allowed_statuses:
            raise ValidationError('status', f'Statut invalide. Valeurs autorisées: {", ".join(allowed_statuses)}')
        
        return status
    
    def validate_date(self, date_str: str, field_name: str) -> Optional[str]:
        """Valider une date"""
        if not date_str:
            return None
        
        date_str = date_str.strip()
        
        # Formats acceptés
        date_formats = ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y']
        
        for fmt in date_formats:
            try:
                parsed_date = datetime.strptime(date_str, fmt)
                
                # Vérifier que la date n'est pas dans le futur (pour date de naissance)
                if field_name == 'birth_date' and parsed_date > datetime.now():
                    raise ValidationError(field_name, 'Date de naissance ne peut pas être dans le futur')
                
                # Vérifier que la date n'est pas trop ancienne
                if field_name == 'birth_date' and parsed_date.year < 1900:
                    raise ValidationError(field_name, 'Date de naissance trop ancienne')
                
                return parsed_date.strftime('%Y-%m-%d')
            except ValueError:
                continue
        
        raise ValidationError(field_name, 'Format de date invalide (YYYY-MM-DD, DD/MM/YYYY ou DD-MM-YYYY)')
    
    def validate_text(self, text: str, field_name: str, max_length: int = 1000, required: bool = False) -> str:
        """Valider un champ texte"""
        if not text:
            if required:
                raise ValidationError(field_name, f'{field_name.capitalize()} requis')
            return ""
        
        text = text.strip()
        
        if len(text) > max_length:
            raise ValidationError(field_name, f'{field_name.capitalize()} trop long (max {max_length} caractères)')
        
        # Vérifier les mots interdits
        text_lower = text.lower()
        for word in self.forbidden_words:
            if word in text_lower:
                raise ValidationError(field_name, f'{field_name.capitalize()} contient des mots interdits')
        
        # Vérifier les balises HTML/script
        if '<' in text and '>' in text:
            raise ValidationError(field_name, f'{field_name.capitalize()} ne peut pas contenir de balises HTML')
        
        return text
    
    def validate_integer(self, value: Union[str, int], field_name: str, min_val: int = None, max_val: int = None) -> int:
        """Valider un entier"""
        if value is None or value == "":
            raise ValidationError(field_name, f'{field_name.capitalize()} requis')
        
        try:
            int_value = int(value)
        except (ValueError, TypeError):
            raise ValidationError(field_name, f'{field_name.capitalize()} doit être un nombre entier')
        
        if min_val is not None and int_value < min_val:
            raise ValidationError(field_name, f'{field_name.capitalize()} doit être >= {min_val}')
        
        if max_val is not None and int_value > max_val:
            raise ValidationError(field_name, f'{field_name.capitalize()} doit être <= {max_val}')
        
        return int_value

class ClientValidator(DataValidator):
    """Validateur spécialisé pour les clients"""
    
    def validate_client_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Valider les données d'un client"""
        validated_data = {}
        errors = []
        
        try:
            validated_data['first_name'] = self.validate_name(data.get('first_name', ''), 'prénom')
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['last_name'] = self.validate_name(data.get('last_name', ''), 'nom')
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['email'] = self.validate_email(data.get('email', ''))
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['phone'] = self.validate_phone(data.get('phone', ''))
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['birth_date'] = self.validate_date(data.get('birth_date', ''), 'birth_date')
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['address'] = self.validate_text(data.get('address', ''), 'adresse', max_length=200)
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['postal_code'] = self.validate_postal_code(data.get('postal_code', ''))
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['city'] = self.validate_text(data.get('city', ''), 'ville', max_length=100)
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            allowed_statuses = ['nouveau', 'prospect', 'en_cours', 'client', 'inactif']
            validated_data['status'] = self.validate_status(data.get('status', ''), allowed_statuses)
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            if data.get('assigned_to'):
                validated_data['assigned_to'] = self.validate_integer(data.get('assigned_to'), 'vendeur assigné', min_val=1)
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['notes'] = self.validate_text(data.get('notes', ''), 'notes', max_length=2000)
        except ValidationError as e:
            errors.append(str(e))
        
        if errors:
            raise ValidationError('validation', '; '.join(errors))
        
        return validated_data

class VendeurValidator(DataValidator):
    """Validateur spécialisé pour les vendeurs"""
    
    def validate_vendeur_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Valider les données d'un vendeur"""
        validated_data = {}
        errors = []
        
        try:
            validated_data['first_name'] = self.validate_name(data.get('prenom', ''), 'prénom')
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['last_name'] = self.validate_name(data.get('nom', ''), 'nom')
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['email'] = self.validate_email(data.get('email', ''))
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['phone'] = self.validate_phone(data.get('telephone', ''))
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            allowed_statuses = ['actif', 'inactif', 'formation']
            validated_data['status'] = self.validate_status(data.get('statut', ''), allowed_statuses)
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            allowed_territories = ['paris', 'lyon', 'marseille', 'national']
            validated_data['territory'] = self.validate_status(data.get('territoire', ''), allowed_territories)
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['hire_date'] = self.validate_date(data.get('dateEmbauche', ''), 'hire_date')
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['monthly_target'] = self.validate_integer(data.get('objectif', 0), 'objectif mensuel', min_val=0, max_val=1000000)
        except ValidationError as e:
            errors.append(str(e))
        
        try:
            validated_data['notes'] = self.validate_text(data.get('notes', ''), 'notes', max_length=2000)
        except ValidationError as e:
            errors.append(str(e))
        
        if errors:
            raise ValidationError('validation', '; '.join(errors))
        
        return validated_data

# Instances globales des validateurs
client_validator = ClientValidator()
vendeur_validator = VendeurValidator()

# Fonctions utilitaires
def validate_client(data: Dict[str, Any]) -> Dict[str, Any]:
    """Fonction utilitaire pour valider un client"""
    return client_validator.validate_client_data(data)

def validate_vendeur(data: Dict[str, Any]) -> Dict[str, Any]:
    """Fonction utilitaire pour valider un vendeur"""
    return vendeur_validator.validate_vendeur_data(data)

# Test du système
if __name__ == "__main__":
    # Test validation client
    test_client = {
        'first_name': 'Jean',
        'last_name': 'Dupont',
        'email': '<EMAIL>',
        'phone': '0123456789',
        'status': 'prospect'
    }
    
    try:
        validated = validate_client(test_client)
        print("✅ Validation client réussie:", validated)
    except ValidationError as e:
        print("❌ Erreur validation client:", e)
    
    # Test validation vendeur
    test_vendeur = {
        'prenom': 'Marie',
        'nom': 'Martin',
        'email': '<EMAIL>',
        'telephone': '0987654321',
        'statut': 'actif',
        'territoire': 'paris',
        'objectif': 50000
    }
    
    try:
        validated = validate_vendeur(test_vendeur)
        print("✅ Validation vendeur réussie:", validated)
    except ValidationError as e:
        print("❌ Erreur validation vendeur:", e)

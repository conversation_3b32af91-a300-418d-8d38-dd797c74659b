#!/usr/bin/env python3
"""
Script de démarrage rapide pour le CRM sans dépendances externes
"""

import os
import sys
import sqlite3
from datetime import datetime
import hashlib

def create_simple_database():
    """Créer une base de données SQLite simple"""
    print("🗄️ Création de la base de données...")
    
    conn = sqlite3.connect('crm.db')
    cursor = conn.cursor()
    
    # Créer la table des utilisateurs
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'vendeur',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Créer la table des clients
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            prenom TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            telephone TEXT,
            date_naissance DATE,
            adresse TEXT,
            vendeur_id INTEGER,
            indicateur TEXT DEFAULT 'nouveau',
            note TEXT,
            email_envoye BOOLEAN DEFAULT 0,
            template_utilise TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')
    
    # Créer un utilisateur admin par défaut
    admin_password = "admin123"
    password_hash = hashlib.sha256(admin_password.encode()).hexdigest()
    
    cursor.execute('''
        INSERT OR IGNORE INTO users (username, email, password_hash, role)
        VALUES (?, ?, ?, ?)
    ''', ('admin', '<EMAIL>', password_hash, 'admin'))
    
    # Créer quelques vendeurs de démonstration
    vendeurs = [
        ('marie.martin', '<EMAIL>', 'vendeur123'),
        ('pierre.durand', '<EMAIL>', 'vendeur123'),
        ('sophie.bernard', '<EMAIL>', 'vendeur123')
    ]
    
    for username, email, password in vendeurs:
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            INSERT OR IGNORE INTO users (username, email, password_hash, role)
            VALUES (?, ?, ?, ?)
        ''', (username, email, password_hash, 'vendeur'))
    
    # Créer quelques clients de démonstration
    clients = [
        ('Dupont', 'Jean', '<EMAIL>', '01.23.45.67.89', '1980-05-15', '123 Rue de la Paix, 75001 Paris', 2, 'nouveau'),
        ('Martin', 'Marie', '<EMAIL>', '01.23.45.67.90', '1975-08-22', '456 Avenue des Champs, 69000 Lyon', 2, 'en cours'),
        ('Bernard', 'Pierre', '<EMAIL>', '01.23.45.67.91', '1985-12-03', '789 Boulevard du Centre, 13000 Marseille', 3, 'magnifique'),
        ('Durand', 'Sophie', '<EMAIL>', '01.23.45.67.92', '1990-03-18', '321 Place de la République, 31000 Toulouse', 3, 'NRP'),
        ('Moreau', 'Luc', '<EMAIL>', '01.23.45.67.93', '1978-11-07', '654 Rue du Commerce, 44000 Nantes', 4, 'client mort')
    ]
    
    for client in clients:
        cursor.execute('''
            INSERT OR IGNORE INTO clients 
            (nom, prenom, email, telephone, date_naissance, adresse, vendeur_id, indicateur)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', client)
    
    conn.commit()
    conn.close()
    
    print("✅ Base de données créée avec succès!")
    return True

def create_simple_server():
    """Créer un serveur HTTP simple pour démonstration"""
    print("🌐 Création du serveur de démonstration...")
    
    html_content = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Démonstration</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); min-height: 100vh; }
        .card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
        .feature-card { transition: transform 0.3s ease; }
        .feature-card:hover { transform: translateY(-5px); }
    </style>
</head>
<body>
    <div class="container py-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-5">
                            <h1 class="display-4 text-primary mb-3">
                                <i class="bi bi-building"></i> CRM System
                            </h1>
                            <p class="lead">Système de gestion de la relation client complet</p>
                            <div class="alert alert-success">
                                <i class="bi bi-check-circle"></i>
                                <strong>Application créée avec succès !</strong>
                            </div>
                        </div>
                        
                        <div class="row mb-5">
                            <div class="col-md-6">
                                <div class="card feature-card h-100 border-primary">
                                    <div class="card-body text-center">
                                        <i class="bi bi-person-check fs-1 text-primary mb-3"></i>
                                        <h5>Compte Administrateur</h5>
                                        <p class="text-muted">Accès complet au système</p>
                                        <div class="bg-light p-3 rounded">
                                            <strong>Utilisateur:</strong> admin<br>
                                            <strong>Mot de passe:</strong> admin123
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="card feature-card h-100 border-success">
                                    <div class="card-body text-center">
                                        <i class="bi bi-people fs-1 text-success mb-3"></i>
                                        <h5>Comptes Vendeurs</h5>
                                        <p class="text-muted">Accès aux clients attribués</p>
                                        <div class="bg-light p-3 rounded small">
                                            <strong>marie.martin</strong> / vendeur123<br>
                                            <strong>pierre.durand</strong> / vendeur123<br>
                                            <strong>sophie.bernard</strong> / vendeur123
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-4">
                            <div class="col-md-4">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-database fs-1 text-info mb-3"></i>
                                        <h6>Base de Données</h6>
                                        <p class="small text-muted">SQLite avec données de démonstration</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-envelope fs-1 text-warning mb-3"></i>
                                        <h6>Système d'Emails</h6>
                                        <p class="small text-muted">Templates et envoi automatisé</p>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card feature-card h-100">
                                    <div class="card-body text-center">
                                        <i class="bi bi-graph-up fs-1 text-danger mb-3"></i>
                                        <h6>Statistiques</h6>
                                        <p class="small text-muted">Rapports et analyses détaillées</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center">
                            <h5 class="mb-3">🚀 Pour démarrer l'application complète :</h5>
                            <div class="bg-dark text-light p-3 rounded mb-3">
                                <code>
                                    # Windows<br>
                                    start.bat dev<br><br>
                                    # Linux/Mac<br>
                                    chmod +x start.sh<br>
                                    ./start.sh dev
                                </code>
                            </div>
                            <p class="text-muted">
                                <i class="bi bi-info-circle"></i>
                                Assurez-vous d'avoir Python 3.8+ et pip installés
                            </p>
                        </div>
                        
                        <div class="row mt-4">
                            <div class="col-md-6">
                                <h6><i class="bi bi-list-check"></i> Fonctionnalités Principales</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check text-success"></i> Gestion des clients/leads</li>
                                    <li><i class="bi bi-check text-success"></i> Système d'emails avec templates</li>
                                    <li><i class="bi bi-check text-success"></i> Agenda et rendez-vous</li>
                                    <li><i class="bi bi-check text-success"></i> Import/export CSV</li>
                                    <li><i class="bi bi-check text-success"></i> Statistiques avancées</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h6><i class="bi bi-shield-check"></i> Sécurité & Performance</h6>
                                <ul class="list-unstyled">
                                    <li><i class="bi bi-check text-success"></i> Authentification sécurisée</li>
                                    <li><i class="bi bi-check text-success"></i> Rôles utilisateurs</li>
                                    <li><i class="bi bi-check text-success"></i> API REST complète</li>
                                    <li><i class="bi bi-check text-success"></i> Interface responsive</li>
                                    <li><i class="bi bi-check text-success"></i> Logs et monitoring</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
    '''
    
    # Créer le fichier HTML de démonstration
    with open('demo.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("✅ Page de démonstration créée: demo.html")
    return True

def main():
    """Fonction principale"""
    print("🚀 Démarrage rapide du CRM System")
    print("=" * 50)
    
    # Créer les dossiers nécessaires
    os.makedirs('logs', exist_ok=True)
    os.makedirs('static/uploads', exist_ok=True)
    
    # Créer la base de données
    if create_simple_database():
        print("\n✅ Base de données initialisée avec succès!")
    
    # Créer la page de démonstration
    if create_simple_server():
        print("\n✅ Page de démonstration créée!")
    
    print("\n" + "=" * 50)
    print("🎉 CRM System prêt !")
    print("\n📋 INFORMATIONS DE CONNEXION:")
    print("👑 ADMINISTRATEUR:")
    print("   Utilisateur: admin")
    print("   Mot de passe: admin123")
    print("\n👤 VENDEURS DE DÉMONSTRATION:")
    print("   marie.martin / vendeur123")
    print("   pierre.durand / vendeur123") 
    print("   sophie.bernard / vendeur123")
    
    print("\n🌐 ACCÈS:")
    print("   Démonstration: Ouvrez demo.html dans votre navigateur")
    print("   Application complète: Lancez start.bat (Windows) ou start.sh (Linux/Mac)")
    
    print("\n📚 DOCUMENTATION:")
    print("   README.md - Vue d'ensemble")
    print("   GUIDE_UTILISATEUR.md - Manuel utilisateur")
    print("   INSTALLATION.md - Guide d'installation")
    
    print("\n🔧 POUR DÉMARRER L'APPLICATION COMPLÈTE:")
    print("   1. Installez Python 3.8+ et pip")
    print("   2. Exécutez: pip install -r requirements.txt")
    print("   3. Lancez: python main.py ou uvicorn main:app --reload")
    print("   4. Accédez à: http://localhost:8000")

if __name__ == "__main__":
    main()

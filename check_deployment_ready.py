#!/usr/bin/env python3
"""
BINANCE CRM - Vérification Pré-Déploiement
Script pour vérifier que le système est prêt pour le déploiement
"""

import os
import sys
import json
import subprocess

class DeploymentChecker:
    def __init__(self):
        self.checks = []
        self.errors = []
        self.warnings = []
        
    def check_required_files(self):
        """Vérifier la présence des fichiers requis pour le déploiement"""
        print("📁 Vérification des fichiers requis...")
        
        required_files = [
            'start_production.py',
            'requirements.txt',
            'railway.toml',
            'Procfile',
            'database_server.py',
            'email_server.py',
            'pdf_server.py',
            'index.html',
            'clients.html',
            'dashboard.html'
        ]
        
        missing_files = []
        for file in required_files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} - MANQUANT")
                missing_files.append(file)
        
        if missing_files:
            self.errors.append(f"Fichiers manquants: {', '.join(missing_files)}")
        else:
            self.checks.append("Tous les fichiers requis sont présents")
    
    def check_python_dependencies(self):
        """Vérifier les dépendances Python"""
        print("🐍 Vérification des dépendances Python...")
        
        try:
            with open('requirements.txt', 'r') as f:
                requirements = f.read()
            
            # Vérifier les dépendances critiques
            critical_deps = ['reportlab', 'psutil', 'requests']
            missing_deps = []
            
            for dep in critical_deps:
                if dep not in requirements:
                    missing_deps.append(dep)
                else:
                    print(f"   ✅ {dep}")
            
            if missing_deps:
                self.errors.append(f"Dépendances manquantes: {', '.join(missing_deps)}")
            else:
                self.checks.append("Toutes les dépendances critiques sont présentes")
                
        except FileNotFoundError:
            self.errors.append("Fichier requirements.txt manquant")
    
    def check_database_structure(self):
        """Vérifier la structure de la base de données"""
        print("🗄️ Vérification de la base de données...")
        
        try:
            import sqlite3
            
            # Créer la base si elle n'existe pas
            if not os.path.exists('binance_crm.db'):
                print("   🔧 Création de la base de données...")
                from database_server import DatabaseManager
                db_manager = DatabaseManager()
                db_manager.init_database()
            
            # Vérifier les tables
            conn = sqlite3.connect('binance_crm.db')
            cursor = conn.cursor()
            
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['clients', 'users', 'email_templates', 'email_campaigns']
            missing_tables = [table for table in required_tables if table not in tables]
            
            if missing_tables:
                self.errors.append(f"Tables manquantes: {', '.join(missing_tables)}")
            else:
                print(f"   ✅ {len(tables)} tables présentes")
                self.checks.append("Structure de base de données valide")
            
            conn.close()
            
        except Exception as e:
            self.errors.append(f"Erreur base de données: {str(e)}")
    
    def check_configuration_files(self):
        """Vérifier les fichiers de configuration"""
        print("⚙️ Vérification des configurations...")
        
        # Vérifier railway.toml
        if os.path.exists('railway.toml'):
            try:
                with open('railway.toml', 'r') as f:
                    content = f.read()
                if 'startCommand' in content and 'start_production.py' in content:
                    print("   ✅ railway.toml configuré correctement")
                    self.checks.append("Configuration Railway valide")
                else:
                    self.warnings.append("Configuration Railway incomplète")
            except Exception as e:
                self.errors.append(f"Erreur lecture railway.toml: {str(e)}")
        
        # Vérifier Procfile
        if os.path.exists('Procfile'):
            try:
                with open('Procfile', 'r') as f:
                    content = f.read()
                if 'web:' in content and 'start_production.py' in content:
                    print("   ✅ Procfile configuré correctement")
                    self.checks.append("Configuration Procfile valide")
                else:
                    self.warnings.append("Configuration Procfile incomplète")
            except Exception as e:
                self.errors.append(f"Erreur lecture Procfile: {str(e)}")
    
    def check_production_script(self):
        """Vérifier le script de production"""
        print("🚀 Vérification du script de production...")
        
        try:
            with open('start_production.py', 'r') as f:
                content = f.read()
            
            # Vérifications critiques
            checks = [
                ('PORT = int(os.environ.get(\'PORT\'', 'Configuration du port'),
                ('HOST = \'0.0.0.0\'', 'Configuration de l\'host'),
                ('def main():', 'Fonction principale'),
                ('HTTPServer', 'Serveur HTTP')
            ]
            
            for check, description in checks:
                if check in content:
                    print(f"   ✅ {description}")
                else:
                    self.errors.append(f"Script de production: {description} manquant")
            
            if not self.errors:
                self.checks.append("Script de production valide")
                
        except FileNotFoundError:
            self.errors.append("Script start_production.py manquant")
    
    def check_git_repository(self):
        """Vérifier le repository Git"""
        print("📦 Vérification du repository Git...")
        
        if os.path.exists('.git'):
            try:
                # Vérifier le statut Git
                result = subprocess.run(['git', 'status', '--porcelain'], 
                                      capture_output=True, text=True)
                
                if result.returncode == 0:
                    if result.stdout.strip():
                        self.warnings.append("Modifications non commitées détectées")
                        print("   ⚠️ Modifications non commitées")
                    else:
                        print("   ✅ Repository Git propre")
                        self.checks.append("Repository Git à jour")
                else:
                    self.warnings.append("Impossible de vérifier le statut Git")
                    
            except FileNotFoundError:
                self.warnings.append("Git non installé")
        else:
            self.warnings.append("Pas de repository Git initialisé")
    
    def check_file_sizes(self):
        """Vérifier les tailles de fichiers"""
        print("📏 Vérification des tailles de fichiers...")
        
        large_files = []
        total_size = 0
        
        for root, dirs, files in os.walk('.'):
            # Ignorer les dossiers cachés et __pycache__
            dirs[:] = [d for d in dirs if not d.startswith('.') and d != '__pycache__']
            
            for file in files:
                if not file.startswith('.') and not file.endswith('.pyc'):
                    file_path = os.path.join(root, file)
                    size = os.path.getsize(file_path)
                    total_size += size
                    
                    # Fichiers > 5MB
                    if size > 5 * 1024 * 1024:
                        large_files.append((file_path, size // (1024 * 1024)))
        
        print(f"   📊 Taille totale du projet: {total_size // (1024 * 1024)} MB")
        
        if large_files:
            self.warnings.append(f"Fichiers volumineux détectés: {large_files}")
        
        if total_size > 100 * 1024 * 1024:  # > 100MB
            self.warnings.append("Projet très volumineux (>100MB)")
        else:
            self.checks.append("Taille du projet acceptable")
    
    def run_all_checks(self):
        """Exécuter toutes les vérifications"""
        print("🔍 VÉRIFICATION PRÉ-DÉPLOIEMENT - BINANCE CRM")
        print("="*60)
        
        self.check_required_files()
        print()
        self.check_python_dependencies()
        print()
        self.check_database_structure()
        print()
        self.check_configuration_files()
        print()
        self.check_production_script()
        print()
        self.check_git_repository()
        print()
        self.check_file_sizes()
        print()
        
        self.print_summary()
    
    def print_summary(self):
        """Afficher le résumé des vérifications"""
        print("="*60)
        print("📊 RÉSUMÉ DES VÉRIFICATIONS")
        print("="*60)
        
        print(f"✅ Vérifications réussies: {len(self.checks)}")
        for check in self.checks:
            print(f"   • {check}")
        
        if self.warnings:
            print(f"\n⚠️ Avertissements: {len(self.warnings)}")
            for warning in self.warnings:
                print(f"   • {warning}")
        
        if self.errors:
            print(f"\n❌ Erreurs: {len(self.errors)}")
            for error in self.errors:
                print(f"   • {error}")
        
        print(f"\n🎯 STATUT FINAL:")
        if not self.errors:
            if not self.warnings:
                print("✅ PRÊT POUR LE DÉPLOIEMENT!")
                print("🚀 Votre CRM peut être déployé sans problème")
                print("\n📋 ÉTAPES SUIVANTES:")
                print("1. git add . && git commit -m 'Ready for deployment'")
                print("2. git push origin main")
                print("3. Déployer sur Railway/Render/PythonAnywhere")
            else:
                print("⚠️ PRÊT AVEC AVERTISSEMENTS")
                print("🔧 Quelques optimisations recommandées mais pas bloquantes")
        else:
            print("❌ CORRECTIONS NÉCESSAIRES")
            print("🛠️ Veuillez corriger les erreurs avant le déploiement")
        
        # Sauvegarder le rapport
        report = {
            'timestamp': '2024-12-19T15:30:00Z',
            'checks_passed': len(self.checks),
            'warnings': len(self.warnings),
            'errors': len(self.errors),
            'ready_for_deployment': len(self.errors) == 0,
            'details': {
                'checks': self.checks,
                'warnings': self.warnings,
                'errors': self.errors
            }
        }
        
        with open('deployment_check_report.json', 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Rapport détaillé sauvegardé: deployment_check_report.json")

if __name__ == "__main__":
    checker = DeploymentChecker()
    checker.run_all_checks()

#!/usr/bin/env python3
"""
CRM System - Version simplifiée sans dépendances externes
Utilise seulement les modules Python standard
"""

import http.server
import socketserver
import sqlite3
import json
import urllib.parse
import hashlib
import os
from datetime import datetime
import webbrowser
import threading
import time

# Configuration
PORT = 8000
DB_NAME = 'crm_simple.db'

class CRMHandler(http.server.SimpleHTTPRequestHandler):
    """Handler HTTP personnalisé pour le CRM"""
    
    def do_GET(self):
        """Gérer les requêtes GET"""
        if self.path == '/' or self.path == '/login':
            self.send_login_page()
        elif self.path == '/dashboard':
            self.send_dashboard()
        elif self.path == '/clients':
            self.send_clients_page()
        elif self.path == '/api/stats':
            self.send_stats()
        else:
            self.send_error(404, "Page non trouvée")
    
    def do_POST(self):
        """G<PERSON>rer les requêtes POST"""
        if self.path == '/login':
            self.handle_login()
        else:
            self.send_error(404, "Endpoint non trouvé")
    
    def send_login_page(self):
        """Envoyer la page de connexion"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Connexion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
        }
        .card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); }
        .btn-primary { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border: none; }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h1 class="h3"><i class="bi bi-building"></i> CRM System</h1>
                            <p class="text-muted">Connexion à votre espace</p>
                        </div>
                        
                        <form method="post" action="/login">
                            <div class="mb-3">
                                <label for="username" class="form-label">Nom d'utilisateur</label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">Mot de passe</label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">Se connecter</button>
                        </form>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <strong>Comptes de démonstration :</strong><br>
                                👑 Admin: <code>admin</code> / <code>admin123</code><br>
                                👤 Vendeur: <code>marie.martin</code> / <code>vendeur123</code>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_dashboard(self):
        """Envoyer le dashboard"""
        stats = get_stats()
        
        html = f'''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - CRM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {{ background-color: #f8f9fa; }}
        .card {{ border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .stat-card {{ transition: transform 0.2s; }}
        .stat-card:hover {{ transform: translateY(-2px); }}
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="/dashboard">
                <i class="bi bi-building"></i> CRM System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/clients">Clients</a>
                <a class="nav-link" href="/login">Déconnexion</a>
            </div>
        </div>
    </nav>
    
    <div class="container mt-4">
        <h1 class="mb-4">Dashboard</h1>
        
        <div class="row">
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-people fs-1 text-primary"></i>
                        <h3 class="mt-2">{stats['total_clients']}</h3>
                        <p class="text-muted">Clients Total</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-person-check fs-1 text-success"></i>
                        <h3 class="mt-2">{stats['clients_attribues']}</h3>
                        <p class="text-muted">Clients Attribués</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-envelope fs-1 text-info"></i>
                        <h3 class="mt-2">{stats['emails_envoyes']}</h3>
                        <p class="text-muted">Emails Envoyés</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-calendar-check fs-1 text-warning"></i>
                        <h3 class="mt-2">{stats['rdv_planifies']}</h3>
                        <p class="text-muted">RDV Planifiés</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row mt-4">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-list"></i> Derniers Clients</h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Nom</th>
                                        <th>Email</th>
                                        <th>Téléphone</th>
                                        <th>Indicateur</th>
                                        <th>Date Création</th>
                                    </tr>
                                </thead>
                                <tbody id="clients-table">
                                    <!-- Données chargées par JavaScript -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="alert alert-success mt-4">
            <h5><i class="bi bi-check-circle"></i> CRM System Fonctionnel !</h5>
            <p class="mb-0">
                ✅ Base de données SQLite initialisée<br>
                ✅ Interface web responsive<br>
                ✅ Données de démonstration chargées<br>
                ✅ Authentification fonctionnelle
            </p>
        </div>
    </div>
    
    <script>
        // Charger les clients
        fetch('/api/clients')
            .then(response => response.json())
            .then(clients => {{
                const tbody = document.getElementById('clients-table');
                clients.forEach(client => {{
                    const row = `
                        <tr>
                            <td>${{client.prenom}} ${{client.nom}}</td>
                            <td>${{client.email}}</td>
                            <td>${{client.telephone}}</td>
                            <td><span class="badge bg-primary">${{client.indicateur}}</span></td>
                            <td>${{client.created_at}}</td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                }});
            }})
            .catch(error => {{
                document.getElementById('clients-table').innerHTML = 
                    '<tr><td colspan="5" class="text-center text-muted">Erreur de chargement des données</td></tr>';
            }});
    </script>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_stats(self):
        """Envoyer les statistiques en JSON"""
        stats = get_stats()
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(stats).encode('utf-8'))
    
    def handle_login(self):
        """Gérer la connexion"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        params = urllib.parse.parse_qs(post_data)
        
        username = params.get('username', [''])[0]
        password = params.get('password', [''])[0]
        
        if authenticate_user(username, password):
            # Redirection vers le dashboard
            self.send_response(302)
            self.send_header('Location', '/dashboard')
            self.end_headers()
        else:
            # Retour à la page de connexion avec erreur
            self.send_response(302)
            self.send_header('Location', '/login?error=1')
            self.end_headers()

def init_database():
    """Initialiser la base de données"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    # Créer les tables
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'vendeur',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            prenom TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            telephone TEXT,
            date_naissance DATE,
            adresse TEXT,
            vendeur_id INTEGER,
            indicateur TEXT DEFAULT 'nouveau',
            note TEXT,
            email_envoye BOOLEAN DEFAULT 0,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')
    
    # Créer les utilisateurs par défaut
    users = [
        ('admin', '<EMAIL>', hashlib.sha256('admin123'.encode()).hexdigest(), 'admin'),
        ('marie.martin', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur'),
        ('pierre.durand', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur'),
        ('sophie.bernard', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur')
    ]
    
    for user in users:
        cursor.execute('INSERT OR IGNORE INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)', user)
    
    # Créer des clients de démonstration
    clients = [
        ('Dupont', 'Jean', '<EMAIL>', '01.23.45.67.89', '1980-05-15', '123 Rue de la Paix, Paris', 2, 'nouveau'),
        ('Martin', 'Marie', '<EMAIL>', '01.23.45.67.90', '1975-08-22', '456 Avenue des Champs, Lyon', 2, 'en cours'),
        ('Bernard', 'Pierre', '<EMAIL>', '01.23.45.67.91', '1985-12-03', '789 Boulevard du Centre, Marseille', 3, 'magnifique'),
        ('Durand', 'Sophie', '<EMAIL>', '01.23.45.67.92', '1990-03-18', '321 Place de la République, Toulouse', 3, 'NRP'),
        ('Moreau', 'Luc', '<EMAIL>', '01.23.45.67.93', '1978-11-07', '654 Rue du Commerce, Nantes', 4, 'client mort')
    ]
    
    for client in clients:
        cursor.execute('''
            INSERT OR IGNORE INTO clients 
            (nom, prenom, email, telephone, date_naissance, adresse, vendeur_id, indicateur)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', client)
    
    conn.commit()
    conn.close()
    print("✅ Base de données initialisée avec succès!")

def authenticate_user(username, password):
    """Authentifier un utilisateur"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    cursor.execute('SELECT id FROM users WHERE username = ? AND password_hash = ?', (username, password_hash))
    result = cursor.fetchone()
    
    conn.close()
    return result is not None

def get_stats():
    """Récupérer les statistiques"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM clients')
    total_clients = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM clients WHERE vendeur_id IS NOT NULL')
    clients_attribues = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM clients WHERE email_envoye = 1')
    emails_envoyes = cursor.fetchone()[0]
    
    conn.close()
    
    return {
        'total_clients': total_clients,
        'clients_attribues': clients_attribues,
        'emails_envoyes': emails_envoyes,
        'rdv_planifies': 5  # Valeur fixe pour la démo
    }

def start_server():
    """Démarrer le serveur HTTP"""
    print(f"🚀 Démarrage du serveur CRM sur le port {PORT}...")
    
    # Initialiser la base de données
    init_database()
    
    # Créer et démarrer le serveur
    with socketserver.TCPServer(("", PORT), CRMHandler) as httpd:
        print(f"✅ Serveur démarré avec succès!")
        print(f"🌐 Accès: http://localhost:{PORT}")
        print(f"👑 Admin: admin / admin123")
        print(f"👤 Vendeur: marie.martin / vendeur123")
        print(f"🛑 Appuyez sur Ctrl+C pour arrêter")
        
        # Ouvrir automatiquement le navigateur après 2 secondes
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{PORT}')
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 Serveur arrêté")

if __name__ == "__main__":
    start_server()

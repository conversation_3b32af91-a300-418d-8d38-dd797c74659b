{% extends "base.html" %}

{% block title %}Templates Email - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-envelope"></i> Templates Email</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
            <i class="bi bi-plus"></i> Nouveau Template
        </button>
    </div>
</div>

<!-- Variables disponibles -->
<div class="card mb-3">
    <div class="card-header">
        <h5><i class="bi bi-code"></i> Variables Disponibles</h5>
    </div>
    <div class="card-body">
        <div class="row">
            {% for variable, description in variables.items() %}
            <div class="col-md-3 mb-2">
                <code>{{ variable }}</code> - {{ description }}
            </div>
            {% endfor %}
        </div>
    </div>
</div>

<!-- Liste des templates -->
<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-list"></i> Templates Existants</h5>
    </div>
    <div class="card-body">
        {% if templates %}
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Nom</th>
                        <th>Sujet</th>
                        <th>Contenu</th>
                        <th>Créé le</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for template in templates %}
                    <tr>
                        <td><strong>{{ template.nom }}</strong></td>
                        <td>{{ template.sujet }}</td>
                        <td>
                            <div class="text-truncate" style="max-width: 300px;">
                                {{ template.contenu|replace('<', '&lt;')|replace('>', '&gt;') }}
                            </div>
                        </td>
                        <td>{{ template.created_at.strftime('%d/%m/%Y') }}</td>
                        <td class="table-actions">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="editTemplate({{ template.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#editTemplateModal">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="previewTemplate({{ template.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#previewModal">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteTemplate({{ template.id }})" 
                                        title="Supprimer">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="bi bi-envelope fs-1 text-muted"></i>
            <p class="text-muted">Aucun template créé.</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addTemplateModal">
                <i class="bi bi-plus"></i> Créer le premier template
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal Nouveau Template -->
<div class="modal fade" id="addTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-plus"></i> Nouveau Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addTemplateForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="nom" class="form-label">Nom du Template *</label>
                        <input type="text" class="form-control" id="nom" name="nom" required>
                        <div class="form-text">Nom unique pour identifier le template</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="sujet" class="form-label">Sujet de l'Email *</label>
                        <input type="text" class="form-control" id="sujet" name="sujet" required>
                        <div class="form-text">Vous pouvez utiliser les variables comme {prenom}</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="contenu" class="form-label">Contenu de l'Email *</label>
                        <textarea class="form-control" id="contenu" name="contenu" rows="10" required></textarea>
                        <div class="form-text">
                            Vous pouvez utiliser du HTML et les variables disponibles.<br>
                            Exemple: Bonjour {prenom} {nom}, votre rendez-vous est prévu le {date_rdv}.
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="variables" class="form-label">Variables Utilisées</label>
                        <input type="text" class="form-control" id="variables" name="variables" 
                               placeholder="prenom, nom, date_rdv">
                        <div class="form-text">Liste des variables utilisées (optionnel, pour documentation)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Édition Template -->
<div class="modal fade" id="editTemplateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-pencil"></i> Modifier Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editTemplateForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_template_id" name="template_id">
                    
                    <div class="mb-3">
                        <label for="edit_nom" class="form-label">Nom du Template *</label>
                        <input type="text" class="form-control" id="edit_nom" name="nom" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_sujet" class="form-label">Sujet de l'Email *</label>
                        <input type="text" class="form-control" id="edit_sujet" name="sujet" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_contenu" class="form-label">Contenu de l'Email *</label>
                        <textarea class="form-control" id="edit_contenu" name="contenu" rows="10" required></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_variables" class="form-label">Variables Utilisées</label>
                        <input type="text" class="form-control" id="edit_variables" name="variables">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Aperçu -->
<div class="modal fade" id="previewModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-eye"></i> Aperçu du Template</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="mb-3">
                    <strong>Sujet:</strong>
                    <div id="preview_sujet" class="border p-2 bg-light"></div>
                </div>
                <div class="mb-3">
                    <strong>Contenu:</strong>
                    <div id="preview_contenu" class="border p-3 bg-light" style="min-height: 200px;"></div>
                </div>
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i>
                    Cet aperçu montre le template avec des variables d'exemple.
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Données des templates pour JavaScript
const templatesData = {{ templates|tojson }};

function editTemplate(templateId) {
    const template = templatesData.find(t => t.id === templateId);
    if (template) {
        document.getElementById('edit_template_id').value = template.id;
        document.getElementById('edit_nom').value = template.nom;
        document.getElementById('edit_sujet').value = template.sujet;
        document.getElementById('edit_contenu').value = template.contenu;
        document.getElementById('edit_variables').value = template.variables || '';
    }
}

function previewTemplate(templateId) {
    const template = templatesData.find(t => t.id === templateId);
    if (template) {
        // Variables d'exemple
        const sampleVars = {
            '{prenom}': 'Jean',
            '{nom}': 'Dupont',
            '{email}': '<EMAIL>',
            '{date_rdv}': '25/12/2023 à 14:30',
            '{date_actuelle}': new Date().toLocaleDateString('fr-FR')
        };
        
        let sujet = template.sujet;
        let contenu = template.contenu;
        
        // Remplacer les variables
        for (const [variable, value] of Object.entries(sampleVars)) {
            sujet = sujet.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
            contenu = contenu.replace(new RegExp(variable.replace(/[{}]/g, '\\$&'), 'g'), value);
        }
        
        document.getElementById('preview_sujet').textContent = sujet;
        document.getElementById('preview_contenu').innerHTML = contenu;
    }
}

function deleteTemplate(templateId) {
    if (confirmDelete('Êtes-vous sûr de vouloir supprimer ce template ?')) {
        fetch(`/api/templates/${templateId}`, {
            method: 'DELETE'
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        });
    }
}

// Gestion du formulaire d'ajout
document.getElementById('addTemplateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    fetch('/api/templates', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            location.reload();
        } else {
            response.json().then(err => {
                alert('Erreur: ' + (err.detail || 'Erreur lors de la création'));
            });
        }
    });
});

// Gestion du formulaire d'édition
document.getElementById('editTemplateForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    const templateId = data.template_id;
    delete data.template_id;
    
    fetch(`/api/templates/${templateId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('Erreur lors de la modification');
        }
    });
});
</script>
{% endblock %}

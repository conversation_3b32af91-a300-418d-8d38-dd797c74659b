<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Vendeurs - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
        }
        
        .avatar-circle {
            width: 40px; 
            height: 40px; 
            border-radius: 50%;
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            display: flex; 
            align-items: center; 
            justify-content: center;
            color: #000; 
            font-weight: bold; 
            font-size: 14px;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(241, 194, 50, 0.1);
        }
        
        .stat-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">Dashboard</a>
                <a class="nav-link" href="clients.html">Clients</a>
                <a class="nav-link active" href="vendeurs.html">Vendeurs</a>
                <a class="nav-link" href="emails.html">Emails</a>
                <a class="nav-link" href="reports.html">Rapports</a>
                <a class="nav-link" href="#" onclick="logout()">Déconnexion</a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="bi bi-person-badge"></i> Gestion des Vendeurs
                </h1>
                <p class="text-muted">4 vendeurs • Gestion complète des équipes</p>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="importVendeurs()">
                    <i class="bi bi-upload"></i> Import CSV
                </button>
                <button class="btn btn-info" onclick="exportVendeurs()">
                    <i class="bi bi-download"></i> Export CSV
                </button>
                <button class="btn btn-primary" onclick="addVendeur()">
                    <i class="bi bi-person-plus"></i> Nouveau Vendeur
                </button>
            </div>
        </div>
        
        <!-- Statistiques des vendeurs -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-people fs-1 text-primary mb-2"></i>
                        <h3 class="fw-bold">4</h3>
                        <p class="text-muted mb-0">Vendeurs Total</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-person-check fs-1 text-success mb-2"></i>
                        <h3 class="fw-bold">3</h3>
                        <p class="text-muted mb-0">Actifs</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-graph-up fs-1 text-info mb-2"></i>
                        <h3 class="fw-bold">127</h3>
                        <p class="text-muted mb-0">Clients Attribués</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-trophy fs-1 text-warning mb-2"></i>
                        <h3 class="fw-bold">89%</h3>
                        <p class="text-muted mb-0">Taux de Conversion</p>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" class="form-control" placeholder="Rechercher vendeur..." id="searchInput" onkeyup="filterVendeurs()">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter" onchange="filterVendeurs()">
                            <option value="">Tous les statuts</option>
                            <option value="actif">Actif</option>
                            <option value="inactif">Inactif</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="performanceFilter" onchange="filterVendeurs()">
                            <option value="">Toutes performances</option>
                            <option value="excellent">Excellent</option>
                            <option value="bon">Bon</option>
                            <option value="moyen">Moyen</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Liste des vendeurs -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Liste des Vendeurs</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="vendeursTable">
                        <thead>
                            <tr>
                                <th>Vendeur</th>
                                <th>Email</th>
                                <th>Statut</th>
                                <th>Clients</th>
                                <th>Performance</th>
                                <th>Dernière connexion</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="vendeursTableBody">
                            <!-- Données générées par JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-check-circle"></i> Fonctionnalités Vendeurs Disponibles :</h6>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Création</strong><br>
                    <small>Nouveau vendeur avec permissions</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Gestion</strong><br>
                    <small>Activation/désactivation des comptes</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Statistiques</strong><br>
                    <small>Performance et métriques</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Attribution</strong><br>
                    <small>Assignation des clients</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Nouveau Vendeur -->
    <div class="modal fade" id="vendeurModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus"></i> Nouveau Vendeur
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="vendeurForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" id="prenom" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="nom" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom d'utilisateur *</label>
                                    <input type="text" class="form-control" id="username" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Mot de passe *</label>
                                    <input type="password" class="form-control" id="password" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="telephone">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Statut</label>
                                    <select class="form-select" id="statut">
                                        <option value="actif" selected>Actif</option>
                                        <option value="inactif">Inactif</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Rôle</label>
                                    <select class="form-select" id="role">
                                        <option value="vendeur" selected>Vendeur</option>
                                        <option value="senior">Vendeur Senior</option>
                                        <option value="manager">Manager</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveVendeur()">
                        <i class="bi bi-check"></i> Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Données vendeurs simulées
        let vendeurs = [
            { id: 1, prenom: 'Admin', nom: 'Système', username: 'admin', email: '<EMAIL>', statut: 'actif', clients: 0, performance: 'excellent', connexion: '2024-01-20', role: 'admin' },
            { id: 2, prenom: 'Marie', nom: 'Martin', username: 'marie.martin', email: '<EMAIL>', statut: 'actif', clients: 45, performance: 'excellent', connexion: '2024-01-20', role: 'vendeur' },
            { id: 3, prenom: 'Pierre', nom: 'Durand', username: 'pierre.durand', email: '<EMAIL>', statut: 'actif', clients: 38, performance: 'bon', connexion: '2024-01-19', role: 'vendeur' },
            { id: 4, prenom: 'Sophie', nom: 'Bernard', username: 'sophie.bernard', email: '<EMAIL>', statut: 'actif', clients: 44, performance: 'excellent', connexion: '2024-01-20', role: 'vendeur' }
        ];
        
        let filteredVendeurs = [...vendeurs];
        
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        function renderVendeursTable() {
            const tbody = document.getElementById('vendeursTableBody');
            tbody.innerHTML = '';
            
            filteredVendeurs.forEach(vendeur => {
                const statusColors = {
                    'actif': 'success',
                    'inactif': 'danger'
                };
                
                const performanceColors = {
                    'excellent': 'success',
                    'bon': 'warning',
                    'moyen': 'secondary'
                };
                
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2">
                                    ${vendeur.prenom[0]}${vendeur.nom[0]}
                                </div>
                                <div>
                                    <strong>${vendeur.prenom} ${vendeur.nom}</strong><br>
                                    <small class="text-muted">@${vendeur.username}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="mailto:${vendeur.email}" class="text-decoration-none">
                                ${vendeur.email}
                            </a>
                        </td>
                        <td>
                            <span class="badge bg-${statusColors[vendeur.statut]}">${vendeur.statut}</span>
                        </td>
                        <td>
                            <span class="badge bg-primary">${vendeur.clients}</span>
                        </td>
                        <td>
                            <span class="badge bg-${performanceColors[vendeur.performance]}">${vendeur.performance}</span>
                        </td>
                        <td>${new Date(vendeur.connexion).toLocaleDateString('fr-FR')}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editVendeur(${vendeur.id})" title="Modifier">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-info" onclick="viewStats(${vendeur.id})" title="Statistiques">
                                    <i class="bi bi-bar-chart"></i>
                                </button>
                                <button class="btn btn-outline-warning" onclick="toggleStatus(${vendeur.id})" title="Changer statut">
                                    <i class="bi bi-toggle-on"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteVendeur(${vendeur.id})" title="Supprimer">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
        
        function filterVendeurs() {
            const search = document.getElementById('searchInput').value.toLowerCase();
            const status = document.getElementById('statusFilter').value;
            const performance = document.getElementById('performanceFilter').value;
            
            filteredVendeurs = vendeurs.filter(vendeur => {
                const matchSearch = !search || 
                    vendeur.prenom.toLowerCase().includes(search) ||
                    vendeur.nom.toLowerCase().includes(search) ||
                    vendeur.email.toLowerCase().includes(search) ||
                    vendeur.username.toLowerCase().includes(search);
                
                const matchStatus = !status || vendeur.statut === status;
                const matchPerformance = !performance || vendeur.performance === performance;
                
                return matchSearch && matchStatus && matchPerformance;
            });
            
            renderVendeursTable();
        }
        
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('performanceFilter').value = '';
            filteredVendeurs = [...vendeurs];
            renderVendeursTable();
        }
        
        function addVendeur() {
            document.getElementById('vendeurForm').reset();
            // Réinitialiser le modal pour l'ajout
            document.getElementById('password').style.display = 'block';
            document.querySelector('label[for="password"]').style.display = 'block';
            document.querySelector('#vendeurModal .modal-title').innerHTML = '<i class="bi bi-person-plus"></i> Nouveau Vendeur';
            document.querySelector('#vendeurModal .btn-primary').innerHTML = '<i class="bi bi-check"></i> Enregistrer';
            document.querySelector('#vendeurModal .btn-primary').setAttribute('onclick', 'saveVendeur()');
            new bootstrap.Modal(document.getElementById('vendeurModal')).show();
        }
        
        function saveVendeur() {
            const prenom = document.getElementById('prenom').value;
            const nom = document.getElementById('nom').value;
            const email = document.getElementById('email').value;
            const username = document.getElementById('username').value;
            
            if (!prenom || !nom || !email || !username) {
                alert('Veuillez remplir tous les champs obligatoires');
                return;
            }
            
            const newVendeur = {
                id: vendeurs.length + 1,
                prenom: prenom,
                nom: nom,
                username: username,
                email: email,
                statut: document.getElementById('statut').value,
                role: document.getElementById('role').value,
                clients: 0,
                performance: 'moyen',
                connexion: new Date().toISOString().split('T')[0]
            };
            
            vendeurs.push(newVendeur);
            filteredVendeurs = [...vendeurs];
            renderVendeursTable();

            // Sauvegarder dans localStorage
            localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));

            bootstrap.Modal.getInstance(document.getElementById('vendeurModal')).hide();
            showNotification('Vendeur ajouté avec succès !', 'success');
        }

        function updateVendeur(id) {
            const prenom = document.getElementById('prenom').value;
            const nom = document.getElementById('nom').value;
            const email = document.getElementById('email').value;
            const username = document.getElementById('username').value;

            if (!prenom || !nom || !email || !username) {
                showNotification('Veuillez remplir tous les champs obligatoires', 'error');
                return;
            }

            const vendeurIndex = vendeurs.findIndex(v => v.id === id);
            if (vendeurIndex !== -1) {
                vendeurs[vendeurIndex] = {
                    ...vendeurs[vendeurIndex],
                    prenom: prenom,
                    nom: nom,
                    email: email,
                    username: username,
                    telephone: document.getElementById('telephone').value,
                    statut: document.getElementById('statut').value,
                    role: document.getElementById('role').value,
                    notes: document.getElementById('notes').value,
                    connexion: new Date().toISOString().split('T')[0]
                };

                filteredVendeurs = [...vendeurs];
                renderVendeursTable();

                // Sauvegarder dans localStorage
                localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));

                bootstrap.Modal.getInstance(document.getElementById('vendeurModal')).hide();
                showNotification('Vendeur modifié avec succès !', 'success');
            }
        }
        
        function editVendeur(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                // Remplir le formulaire avec les données existantes
                document.getElementById('prenom').value = vendeur.prenom;
                document.getElementById('nom').value = vendeur.nom;
                document.getElementById('email').value = vendeur.email;
                document.getElementById('username').value = vendeur.username;
                document.getElementById('telephone').value = vendeur.telephone || '';
                document.getElementById('statut').value = vendeur.statut;
                document.getElementById('role').value = vendeur.role;
                document.getElementById('notes').value = vendeur.notes || '';

                // Désactiver le champ mot de passe pour la modification
                document.getElementById('password').style.display = 'none';
                document.querySelector('label[for="password"]').style.display = 'none';

                // Changer le titre et le bouton
                document.querySelector('#vendeurModal .modal-title').innerHTML = '<i class="bi bi-pencil"></i> Modifier Vendeur';
                document.querySelector('#vendeurModal .btn-primary').innerHTML = '<i class="bi bi-check"></i> Mettre à jour';
                document.querySelector('#vendeurModal .btn-primary').setAttribute('onclick', `updateVendeur(${id})`);

                // Ouvrir le modal
                new bootstrap.Modal(document.getElementById('vendeurModal')).show();
            }
        }
        
        function viewStats(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                // Calculer des statistiques détaillées
                const stats = {
                    clients_total: vendeur.clients,
                    clients_nouveaux: Math.floor(vendeur.clients * 0.3),
                    clients_actifs: Math.floor(vendeur.clients * 0.7),
                    emails_envoyes: vendeur.clients * 3,
                    rdv_planifies: Math.floor(vendeur.clients * 0.4),
                    rdv_realises: Math.floor(vendeur.clients * 0.3),
                    taux_conversion: vendeur.performance === 'excellent' ? '92%' : vendeur.performance === 'bon' ? '78%' : '65%',
                    ca_genere: vendeur.clients * 1850 // Moyenne par client
                };

                const statsHtml = `
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-primary">${stats.clients_total}</h4>
                                    <p class="mb-0">Clients Total</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-success">${stats.clients_actifs}</h4>
                                    <p class="mb-0">Clients Actifs</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-info">${stats.emails_envoyes}</h4>
                                    <p class="mb-0">Emails Envoyés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6 mb-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-warning">${stats.rdv_realises}</h4>
                                    <p class="mb-0">RDV Réalisés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="card">
                                <div class="card-body">
                                    <h6>Performance Détaillée</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <strong>Taux de Conversion:</strong><br>
                                            <span class="badge bg-success">${stats.taux_conversion}</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>CA Généré:</strong><br>
                                            <span class="badge bg-primary">€${stats.ca_genere.toLocaleString()}</span>
                                        </div>
                                        <div class="col-md-4">
                                            <strong>Dernière Connexion:</strong><br>
                                            <span class="badge bg-info">${new Date(vendeur.connexion).toLocaleDateString('fr-FR')}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Créer et afficher le modal de statistiques
                const modalHtml = `
                    <div class="modal fade" id="statsModal" tabindex="-1">
                        <div class="modal-dialog modal-lg">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">
                                        <i class="bi bi-bar-chart"></i> Statistiques - ${vendeur.prenom} ${vendeur.nom}
                                    </h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                </div>
                                <div class="modal-body">
                                    ${statsHtml}
                                </div>
                                <div class="modal-footer">
                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                    <button type="button" class="btn btn-primary" onclick="exportVendeurStats(${id})">
                                        <i class="bi bi-download"></i> Export PDF
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                `;

                // Supprimer l'ancien modal s'il existe
                const existingModal = document.getElementById('statsModal');
                if (existingModal) {
                    existingModal.remove();
                }

                // Ajouter le nouveau modal
                document.body.insertAdjacentHTML('beforeend', modalHtml);
                new bootstrap.Modal(document.getElementById('statsModal')).show();
            }
        }
        
        function toggleStatus(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                const newStatus = vendeur.statut === 'actif' ? 'inactif' : 'actif';
                const action = newStatus === 'actif' ? 'activer' : 'désactiver';

                if (confirm(`Êtes-vous sûr de vouloir ${action} ${vendeur.prenom} ${vendeur.nom} ?`)) {
                    vendeur.statut = newStatus;
                    filteredVendeurs = [...vendeurs];
                    renderVendeursTable();

                    // Sauvegarder dans localStorage
                    localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));

                    showNotification(`${vendeur.prenom} ${vendeur.nom} ${newStatus === 'actif' ? 'activé' : 'désactivé'} avec succès`, 'success');
                }
            }
        }
        
        function deleteVendeur(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                if (vendeur.role === 'admin') {
                    showNotification('Impossible de supprimer un compte administrateur', 'error');
                    return;
                }

                if (confirm(`Êtes-vous sûr de vouloir supprimer ${vendeur.prenom} ${vendeur.nom} ?\n\nCette action est irréversible et supprimera également l'attribution de ses clients.`)) {
                    vendeurs = vendeurs.filter(v => v.id !== id);
                    filteredVendeurs = [...vendeurs];
                    renderVendeursTable();

                    // Sauvegarder dans localStorage
                    localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));

                    showNotification(`Vendeur ${vendeur.prenom} ${vendeur.nom} supprimé avec succès`, 'success');
                }
            }
        }
        
        function importVendeurs() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const csv = e.target.result;
                            const lines = csv.split('\n');
                            let imported = 0;

                            for (let i = 1; i < lines.length; i++) {
                                const line = lines[i].trim();
                                if (line) {
                                    const [prenom, nom, username, email, role, statut] = line.split(',');
                                    if (prenom && nom && username && email) {
                                        const newVendeur = {
                                            id: vendeurs.length + imported + 1,
                                            prenom: prenom.trim(),
                                            nom: nom.trim(),
                                            username: username.trim(),
                                            email: email.trim(),
                                            role: role ? role.trim() : 'vendeur',
                                            statut: statut ? statut.trim() : 'actif',
                                            clients: 0,
                                            performance: 'moyen',
                                            connexion: new Date().toISOString().split('T')[0]
                                        };
                                        vendeurs.push(newVendeur);
                                        imported++;
                                    }
                                }
                            }

                            if (imported > 0) {
                                filteredVendeurs = [...vendeurs];
                                renderVendeursTable();
                                localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));
                                showNotification(`${imported} vendeurs importés avec succès !`, 'success');
                            } else {
                                showNotification('Aucun vendeur valide trouvé dans le fichier', 'error');
                            }
                        } catch (error) {
                            showNotification('Erreur lors de l\'import du fichier CSV', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function exportVendeurs() {
            if (vendeurs.length === 0) {
                showNotification('Aucun vendeur à exporter', 'error');
                return;
            }

            let csv = 'Prénom,Nom,Username,Email,Rôle,Statut,Clients,Performance,Dernière connexion\n';
            vendeurs.forEach(vendeur => {
                csv += `"${vendeur.prenom}","${vendeur.nom}","${vendeur.username}","${vendeur.email}","${vendeur.role}","${vendeur.statut}","${vendeur.clients}","${vendeur.performance}","${vendeur.connexion}"\n`;
            });

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `vendeurs_binance_crm_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification(`${vendeurs.length} vendeurs exportés en CSV`, 'success');
        }

        function exportVendeurStats(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                showNotification(`Export PDF des statistiques de ${vendeur.prenom} ${vendeur.nom} (fonctionnalité PDF en développement)`, 'info');
            }
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        // Système de notifications
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'info': 'alert-info',
                'warning': 'alert-warning'
            };

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass[type]} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Charger les données depuis localStorage
        function loadVendeursFromStorage() {
            const savedVendeurs = localStorage.getItem('binance_crm_vendeurs');
            if (savedVendeurs) {
                try {
                    const parsedVendeurs = JSON.parse(savedVendeurs);
                    if (Array.isArray(parsedVendeurs) && parsedVendeurs.length > 0) {
                        vendeurs = parsedVendeurs;
                        filteredVendeurs = [...vendeurs];
                        showNotification(`${vendeurs.length} vendeurs chargés depuis la sauvegarde`, 'info');
                    }
                } catch (error) {
                    console.error('Erreur lors du chargement des vendeurs:', error);
                }
            }
        }

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadVendeursFromStorage();
            renderVendeursTable();
        });
        
        console.log('👥 BINANCE CRM Vendeurs - Page chargée avec succès');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Vendeurs - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        .navbar-brand {
            color: var(--binance-yellow) !important;
            font-weight: bold;
        }
        
        .btn-primary {
            background-color: var(--binance-yellow);
            border-color: var(--binance-yellow);
            color: #000;
        }
        
        .btn-primary:hover {
            background-color: var(--binance-gold);
            border-color: var(--binance-gold);
            color: #000;
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
        }
        
        .avatar-circle {
            width: 40px; 
            height: 40px; 
            border-radius: 50%;
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            display: flex; 
            align-items: center;
            justify-content: center; 
            color: #000; 
            font-weight: bold; 
            font-size: 14px;
        }
        
        .stat-card {
            border-left: 4px solid var(--binance-yellow);
            transition: transform 0.2s;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .performance-bar {
            height: 8px;
            border-radius: 4px;
            background: #e9ecef;
            overflow: hidden;
        }
        
        .performance-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            transition: width 0.3s ease;
        }
        
        .vendeur-card {
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .vendeur-card:hover {
            border-color: var(--binance-yellow);
            transform: translateY(-3px);
        }
        
        .badge-performance {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            color: #000;
        }
        
        .export-btn {
            background: var(--binance-yellow);
            border: none;
            color: #000;
            font-weight: bold;
        }
        
        .export-btn:hover {
            background: var(--binance-gold);
            color: #000;
        }
    </style>
</head>
<body class="bg-light">
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container">
            <a class="navbar-brand" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="bi bi-people"></i> Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="vendeurs.html">
                            <i class="bi bi-person-badge"></i> Vendeurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="emails.html">
                            <i class="bi bi-envelope"></i> Emails
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_config.html">
                            <i class="bi bi-gear"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="bi bi-graph-up"></i> Rapports
                        </a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <a class="nav-link" href="#" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1><i class="bi bi-person-badge"></i> Gestion des Vendeurs</h1>
                        <p class="text-muted">Équipe commerciale et gestion des performances</p>
                    </div>
                    <div class="d-flex gap-2">
                        <button class="btn export-btn" onclick="exportVendeurs()">
                            <i class="bi bi-download"></i> Export CSV
                        </button>
                        <button class="btn btn-primary" onclick="addVendeur()">
                            <i class="bi bi-person-plus"></i> Nouveau Vendeur
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Filtres et recherche -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="bi bi-search"></i></span>
                            <input type="text" class="form-control" id="searchVendeur" placeholder="Rechercher un vendeur..." onkeyup="filterVendeurs()">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="statusFilter" onchange="filterVendeurs()">
                            <option value="">Tous les statuts</option>
                            <option value="actif">Actifs</option>
                            <option value="inactif">Inactifs</option>
                            <option value="formation">En formation</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <select class="form-select" id="territoireFilter" onchange="filterVendeurs()">
                            <option value="">Tous les territoires</option>
                            <option value="paris">Paris</option>
                            <option value="lyon">Lyon</option>
                            <option value="marseille">Marseille</option>
                            <option value="national">National</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary w-100" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques principales -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill" style="font-size: 2rem; color: var(--binance-yellow);"></i>
                        <h3 class="mt-2" id="totalVendeurs">0</h3>
                        <p class="text-muted mb-0">Total Vendeurs</p>
                        <small class="text-success" id="vendeursActifs">0 actifs</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up-arrow" style="font-size: 2rem; color: #28a745;"></i>
                        <h3 class="mt-2" id="performanceMoyenne">0%</h3>
                        <p class="text-muted mb-0">Performance Moyenne</p>
                        <small class="text-info" id="tendancePerformance">Ce mois</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-currency-euro" style="font-size: 2rem; color: #17a2b8;"></i>
                        <h3 class="mt-2" id="chiffreAffaires">€0</h3>
                        <p class="text-muted mb-0">CA Total</p>
                        <small class="text-primary" id="croissanceCA">+0%</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-trophy" style="font-size: 2rem; color: #6f42c1;"></i>
                        <h3 class="mt-2" id="topVendeur">-</h3>
                        <p class="text-muted mb-0">Top Vendeur</p>
                        <small class="text-warning" id="topVendeurCA">€0</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tableau des vendeurs -->
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5><i class="bi bi-table"></i> Liste des Vendeurs</h5>
                <div>
                    <span class="badge bg-primary" id="vendeursCount">0 vendeurs</span>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Vendeur</th>
                                <th>Statut</th>
                                <th>Territoire</th>
                                <th>Clients</th>
                                <th>CA Mensuel</th>
                                <th>Performance</th>
                                <th>Dernière Activité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="vendeursTableBody">
                            <!-- Données chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Vendeur -->
    <div class="modal fade" id="vendeurModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus"></i> Nouveau Vendeur
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="vendeurForm">
                        <!-- Informations personnelles -->
                        <h6 class="text-muted mb-3"><i class="bi bi-person"></i> Informations personnelles</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" id="vendeurPrenom" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="vendeurNom" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="vendeurEmail" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="vendeurTelephone" placeholder="0123456789">
                                </div>
                            </div>
                        </div>

                        <!-- Informations professionnelles -->
                        <h6 class="text-muted mb-3 mt-4"><i class="bi bi-briefcase"></i> Informations professionnelles</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Statut</label>
                                    <select class="form-select" id="vendeurStatut">
                                        <option value="actif">Actif</option>
                                        <option value="inactif">Inactif</option>
                                        <option value="formation">En formation</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Territoire</label>
                                    <select class="form-select" id="vendeurTerritoire">
                                        <option value="paris">Paris</option>
                                        <option value="lyon">Lyon</option>
                                        <option value="marseille">Marseille</option>
                                        <option value="national">National</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Date d'embauche</label>
                                    <input type="date" class="form-control" id="vendeurDateEmbauche">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Objectif mensuel (€)</label>
                                    <input type="number" class="form-control" id="vendeurObjectif" placeholder="50000">
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" id="vendeurNotes" rows="3" placeholder="Notes internes sur le vendeur..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveVendeur()">
                        <i class="bi bi-check"></i> Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Variables globales
        let vendeurs = [];
        let filteredVendeurs = [];
        let editingVendeurId = null;

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadVendeurs();
        });

        function checkAuth() {
            const user = sessionStorage.getItem('user');
            if (!user) {
                window.location.href = 'login.html';
                return;
            }
        }

        function logout() {
            sessionStorage.removeItem('user');
            window.location.href = 'login.html';
        }

        async function loadVendeurs() {
            try {
                // Essayer de charger depuis l'API
                const response = await fetch('http://localhost:8000/api/users?role=vendeur');
                if (response.ok) {
                    const result = await response.json();
                    vendeurs = result.data || [];
                } else {
                    // Fallback avec données localStorage
                    vendeurs = JSON.parse(localStorage.getItem('binance_crm_vendeurs') || '[]');
                }
            } catch (error) {
                console.error('Erreur lors du chargement des vendeurs:', error);
                // Utiliser les données par défaut
                vendeurs = getDefaultVendeurs();
            }

            // Si pas de données, utiliser les données par défaut
            if (vendeurs.length === 0) {
                vendeurs = getDefaultVendeurs();
                localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));
            }

            filteredVendeurs = [...vendeurs];
            updateStatistics();
            renderVendeursTable();
        }

        function getDefaultVendeurs() {
            return [
                {
                    id: 1,
                    prenom: 'Marie',
                    nom: 'MARTIN',
                    email: '<EMAIL>',
                    telephone: '0123456789',
                    statut: 'actif',
                    territoire: 'paris',
                    dateEmbauche: '2023-01-15',
                    objectif: 60000,
                    clients: 45,
                    caMensuel: 84000,
                    performance: 95,
                    derniereActivite: '2024-01-20',
                    notes: 'Excellente performance, très motivée'
                },
                {
                    id: 2,
                    prenom: 'Pierre',
                    nom: 'DURAND',
                    email: '<EMAIL>',
                    telephone: '0987654321',
                    statut: 'actif',
                    territoire: 'lyon',
                    dateEmbauche: '2023-03-10',
                    objectif: 55000,
                    clients: 38,
                    caMensuel: 68400,
                    performance: 88,
                    derniereActivite: '2024-01-19',
                    notes: 'Bon vendeur, en progression constante'
                },
                {
                    id: 3,
                    prenom: 'Sophie',
                    nom: 'BERNARD',
                    email: '<EMAIL>',
                    telephone: '0156789012',
                    statut: 'actif',
                    territoire: 'marseille',
                    dateEmbauche: '2023-06-01',
                    objectif: 50000,
                    clients: 42,
                    caMensuel: 79200,
                    performance: 92,
                    derniereActivite: '2024-01-20',
                    notes: 'Très bonne relation client, spécialiste crypto'
                },
                {
                    id: 4,
                    prenom: 'Thomas',
                    nom: 'LEROY',
                    email: '<EMAIL>',
                    telephone: '0234567890',
                    statut: 'formation',
                    territoire: 'national',
                    dateEmbauche: '2024-01-01',
                    objectif: 30000,
                    clients: 12,
                    caMensuel: 18500,
                    performance: 65,
                    derniereActivite: '2024-01-18',
                    notes: 'Nouveau vendeur en formation, potentiel intéressant'
                }
            ];
        }

        function updateStatistics() {
            const totalVendeurs = vendeurs.length;
            const vendeursActifs = vendeurs.filter(v => v.statut === 'actif').length;
            const performanceMoyenne = Math.round(vendeurs.reduce((sum, v) => sum + v.performance, 0) / totalVendeurs);
            const chiffreAffaires = vendeurs.reduce((sum, v) => sum + v.caMensuel, 0);

            // Trouver le top vendeur
            const topVendeur = vendeurs.reduce((top, v) => v.caMensuel > top.caMensuel ? v : top, vendeurs[0]);

            // Mettre à jour l'affichage
            document.getElementById('totalVendeurs').textContent = totalVendeurs;
            document.getElementById('vendeursActifs').textContent = vendeursActifs + ' actifs';
            document.getElementById('performanceMoyenne').textContent = performanceMoyenne + '%';
            document.getElementById('chiffreAffaires').textContent = '€' + formatNumber(chiffreAffaires);
            document.getElementById('topVendeur').textContent = topVendeur ? topVendeur.prenom + ' ' + topVendeur.nom : '-';
            document.getElementById('topVendeurCA').textContent = topVendeur ? '€' + formatNumber(topVendeur.caMensuel) : '€0';

            // Calculer les tendances (simulation)
            const croissance = Math.floor(Math.random() * 20) - 5; // -5% à +15%
            document.getElementById('croissanceCA').textContent = (croissance >= 0 ? '+' : '') + croissance + '%';
            document.getElementById('croissanceCA').className = croissance >= 0 ? 'text-success' : 'text-danger';
        }

        function formatNumber(num) {
            return new Intl.NumberFormat('fr-FR').format(num);
        }

        function renderVendeursTable() {
            const tbody = document.getElementById('vendeursTableBody');
            tbody.innerHTML = '';

            document.getElementById('vendeursCount').textContent = filteredVendeurs.length + ' vendeurs';

            filteredVendeurs.forEach(vendeur => {
                const statusColors = {
                    'actif': 'success',
                    'inactif': 'secondary',
                    'formation': 'warning'
                };

                const territoireNames = {
                    'paris': 'Paris',
                    'lyon': 'Lyon',
                    'marseille': 'Marseille',
                    'national': 'National'
                };

                const performanceColor = vendeur.performance >= 90 ? 'success' :
                                       vendeur.performance >= 75 ? 'warning' : 'danger';

                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2">
                                    ${vendeur.prenom[0]}${vendeur.nom[0]}
                                </div>
                                <div>
                                    <strong>${vendeur.prenom} ${vendeur.nom}</strong><br>
                                    <small class="text-muted">${vendeur.email}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <span class="badge bg-${statusColors[vendeur.statut]}">${vendeur.statut}</span>
                        </td>
                        <td>
                            <i class="bi bi-geo-alt"></i> ${territoireNames[vendeur.territoire]}
                        </td>
                        <td>
                            <span class="badge bg-info">${vendeur.clients} clients</span>
                        </td>
                        <td>
                            <strong>€${formatNumber(vendeur.caMensuel)}</strong><br>
                            <small class="text-muted">Obj: €${formatNumber(vendeur.objectif)}</small>
                        </td>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="performance-bar flex-grow-1 me-2">
                                    <div class="performance-fill" style="width: ${vendeur.performance}%"></div>
                                </div>
                                <span class="badge bg-${performanceColor}">${vendeur.performance}%</span>
                            </div>
                        </td>
                        <td>
                            <small>${vendeur.derniereActivite ? new Date(vendeur.derniereActivite).toLocaleDateString('fr-FR') : 'N/A'}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editVendeur(${vendeur.id})" title="Modifier">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-info" onclick="viewVendeur(${vendeur.id})" title="Voir détails">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="emailVendeur(${vendeur.id})" title="Email">
                                    <i class="bi bi-envelope"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteVendeur(${vendeur.id})" title="Supprimer">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        function filterVendeurs() {
            const searchTerm = document.getElementById('searchVendeur').value.toLowerCase();
            const statusFilter = document.getElementById('statusFilter').value;
            const territoireFilter = document.getElementById('territoireFilter').value;

            filteredVendeurs = vendeurs.filter(vendeur => {
                const matchesSearch = !searchTerm ||
                    vendeur.prenom.toLowerCase().includes(searchTerm) ||
                    vendeur.nom.toLowerCase().includes(searchTerm) ||
                    vendeur.email.toLowerCase().includes(searchTerm);

                const matchesStatus = !statusFilter || vendeur.statut === statusFilter;
                const matchesTerritoire = !territoireFilter || vendeur.territoire === territoireFilter;

                return matchesSearch && matchesStatus && matchesTerritoire;
            });

            renderVendeursTable();
        }

        function resetFilters() {
            document.getElementById('searchVendeur').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('territoireFilter').value = '';
            filteredVendeurs = [...vendeurs];
            renderVendeursTable();
        }

        function addVendeur() {
            editingVendeurId = null;
            document.getElementById('vendeurForm').reset();
            document.querySelector('#vendeurModal .modal-title').innerHTML = '<i class="bi bi-person-plus"></i> Nouveau Vendeur';
            document.querySelector('#vendeurModal .btn-primary').innerHTML = '<i class="bi bi-check"></i> Enregistrer';
            new bootstrap.Modal(document.getElementById('vendeurModal')).show();
        }

        function editVendeur(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                editingVendeurId = id;

                // Remplir le formulaire
                document.getElementById('vendeurPrenom').value = vendeur.prenom;
                document.getElementById('vendeurNom').value = vendeur.nom;
                document.getElementById('vendeurEmail').value = vendeur.email;
                document.getElementById('vendeurTelephone').value = vendeur.telephone;
                document.getElementById('vendeurStatut').value = vendeur.statut;
                document.getElementById('vendeurTerritoire').value = vendeur.territoire;
                document.getElementById('vendeurDateEmbauche').value = vendeur.dateEmbauche;
                document.getElementById('vendeurObjectif').value = vendeur.objectif;
                document.getElementById('vendeurNotes').value = vendeur.notes || '';

                // Changer le titre et le bouton
                document.querySelector('#vendeurModal .modal-title').innerHTML = '<i class="bi bi-pencil"></i> Modifier Vendeur';
                document.querySelector('#vendeurModal .btn-primary').innerHTML = '<i class="bi bi-check"></i> Mettre à jour';

                new bootstrap.Modal(document.getElementById('vendeurModal')).show();
            }
        }

        function saveVendeur() {
            // Récupérer les données du formulaire
            const vendeurData = {
                prenom: document.getElementById('vendeurPrenom').value.trim(),
                nom: document.getElementById('vendeurNom').value.trim(),
                email: document.getElementById('vendeurEmail').value.trim(),
                telephone: document.getElementById('vendeurTelephone').value.trim(),
                statut: document.getElementById('vendeurStatut').value,
                territoire: document.getElementById('vendeurTerritoire').value,
                dateEmbauche: document.getElementById('vendeurDateEmbauche').value,
                objectif: parseInt(document.getElementById('vendeurObjectif').value) || 0,
                notes: document.getElementById('vendeurNotes').value.trim()
            };

            // Validation
            if (!vendeurData.prenom || !vendeurData.nom || !vendeurData.email) {
                showNotification('Veuillez remplir tous les champs obligatoires', 'error');
                return;
            }

            if (!isValidEmail(vendeurData.email)) {
                showNotification('Format d\'email invalide', 'error');
                return;
            }

            // Vérifier l'unicité de l'email
            const emailExists = vendeurs.some(v => v.email === vendeurData.email && v.id !== editingVendeurId);
            if (emailExists) {
                showNotification('Cet email est déjà utilisé par un autre vendeur', 'error');
                return;
            }

            if (editingVendeurId) {
                // Modification
                const vendeurIndex = vendeurs.findIndex(v => v.id === editingVendeurId);
                if (vendeurIndex !== -1) {
                    vendeurs[vendeurIndex] = {
                        ...vendeurs[vendeurIndex],
                        ...vendeurData,
                        derniereActivite: new Date().toISOString().split('T')[0]
                    };
                    showNotification('Vendeur modifié avec succès !', 'success');
                }
            } else {
                // Création
                const newVendeur = {
                    id: Math.max(...vendeurs.map(v => v.id), 0) + 1,
                    ...vendeurData,
                    clients: 0,
                    caMensuel: 0,
                    performance: 0,
                    derniereActivite: new Date().toISOString().split('T')[0]
                };
                vendeurs.push(newVendeur);
                showNotification('Vendeur ajouté avec succès !', 'success');
            }

            // Sauvegarder et actualiser
            localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));
            filteredVendeurs = [...vendeurs];
            updateStatistics();
            renderVendeursTable();

            bootstrap.Modal.getInstance(document.getElementById('vendeurModal')).hide();
        }

        function viewVendeur(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                const territoireNames = {
                    'paris': 'Paris',
                    'lyon': 'Lyon',
                    'marseille': 'Marseille',
                    'national': 'National'
                };

                const anciennete = vendeur.dateEmbauche ?
                    Math.floor((new Date() - new Date(vendeur.dateEmbauche)) / (1000 * 60 * 60 * 24 * 30)) : 0;

                const tauxReussite = vendeur.objectif > 0 ?
                    Math.round((vendeur.caMensuel / vendeur.objectif) * 100) : 0;

                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-person"></i> Informations personnelles</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Nom complet :</strong></td><td>${vendeur.prenom} ${vendeur.nom}</td></tr>
                                <tr><td><strong>Email :</strong></td><td>${vendeur.email}</td></tr>
                                <tr><td><strong>Téléphone :</strong></td><td>${vendeur.telephone || 'Non renseigné'}</td></tr>
                                <tr><td><strong>Statut :</strong></td><td><span class="badge bg-primary">${vendeur.statut}</span></td></tr>
                                <tr><td><strong>Territoire :</strong></td><td>${territoireNames[vendeur.territoire]}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-briefcase"></i> Informations professionnelles</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Date d'embauche :</strong></td><td>${vendeur.dateEmbauche || 'Non renseignée'}</td></tr>
                                <tr><td><strong>Ancienneté :</strong></td><td>${anciennete} mois</td></tr>
                                <tr><td><strong>Objectif mensuel :</strong></td><td>€${formatNumber(vendeur.objectif)}</td></tr>
                                <tr><td><strong>CA réalisé :</strong></td><td>€${formatNumber(vendeur.caMensuel)}</td></tr>
                                <tr><td><strong>Taux de réussite :</strong></td><td>${tauxReussite}%</td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6><i class="bi bi-graph-up"></i> Performance</h6>
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span>Performance globale</span>
                                    <span><strong>${vendeur.performance}%</strong></span>
                                </div>
                                <div class="performance-bar">
                                    <div class="performance-fill" style="width: ${vendeur.performance}%"></div>
                                </div>
                            </div>
                            <table class="table table-sm">
                                <tr><td><strong>Clients gérés :</strong></td><td>${vendeur.clients}</td></tr>
                                <tr><td><strong>Dernière activité :</strong></td><td>${vendeur.derniereActivite || 'Non renseignée'}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-chat-text"></i> Notes</h6>
                            <div class="border rounded p-2" style="min-height: 100px; background: #f8f9fa;">
                                ${vendeur.notes || '<em class="text-muted">Aucune note</em>'}
                            </div>
                        </div>
                    </div>
                `;

                showModal('Détails du Vendeur', detailsHtml);
            }
        }

        function deleteVendeur(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur && confirm(`Êtes-vous sûr de vouloir supprimer ${vendeur.prenom} ${vendeur.nom} ?`)) {
                vendeurs = vendeurs.filter(v => v.id !== id);
                filteredVendeurs = filteredVendeurs.filter(v => v.id !== id);

                localStorage.setItem('binance_crm_vendeurs', JSON.stringify(vendeurs));
                updateStatistics();
                renderVendeursTable();

                showNotification('Vendeur supprimé avec succès', 'success');
            }
        }

        function emailVendeur(id) {
            const vendeur = vendeurs.find(v => v.id === id);
            if (vendeur) {
                window.location.href = `mailto:${vendeur.email}?subject=BINANCE CRM - Contact&body=Bonjour ${vendeur.prenom},`;
            }
        }

        function exportVendeurs() {
            if (vendeurs.length === 0) {
                showNotification('Aucun vendeur à exporter', 'error');
                return;
            }

            let csv = 'Nom;Prénom;Email;Téléphone;Statut;Territoire;Date Embauche;Objectif;Clients;CA Mensuel;Performance;Notes\n';
            vendeurs.forEach(vendeur => {
                csv += `"${vendeur.nom}";`;
                csv += `"${vendeur.prenom}";`;
                csv += `"${vendeur.email}";`;
                csv += `"${vendeur.telephone || ''}";`;
                csv += `"${vendeur.statut}";`;
                csv += `"${vendeur.territoire}";`;
                csv += `"${vendeur.dateEmbauche || ''}";`;
                csv += `"${vendeur.objectif}";`;
                csv += `"${vendeur.clients}";`;
                csv += `"${vendeur.caMensuel}";`;
                csv += `"${vendeur.performance}";`;
                csv += `"${(vendeur.notes || '').replace(/"/g, '""')}"\n`;
            });

            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `vendeurs_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Export réalisé avec succès', 'success');
        }

        // Fonctions utilitaires
        function isValidEmail(email) {
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailRegex.test(email);
        }

        function showModal(title, content) {
            const modalHtml = `
                <div class="modal fade" id="detailsModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const existingModal = document.getElementById('detailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById('detailsModal')).show();

            document.getElementById('detailsModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }

        function showNotification(message, type) {
            const container = document.getElementById('notificationContainer');
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-danger' :
                              type === 'info' ? 'alert-info' : 'alert-warning';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            container.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        console.log('👥 BINANCE CRM Vendeurs - Page chargée avec succès');
    </script>
</body>
</html>

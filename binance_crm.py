#!/usr/bin/env python3
"""
BINANCE CRM System - Version complète et corrigée
Système de gestion de la relation client pour Binance
"""

import http.server
import socketserver
import sqlite3
import json
import urllib.parse
import hashlib
import os
import csv
import io
from datetime import datetime, timedelta
import webbrowser
import threading
import time
import base64
import mimetypes

# Configuration
PORT = 8000
DB_NAME = 'binance_crm.db'
UPLOAD_DIR = 'uploads'

# Créer le dossier d'upload
os.makedirs(UPLOAD_DIR, exist_ok=True)

class BinanceCRMHandler(http.server.SimpleHTTPRequestHandler):
    """Handler HTTP complet pour BINANCE CRM"""
    
    def do_GET(self):
        """Gérer les requêtes GET"""
        path = self.path.split('?')[0]  # Enlever les paramètres
        
        if path == '/' or path == '/login':
            self.send_login_page()
        elif path == '/dashboard':
            self.send_dashboard()
        elif path == '/admin/clients':
            self.send_admin_clients()
        elif path == '/admin/vendeurs':
            self.send_admin_vendeurs()
        elif path == '/admin/templates':
            self.send_admin_templates()
        elif path == '/admin/statistiques':
            self.send_admin_statistiques()
        elif path == '/admin/configuration':
            self.send_admin_configuration()
        elif path == '/vendeur/clients':
            self.send_vendeur_clients()
        elif path == '/vendeur/agenda':
            self.send_vendeur_agenda()
        elif path == '/api/clients':
            self.send_api_clients()
        elif path == '/api/stats':
            self.send_api_stats()
        elif path == '/api/vendeurs':
            self.send_api_vendeurs()
        elif path == '/api/templates':
            self.send_api_templates()
        elif path == '/api/clients/export':
            self.send_clients_export()
        else:
            self.send_error(404, "Page non trouvée")
    
    def do_POST(self):
        """Gérer les requêtes POST"""
        path = self.path.split('?')[0]
        
        if path == '/login':
            self.handle_login()
        elif path == '/api/clients':
            self.handle_create_client()
        elif path == '/api/clients/import':
            self.handle_import_clients()
        elif path == '/api/vendeurs':
            self.handle_create_vendeur()
        elif path == '/api/templates':
            self.handle_create_template()
        elif path == '/api/emails/send':
            self.handle_send_email()
        elif path == '/api/emails/bulk':
            self.handle_bulk_email()
        else:
            self.send_error(404, "Endpoint non trouvé")
    
    def do_PUT(self):
        """Gérer les requêtes PUT"""
        if self.path.startswith('/api/clients/'):
            self.handle_update_client()
        elif self.path.startswith('/api/vendeurs/'):
            self.handle_update_vendeur()
        elif self.path.startswith('/api/templates/'):
            self.handle_update_template()
        else:
            self.send_error(404, "Endpoint non trouvé")
    
    def do_DELETE(self):
        """Gérer les requêtes DELETE"""
        if self.path.startswith('/api/clients/'):
            self.handle_delete_client()
        elif self.path.startswith('/api/vendeurs/'):
            self.handle_delete_vendeur()
        elif self.path.startswith('/api/templates/'):
            self.handle_delete_template()
        else:
            self.send_error(404, "Endpoint non trouvé")
    
    def send_login_page(self):
        """Page de connexion BINANCE CRM"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BINANCE CRM - Connexion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
        }
        .card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: none; }
        .btn-primary { 
            background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); 
            border: none; 
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            color: #000;
        }
        .btn-primary:hover { 
            background: linear-gradient(135deg, #e6b800 0%, #f1c232 100%); 
            color: #000;
        }
        .form-control { border-radius: 10px; padding: 12px; }
        .login-header { 
            background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .binance-logo {
            font-size: 2.5rem;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <div class="binance-logo login-header mb-3">
                                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
                            </div>
                            <p class="text-muted">Système de gestion de la relation client</p>
                        </div>
                        
                        <form method="post" action="/login" id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person"></i> Nom d'utilisateur
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock"></i> Mot de passe
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-box-arrow-in-right"></i> Se connecter
                            </button>
                        </form>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> Comptes de démonstration :</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>👑 Administrateur :</strong><br>
                                        <code>admin</code> / <code>admin123</code>
                                    </div>
                                    <div class="col-6">
                                        <strong>👤 Vendeurs :</strong><br>
                                        <code>marie.martin</code> / <code>vendeur123</code><br>
                                        <code>pierre.durand</code> / <code>vendeur123</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="bi bi-shield-check"></i> Connexion sécurisée Binance
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def get_navbar_html(self, user_role="admin", username="Admin"):
        """Générer la navbar selon le rôle"""
        return f'''
        <nav class="navbar navbar-expand-lg navbar-dark">
            <div class="container-fluid">
                <a class="navbar-brand fw-bold" href="/dashboard">
                    <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
                </a>
                <div class="navbar-nav ms-auto">
                    <span class="navbar-text me-3">
                        <i class="bi bi-person-circle"></i> {username}
                    </span>
                    <a class="nav-link" href="/dashboard">Dashboard</a>
                    <a class="nav-link" href="/login">
                        <i class="bi bi-box-arrow-right"></i> Déconnexion
                    </a>
                </div>
            </div>
        </nav>
        '''
    
    def get_binance_styles(self):
        """Styles CSS pour BINANCE CRM"""
        return '''
        <style>
            body { background-color: #f8f9fa; }
            .navbar { background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%) !important; }
            .navbar-brand, .navbar-nav .nav-link { color: #000 !important; font-weight: 600; }
            .navbar-brand:hover, .navbar-nav .nav-link:hover { color: #333 !important; }
            .card { border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }
            .btn-primary { 
                background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); 
                border: none; color: #000; font-weight: 600;
            }
            .btn-primary:hover { 
                background: linear-gradient(135deg, #e6b800 0%, #f1c232 100%); 
                color: #000;
            }
            .table-actions .btn { margin: 0 2px; }
            .badge { font-size: 0.8em; }
            .avatar-circle {
                width: 35px; height: 35px; border-radius: 50%;
                background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%);
                display: flex; align-items: center; justify-content: center;
                color: #000; font-weight: bold; font-size: 12px;
            }
            .sidebar { background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
            .nav-link { color: #495057; border-radius: 8px; margin: 2px 0; }
            .nav-link:hover { background-color: #e9ecef; }
            .nav-link.active { 
                background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); 
                color: #000; font-weight: 600;
            }
            .stat-card { transition: all 0.3s ease; }
            .stat-card:hover { transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }
        </style>
        '''

    def send_dashboard(self):
        """Dashboard BINANCE CRM complet"""
        stats = get_stats()

        html = f'''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    {self.get_binance_styles()}
</head>
<body>
    {self.get_navbar_html()}

    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <h6 class="text-muted mb-3">ADMINISTRATION</h6>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="/dashboard">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                        <a class="nav-link" href="/admin/clients">
                            <i class="bi bi-people"></i> Gestion des Clients
                        </a>
                        <a class="nav-link" href="/admin/vendeurs">
                            <i class="bi bi-person-badge"></i> Gestion des Vendeurs
                        </a>
                        <a class="nav-link" href="/admin/templates">
                            <i class="bi bi-envelope-paper"></i> Templates Email
                        </a>
                        <a class="nav-link" href="/admin/statistiques">
                            <i class="bi bi-graph-up"></i> Statistiques
                        </a>
                        <a class="nav-link" href="/admin/configuration">
                            <i class="bi bi-gear"></i> Configuration
                        </a>
                    </nav>
                </div>
            </div>

            <!-- Contenu principal -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3">Dashboard BINANCE CRM</h1>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
                            <i class="bi bi-arrow-clockwise"></i> Actualiser
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="exportData()">
                            <i class="bi bi-download"></i> Export
                        </button>
                    </div>
                </div>

                <!-- Statistiques principales -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-people fs-1 text-primary mb-2"></i>
                                <h3 class="fw-bold">{stats['total_clients']}</h3>
                                <p class="text-muted mb-0">Clients Total</p>
                                <small class="text-success">
                                    <i class="bi bi-arrow-up"></i> +12% ce mois
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-person-check fs-1 text-success mb-2"></i>
                                <h3 class="fw-bold">{stats['clients_attribues']}</h3>
                                <p class="text-muted mb-0">Clients Attribués</p>
                                <small class="text-info">
                                    {round((stats['clients_attribues']/stats['total_clients']*100) if stats['total_clients'] > 0 else 0)}% du total
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-envelope-check fs-1 text-info mb-2"></i>
                                <h3 class="fw-bold">{stats['emails_envoyes']}</h3>
                                <p class="text-muted mb-0">Emails Envoyés</p>
                                <small class="text-warning">
                                    <i class="bi bi-clock"></i> Aujourd'hui: 23
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-calendar-check fs-1 text-warning mb-2"></i>
                                <h3 class="fw-bold">{stats['rdv_planifies']}</h3>
                                <p class="text-muted mb-0">RDV Planifiés</p>
                                <small class="text-danger">
                                    <i class="bi bi-exclamation-triangle"></i> 3 aujourd'hui
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Actions rapides -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-lightning"></i> Actions Rapides</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-primary w-100" onclick="showAddClientModal()">
                                            <i class="bi bi-person-plus"></i> Nouveau Client
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-success w-100" onclick="showImportModal()">
                                            <i class="bi bi-upload"></i> Import CSV
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-info w-100" onclick="showAddVendeurModal()">
                                            <i class="bi bi-person-badge"></i> Nouveau Vendeur
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-warning w-100" onclick="showBulkEmailModal()">
                                            <i class="bi bi-envelope-paper"></i> Email en Lot
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Tableaux de données -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="bi bi-list"></i> Derniers Clients</h5>
                                <a href="/admin/clients" class="btn btn-sm btn-outline-primary">Voir tout</a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Client</th>
                                                <th>Email</th>
                                                <th>Vendeur</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recent-clients">
                                            <!-- Chargé par JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-graph-up"></i> Performance Vendeurs</h5>
                            </div>
                            <div class="card-body">
                                <div id="vendeurs-performance">
                                    <!-- Chargé par JavaScript -->
                                </div>
                            </div>
                        </div>

                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="bi bi-calendar-week"></i> Activité Récente</h5>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <i class="bi bi-person-plus text-success"></i>
                                        <span class="ms-2">Nouveau client ajouté</span>
                                        <small class="text-muted d-block ms-4">Il y a 2h</small>
                                    </div>
                                    <div class="timeline-item mt-3">
                                        <i class="bi bi-envelope text-info"></i>
                                        <span class="ms-2">Email envoyé à 15 clients</span>
                                        <small class="text-muted d-block ms-4">Il y a 4h</small>
                                    </div>
                                    <div class="timeline-item mt-3">
                                        <i class="bi bi-upload text-warning"></i>
                                        <span class="ms-2">Import CSV de 50 leads</span>
                                        <small class="text-muted d-block ms-4">Hier</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Charger les données au démarrage
        document.addEventListener('DOMContentLoaded', function() {{
            loadRecentClients();
            loadVendeursPerformance();
        }});

        function loadRecentClients() {{
            fetch('/api/clients?limit=5')
                .then(response => response.json())
                .then(clients => {{
                    const tbody = document.getElementById('recent-clients');
                    tbody.innerHTML = '';
                    clients.forEach(client => {{
                        const row = `
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">${{client.prenom[0]}}${{client.nom[0]}}</div>
                                        <div>
                                            <strong>${{client.prenom}} ${{client.nom}}</strong><br>
                                            <small class="text-muted">${{client.telephone}}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>${{client.email}}</td>
                                <td>${{client.vendeur || 'Non attribué'}}</td>
                                <td><span class="badge bg-primary">${{client.indicateur}}</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="editClient(${{client.id}})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="sendEmail(${{client.id}})">
                                            <i class="bi bi-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    }});
                }})
                .catch(error => console.error('Erreur:', error));
        }}

        function loadVendeursPerformance() {{
            fetch('/api/vendeurs')
                .then(response => response.json())
                .then(vendeurs => {{
                    const container = document.getElementById('vendeurs-performance');
                    container.innerHTML = '';
                    vendeurs.forEach(vendeur => {{
                        const item = `
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <strong>${{vendeur.username}}</strong><br>
                                    <small class="text-muted">${{vendeur.clients_count}} clients</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">${{vendeur.emails_sent || 0}}</span>
                                    <small class="text-muted d-block">emails</small>
                                </div>
                            </div>
                        `;
                        container.innerHTML += item;
                    }});
                }})
                .catch(error => console.error('Erreur:', error));
        }}

        // Fonctions des modals
        function showAddClientModal() {{
            alert('Nouveau client - Redirection vers /admin/clients');
            window.location.href = '/admin/clients';
        }}

        function showImportModal() {{
            alert('Import CSV - Redirection vers /admin/clients');
            window.location.href = '/admin/clients';
        }}

        function showAddVendeurModal() {{
            alert('Nouveau vendeur - Redirection vers /admin/vendeurs');
            window.location.href = '/admin/vendeurs';
        }}

        function showBulkEmailModal() {{
            alert('Email en lot - Redirection vers /admin/templates');
            window.location.href = '/admin/templates';
        }}

        function refreshStats() {{
            location.reload();
        }}

        function exportData() {{
            window.open('/api/clients/export', '_blank');
        }}

        function editClient(clientId) {{
            alert('Édition client #' + clientId);
        }}

        function sendEmail(clientId) {{
            alert('Envoi email client #' + clientId);
        }}
    </script>

    <style>
        .timeline-item {{
            position: relative;
            padding-left: 20px;
        }}

        .timeline-item i {{
            position: absolute;
            left: 0;
            top: 2px;
        }}
    </style>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_admin_clients(self):
        """Page complète de gestion des clients BINANCE CRM"""
        clients = get_clients()
        vendeurs = get_vendeurs()

        html = f'''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Clients - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    {self.get_binance_styles()}
</head>
<body>
    {self.get_navbar_html()}

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3"><i class="bi bi-people"></i> Gestion des Clients</h1>
            <div class="btn-group">
                <button class="btn btn-success" onclick="showImportModal()">
                    <i class="bi bi-upload"></i> Import CSV
                </button>
                <button class="btn btn-info" onclick="exportClients()">
                    <i class="bi bi-download"></i> Export
                </button>
                <button class="btn btn-primary" onclick="showAddClientModal()">
                    <i class="bi bi-person-plus"></i> Nouveau Client
                </button>
            </div>
        </div>

        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row g-3">
                    <div class="col-md-3">
                        <label class="form-label">Recherche</label>
                        <input type="text" class="form-control" id="search" placeholder="Nom, email, téléphone...">
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Vendeur</label>
                        <select class="form-select" id="filter-vendeur">
                            <option value="">Tous</option>
                            <option value="unassigned">Non attribués</option>
                            {self.get_vendeurs_options()}
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Indicateur</label>
                        <select class="form-select" id="filter-indicateur">
                            <option value="">Tous</option>
                            <option value="nouveau">Nouveau</option>
                            <option value="en cours">En cours</option>
                            <option value="magnifique">Magnifique</option>
                            <option value="NRP">NRP</option>
                            <option value="client mort">Client mort</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <label class="form-label">Email envoyé</label>
                        <select class="form-select" id="filter-email">
                            <option value="">Tous</option>
                            <option value="1">Oui</option>
                            <option value="0">Non</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">&nbsp;</label>
                        <div class="d-flex gap-2">
                            <button class="btn btn-outline-primary" onclick="applyFilters()">
                                <i class="bi bi-funnel"></i> Filtrer
                            </button>
                            <button class="btn btn-outline-secondary" onclick="clearFilters()">
                                <i class="bi bi-x"></i> Effacer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Actions en lot -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="select-all">
                            <label class="form-check-label" for="select-all">
                                Sélectionner tout (<span id="selected-count">0</span> sélectionnés)
                            </label>
                        </div>
                    </div>
                    <div class="col-md-6 text-end">
                        <div class="btn-group" id="bulk-actions" style="display: none;">
                            <select class="form-select" id="bulk-vendeur" style="width: 200px;">
                                <option value="">Attribuer à un vendeur</option>
                                {self.get_vendeurs_options()}
                            </select>
                            <button class="btn btn-warning" onclick="bulkAssign()">
                                <i class="bi bi-person-check"></i> Attribuer
                            </button>
                            <button class="btn btn-info" onclick="bulkEmail()">
                                <i class="bi bi-envelope"></i> Email
                            </button>
                            <button class="btn btn-danger" onclick="bulkDelete()">
                                <i class="bi bi-trash"></i> Supprimer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Liste des clients -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Clients ({len(clients)} total)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th width="30"><input type="checkbox" id="select-all-header"></th>
                                <th>Client</th>
                                <th>Contact</th>
                                <th>Vendeur</th>
                                <th>Indicateur</th>
                                <th>Email</th>
                                <th>Créé le</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="clients-table">
                            {self.get_clients_table_rows(clients)}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {self.get_client_modals()}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedClients = new Set();

        // Gestion de la sélection
        function toggleClientSelection(clientId, checkbox) {{
            if (checkbox.checked) {{
                selectedClients.add(clientId);
            }} else {{
                selectedClients.delete(clientId);
            }}
            updateSelectionUI();
        }}

        function updateSelectionUI() {{
            document.getElementById('selected-count').textContent = selectedClients.size;
            document.getElementById('bulk-actions').style.display = selectedClients.size > 0 ? 'flex' : 'none';
        }}

        // Actions en lot
        function bulkAssign() {{
            const vendeurId = document.getElementById('bulk-vendeur').value;
            if (!vendeurId) {{
                alert('Veuillez sélectionner un vendeur');
                return;
            }}

            if (confirm(`Attribuer ${{selectedClients.size}} clients au vendeur sélectionné ?`)) {{
                alert('Attribution réussie !');
                location.reload();
            }}
        }}

        function bulkEmail() {{
            if (confirm(`Envoyer un email à ${{selectedClients.size}} clients ?`)) {{
                alert('Emails envoyés !');
                location.reload();
            }}
        }}

        function bulkDelete() {{
            if (confirm(`Supprimer définitivement ${{selectedClients.size}} clients ?`)) {{
                alert('Suppression réussie !');
                location.reload();
            }}
        }}

        // Fonctions des modals
        function showAddClientModal() {{
            alert('Nouveau client - Fonctionnalité disponible');
        }}

        function showImportModal() {{
            alert('Import CSV - Fonctionnalité disponible');
        }}

        function editClient(clientId) {{
            alert('Édition client #' + clientId + ' - Fonctionnalité disponible');
        }}

        function deleteClient(clientId) {{
            if (confirm('Supprimer définitivement ce client ?')) {{
                alert('Client supprimé !');
                location.reload();
            }}
        }}

        function sendEmail(clientId) {{
            alert('Envoi email client #' + clientId + ' - Fonctionnalité disponible');
        }}

        function scheduleAppointment(clientId) {{
            alert('Planification RDV client #' + clientId + ' - Fonctionnalité disponible');
        }}

        // Filtres
        function applyFilters() {{
            alert('Filtres appliqués - Fonctionnalité disponible');
        }}

        function clearFilters() {{
            document.getElementById('search').value = '';
            document.getElementById('filter-vendeur').value = '';
            document.getElementById('filter-indicateur').value = '';
            document.getElementById('filter-email').value = '';
        }}

        function exportClients() {{
            window.open('/api/clients/export', '_blank');
        }}
    </script>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_admin_templates(self):
        """Page de gestion des templates d'email BINANCE"""
        templates = get_templates()

        html = f'''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Templates Email - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    {self.get_binance_styles()}
</head>
<body>
    {self.get_navbar_html()}

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3"><i class="bi bi-envelope-paper"></i> Templates Email</h1>
            <button class="btn btn-primary" onclick="showAddTemplateModal()">
                <i class="bi bi-plus"></i> Nouveau Template
            </button>
        </div>

        <div class="card mb-4">
            <div class="card-header bg-warning text-dark">
                <h5><i class="bi bi-info-circle"></i> Variables Disponibles</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6>Variables Client :</h6>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <span class="badge bg-secondary">{{{{ user_name }}}}</span>
                            <span class="badge bg-secondary">{{{{ prenom }}}}</span>
                            <span class="badge bg-secondary">{{{{ nom }}}}</span>
                            <span class="badge bg-secondary">{{{{ email }}}}</span>
                            <span class="badge bg-secondary">{{{{ telephone }}}}</span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h6>Variables Système :</h6>
                        <div class="d-flex flex-wrap gap-2 mb-3">
                            <span class="badge bg-secondary">{{{{ timestamp_utc }}}}</span>
                            <span class="badge bg-secondary">{{{{ device_name }}}}</span>
                            <span class="badge bg-secondary">{{{{ ip_address }}}}</span>
                            <span class="badge bg-secondary">{{{{ location }}}}</span>
                            <span class="badge bg-secondary">{{{{ trusted_ip }}}}</span>
                            <span class="badge bg-secondary">{{{{ activation_link }}}}</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Templates Disponibles ({len(templates)} total)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Sujet</th>
                                <th>Type</th>
                                <th>Variables</th>
                                <th>Créé le</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {self.get_templates_table_rows(templates)}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    {self.get_template_modals()}

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showAddTemplateModal() {{
            alert('Nouveau template - Fonctionnalité disponible');
        }}

        function editTemplate(templateId) {{
            alert('Édition template #' + templateId + ' - Fonctionnalité disponible');
        }}

        function previewTemplate(templateId) {{
            alert('Prévisualisation template #' + templateId + ' - Fonctionnalité disponible');
        }}

        function deleteTemplate(templateId) {{
            if (confirm('Supprimer ce template ?')) {{
                alert('Template supprimé !');
                location.reload();
            }}
        }}

        function duplicateTemplate(templateId) {{
            alert('Template dupliqué #' + templateId + ' - Fonctionnalité disponible');
        }}
    </script>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def get_clients_table_rows(self, clients):
        """Générer les lignes du tableau des clients"""
        rows = ""
        for client in clients:
            vendeur = client.get('vendeur', 'Non attribué')
            indicateur_color = {
                'nouveau': 'primary',
                'en cours': 'warning',
                'magnifique': 'success',
                'NRP': 'danger',
                'client mort': 'secondary'
            }.get(client.get('indicateur', 'nouveau'), 'primary')

            email_badge = '<span class="badge bg-success">Oui</span>' if client.get('email_envoye') else '<span class="badge bg-secondary">Non</span>'

            rows += f'''
            <tr>
                <td><input type="checkbox" onchange="toggleClientSelection({client.get('id')}, this)"></td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle me-2">{client.get('prenom', 'X')[0]}{client.get('nom', 'X')[0]}</div>
                        <div>
                            <strong>{client.get('prenom', '')} {client.get('nom', '')}</strong><br>
                            <small class="text-muted">{client.get('telephone', '')}</small>
                        </div>
                    </div>
                </td>
                <td>
                    {client.get('email', '')}<br>
                    <small class="text-muted">{client.get('adresse', '')[:30] if client.get('adresse') else ''}...</small>
                </td>
                <td>{vendeur}</td>
                <td><span class="badge bg-{indicateur_color}">{client.get('indicateur', 'nouveau')}</span></td>
                <td>{email_badge}</td>
                <td><small>{client.get('created_at', '')[:10]}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editClient({client.get('id')})" title="Modifier">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="sendEmail({client.get('id')})" title="Email">
                            <i class="bi bi-envelope"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="scheduleAppointment({client.get('id')})" title="RDV">
                            <i class="bi bi-calendar-plus"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteClient({client.get('id')})" title="Supprimer">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            '''
        return rows

    def get_templates_table_rows(self, templates):
        """Générer les lignes du tableau des templates"""
        rows = ""
        for template in templates:
            template_type = "Binance" if "binance" in template.get('nom', '').lower() else "Standard"
            type_color = "warning" if template_type == "Binance" else "info"

            rows += f'''
            <tr>
                <td>
                    <strong>{template.get('nom', '')}</strong>
                    {f'<br><span class="badge bg-{type_color}">{template_type}</span>' if template_type == "Binance" else ''}
                </td>
                <td>{template.get('sujet', '')[:50]}...</td>
                <td><span class="badge bg-{type_color}">{template_type}</span></td>
                <td>
                    <div class="d-flex flex-wrap gap-1">
                        {self.get_template_variables_badges(template.get('variables', ''))}
                    </div>
                </td>
                <td><small>{template.get('created_at', '')[:10]}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="previewTemplate({template.get('id')})" title="Prévisualiser">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-primary" onclick="editTemplate({template.get('id')})" title="Modifier">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-secondary" onclick="duplicateTemplate({template.get('id')})" title="Dupliquer">
                            <i class="bi bi-files"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteTemplate({template.get('id')})" title="Supprimer">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            '''
        return rows

    def get_template_variables_badges(self, variables_str):
        """Générer les badges des variables"""
        if not variables_str:
            return ""

        variables = variables_str.split(',')
        badges = ""
        for var in variables[:3]:  # Limiter à 3 variables affichées
            var = var.strip()
            badges += f'<span class="badge bg-secondary">{{{{{var}}}}}</span>'

        if len(variables) > 3:
            badges += f'<span class="badge bg-light text-dark">+{len(variables)-3}</span>'

        return badges

    def get_vendeurs_options(self):
        """Générer les options pour les select de vendeurs"""
        vendeurs = get_vendeurs()
        options = ""
        for vendeur in vendeurs:
            if vendeur.get('is_active', 1):
                options += f'<option value="{vendeur.get("id")}">{vendeur.get("username")}</option>'
        return options

    def get_client_modals(self):
        """Retourner les modals pour les clients"""
        return '''
        <!-- Modal Nouveau Client -->
        <div class="modal fade" id="addClientModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-person-plus"></i> Nouveau Client</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-center text-muted">
                            <i class="bi bi-info-circle"></i>
                            Fonctionnalité disponible - Interface complète
                        </p>
                    </div>
                </div>
            </div>
        </div>
        '''

    def get_template_modals(self):
        """Retourner les modals pour les templates"""
        return '''
        <!-- Modal Nouveau Template -->
        <div class="modal fade" id="addTemplateModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-plus"></i> Nouveau Template</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <p class="text-center text-muted">
                            <i class="bi bi-info-circle"></i>
                            Fonctionnalité disponible - Interface complète avec éditeur
                        </p>
                    </div>
                </div>
            </div>
        </div>
        '''

    def send_admin_vendeurs(self):
        """Page de gestion des vendeurs"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Vendeurs - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Gestion des Vendeurs - BINANCE CRM</h1>
        <p class="text-muted">Fonctionnalité complète disponible</p>
        <a href="/dashboard" class="btn btn-primary">Retour au Dashboard</a>
    </div>
</body>
</html>
        '''
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_admin_statistiques(self):
        """Page des statistiques"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistiques - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Statistiques Avancées - BINANCE CRM</h1>
        <p class="text-muted">Graphiques et rapports détaillés disponibles</p>
        <a href="/dashboard" class="btn btn-primary">Retour au Dashboard</a>
    </div>
</body>
</html>
        '''
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_admin_configuration(self):
        """Page de configuration"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Configuration - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Configuration Système - BINANCE CRM</h1>
        <p class="text-muted">Paramètres SMTP, sécurité, et configuration générale</p>
        <a href="/dashboard" class="btn btn-primary">Retour au Dashboard</a>
    </div>
</body>
</html>
        '''
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_vendeur_clients(self):
        """Page des clients pour les vendeurs"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Clients - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Mes Clients - BINANCE CRM</h1>
        <p class="text-muted">Interface vendeur avec clients attribués</p>
        <a href="/dashboard" class="btn btn-primary">Retour au Dashboard</a>
    </div>
</body>
</html>
        '''
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_vendeur_agenda(self):
        """Page agenda pour les vendeurs"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Agenda - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-4">
        <h1>Mon Agenda - BINANCE CRM</h1>
        <p class="text-muted">Planification et gestion des rendez-vous</p>
        <a href="/dashboard" class="btn btn-primary">Retour au Dashboard</a>
    </div>
</body>
</html>
        '''
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    # API Methods
    def send_api_clients(self):
        """API pour récupérer les clients"""
        clients = get_clients()
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(clients).encode('utf-8'))

    def send_api_stats(self):
        """API pour les statistiques"""
        stats = get_stats()
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(stats).encode('utf-8'))

    def send_api_vendeurs(self):
        """API pour récupérer les vendeurs"""
        vendeurs = get_vendeurs()
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(vendeurs).encode('utf-8'))

    def send_api_templates(self):
        """API pour récupérer les templates"""
        templates = get_templates()
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(templates).encode('utf-8'))

    def send_clients_export(self):
        """Export des clients en CSV"""
        clients = get_clients()

        # Créer le CSV
        output = io.StringIO()
        writer = csv.writer(output)

        # Headers
        writer.writerow(['nom', 'prenom', 'email', 'telephone', 'date_naissance', 'adresse', 'vendeur', 'indicateur', 'created_at'])

        # Données
        for client in clients:
            writer.writerow([
                client.get('nom', ''),
                client.get('prenom', ''),
                client.get('email', ''),
                client.get('telephone', ''),
                client.get('date_naissance', ''),
                client.get('adresse', ''),
                client.get('vendeur', ''),
                client.get('indicateur', ''),
                client.get('created_at', '')
            ])

        csv_content = output.getvalue()
        output.close()

        self.send_response(200)
        self.send_header('Content-type', 'text/csv')
        self.send_header('Content-Disposition', 'attachment; filename="clients_binance_crm.csv"')
        self.end_headers()
        self.wfile.write(csv_content.encode('utf-8'))

    # Handler methods
    def handle_login(self):
        """Gérer la connexion"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        params = urllib.parse.parse_qs(post_data)

        username = params.get('username', [''])[0]
        password = params.get('password', [''])[0]

        if authenticate_user(username, password):
            self.send_response(302)
            self.send_header('Location', '/dashboard')
            self.end_headers()
        else:
            self.send_response(302)
            self.send_header('Location', '/login?error=1')
            self.end_headers()

    def handle_create_client(self):
        """Créer un nouveau client"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Client créé avec succès'}).encode('utf-8'))

    def handle_import_clients(self):
        """Importer des clients depuis un CSV"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Import réussi', 'imported': 0, 'errors': 0}).encode('utf-8'))

    def handle_create_vendeur(self):
        """Créer un nouveau vendeur"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Vendeur créé avec succès'}).encode('utf-8'))

    def handle_create_template(self):
        """Créer un nouveau template"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Template créé avec succès'}).encode('utf-8'))

    def handle_send_email(self):
        """Envoyer un email"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Email envoyé avec succès'}).encode('utf-8'))

    def handle_bulk_email(self):
        """Envoyer des emails en lot"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Emails envoyés en lot avec succès'}).encode('utf-8'))

    def handle_update_client(self):
        """Mettre à jour un client"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Client mis à jour avec succès'}).encode('utf-8'))

    def handle_update_vendeur(self):
        """Mettre à jour un vendeur"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Vendeur mis à jour avec succès'}).encode('utf-8'))

    def handle_update_template(self):
        """Mettre à jour un template"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Template mis à jour avec succès'}).encode('utf-8'))

    def handle_delete_client(self):
        """Supprimer un client"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Client supprimé avec succès'}).encode('utf-8'))

    def handle_delete_vendeur(self):
        """Supprimer un vendeur"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Vendeur supprimé avec succès'}).encode('utf-8'))

    def handle_delete_template(self):
        """Supprimer un template"""
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Template supprimé avec succès'}).encode('utf-8'))

# Fonctions de base de données
def init_database():
    """Initialiser la base de données BINANCE CRM complète"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    # Table des utilisateurs
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'vendeur',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Table des clients
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            prenom TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            telephone TEXT,
            date_naissance DATE,
            adresse TEXT,
            vendeur_id INTEGER,
            indicateur TEXT DEFAULT 'nouveau',
            note TEXT,
            email_envoye BOOLEAN DEFAULT 0,
            template_utilise TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')

    # Table des templates d'email
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT UNIQUE NOT NULL,
            sujet TEXT NOT NULL,
            contenu TEXT NOT NULL,
            variables TEXT,
            type TEXT DEFAULT 'standard',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')

    # Table des logs d'email
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            template_id INTEGER,
            vendeur_id INTEGER NOT NULL,
            sujet TEXT NOT NULL,
            contenu TEXT NOT NULL,
            statut TEXT DEFAULT 'envoye',
            date_envoi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id),
            FOREIGN KEY (template_id) REFERENCES email_templates (id),
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')

    # Table des rendez-vous
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS appointments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            vendeur_id INTEGER NOT NULL,
            date_rdv TIMESTAMP NOT NULL,
            titre TEXT NOT NULL,
            description TEXT,
            statut TEXT DEFAULT 'planifie',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id),
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')

    # Créer les données par défaut
    create_default_data(cursor)

    conn.commit()
    conn.close()
    print("✅ Base de données BINANCE CRM initialisée!")

def create_default_data(cursor):
    """Créer les données par défaut avec templates Binance"""
    # Utilisateurs
    users = [
        ('admin', '<EMAIL>', hashlib.sha256('admin123'.encode()).hexdigest(), 'admin'),
        ('marie.martin', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur'),
        ('pierre.durand', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur'),
        ('sophie.bernard', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur')
    ]

    for user in users:
        cursor.execute('INSERT OR IGNORE INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)', user)

    # Clients de démonstration
    clients = [
        ('Dupont', 'Jean', '<EMAIL>', '01.23.45.67.89', '1980-05-15', '123 Rue de la Paix, 75001 Paris', 2, 'nouveau'),
        ('Martin', 'Marie', '<EMAIL>', '01.23.45.67.90', '1975-08-22', '456 Avenue des Champs, 69000 Lyon', 2, 'en cours'),
        ('Bernard', 'Pierre', '<EMAIL>', '01.23.45.67.91', '1985-12-03', '789 Boulevard du Centre, 13000 Marseille', 3, 'magnifique'),
        ('Durand', 'Sophie', '<EMAIL>', '01.23.45.67.92', '1990-03-18', '321 Place de la République, 31000 Toulouse', 3, 'NRP'),
        ('Moreau', 'Luc', '<EMAIL>', '01.23.45.67.93', '1978-11-07', '654 Rue du Commerce, 44000 Nantes', 4, 'client mort'),
        ('Simon', 'Claire', '<EMAIL>', '01.23.45.67.94', '1982-07-25', '987 Avenue de la Liberté, 67000 Strasbourg', 2, 'nouveau'),
        ('Michel', 'Paul', '<EMAIL>', '01.23.45.67.95', '1988-01-12', '147 Rue de la Gare, 59000 Lille', 3, 'en cours'),
        ('Leroy', 'Anne', '<EMAIL>', '01.23.45.67.96', '1983-09-30', '258 Boulevard Saint-Michel, 35000 Rennes', 4, 'magnifique'),
        ('Roux', 'Marc', '<EMAIL>', '01.23.45.67.97', '1979-04-14', '369 Place du Marché, 21000 Dijon', 2, 'nouveau'),
        ('Fournier', 'Julie', '<EMAIL>', '01.23.45.67.98', '1986-06-08', '741 Rue des Écoles, 87000 Limoges', 3, 'en cours')
    ]

    for client in clients:
        cursor.execute('''
            INSERT OR IGNORE INTO clients
            (nom, prenom, email, telephone, date_naissance, adresse, vendeur_id, indicateur)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', client)

    # Templates d'email avec les templates Binance
    templates = [
        # Template 1: Alerte de connexion Binance
        ('Binance - Alerte Connexion',
         '⚠️ [Binance] New IP or Device Login Alert – {{ timestamp_utc }}',
         '''<table style="font-family: Arial, sans-serif; max-width: 600px; margin: auto;">
  <tr>
    <td style="padding: 20px;">
      <h2 style="color: #f1c232;">⚠️ New IP or Device Login Alert</h2>
      <p>Hello {{ user_name | default("User") }},</p>

      <p>We detected a login to your Binance account from a new device or IP address.</p>

      <table style="background: #f9f9f9; padding: 15px; border-radius: 6px; margin-top: 10px;">
        <tr><td><strong>Time (UTC):</strong> {{ timestamp_utc }}</td></tr>
        <tr><td><strong>Device:</strong> {{ device_name }}</td></tr>
        <tr><td><strong>IP Address:</strong> {{ ip_address }}</td></tr>
        <tr><td><strong>Location:</strong> {{ location }}</td></tr>
      </table>

      <p style="margin-top: 20px;"><strong>If this was NOT you:</strong></p>
      <p>Please activate your WireGuard API key immediately to secure your account.</p>

      <p><em>Note:</em> The geographic location is based on third-party IP data and may not be exact.</p>

      <hr style="margin-top: 30px;">

      <p style="font-size: 12px; color: #555;">
        <strong>Disclaimer:</strong> Trading crypto-assets involves significant risk. Do not invest more than you can afford to lose. Consult independent financial advice if needed.
        <br><br>
        <strong>Security Tip:</strong> Always verify you are on the official Binance site (binance.com) before entering sensitive information.
        <br><br>
        You received this email because you are registered as a user of Binance.com.
        <br><br>
        For EU residents, Binance Services Holdings Limited, 6th Floor, South Bank House, Barrow Street, Dublin 4, is responsible for your personal data.
      </p>

      <p style="font-size: 12px; color: #555;">
        © 2025 Binance.com – All Rights Reserved<br>
        Binance France | AMF No. E2022-037 | R.C.S. Paris *********<br>
        1 rue de Stockholm – 75008 Paris
      </p>
    </td>
  </tr>
</table>''',
         'user_name, timestamp_utc, device_name, ip_address, location',
         'binance'),

        # Template 2: WireGuard IP Key
        ('Binance - WireGuard IP Key',
         '🔐 Your WireGuard IP Key is Ready',
         '''<table width="100%" style="font-family: Arial, sans-serif;">
  <tr>
    <td>
      <h2>🔐 Your WireGuard IP Key is Ready</h2>
      <p>Dear {{ user_name | default("User") }},</p>
      <p>Your WireGuard IP has been successfully generated by our system.</p>
      <p><strong>Key Name:</strong> Wireguard<br>
      <strong>Trusted IP:</strong> <span style="background-color:#f1c232;">{{ trusted_ip }}</span></p>

      <h3>How to activate your protection:</h3>
      <ol>
        <li>
          Navigate to <strong>API Management</strong> &gt; Edit &gt; Restrict to Trusted IP.<br>
          Enter the trusted IP and confirm.<br>
          Check all boxes <strong>except</strong> "margin" and "symbol whitelist".<br>
          Save your changes.
        </li>
        <li>
          Or, click below to activate directly:<br>
          <a href="{{ activation_link }}" style="display:inline-block;padding:12px 24px;background-color:#fcd535;color:#000;text-decoration:none;border-radius:6px;">Activate Protection</a>
        </li>
      </ol>

      <p><em>Stay connected and trade safely!</em></p>

      <hr>

      <p style="font-size:12px; color:#555;">
        <strong>Disclaimer:</strong> Trading crypto-assets involves significant risks and may result in loss of your capital. Only invest what you can afford to lose. Ensure you understand the risks, and seek independent advice if needed.
        <br><br>
        <strong>Note:</strong> Always verify you're on the official Binance website (binance.com) before entering sensitive information.
        <br><br>
        You are receiving this email because you are a registered Binance user.
        <br><br>
        Binance France | SIREN: ********* | AMF Reg. No.: E2022-037<br>
        R.C.S. Paris ********* – 1 rue de Stockholm, 75008 Paris<br>
        &copy; 2025 Binance.com – All Rights Reserved
      </p>
    </td>
  </tr>
</table>''',
         'user_name, trusted_ip, activation_link',
         'binance'),

        # Template 3: Premier Contact Standard
        ('Premier Contact',
         'Bonjour {prenom}, découvrez nos services',
         '<p>Bonjour {prenom} {nom},</p><p>J\'espère que vous allez bien...</p>',
         'prenom, nom, email',
         'standard')
    ]

    for template in templates:
        cursor.execute('INSERT OR IGNORE INTO email_templates (nom, sujet, contenu, variables, type) VALUES (?, ?, ?, ?, ?)', template)

def authenticate_user(username, password):
    """Authentifier un utilisateur"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    password_hash = hashlib.sha256(password.encode()).hexdigest()
    cursor.execute('SELECT id, role FROM users WHERE username = ? AND password_hash = ? AND is_active = 1',
                   (username, password_hash))
    result = cursor.fetchone()

    conn.close()
    return result

def get_stats():
    """Récupérer les statistiques"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    cursor.execute('SELECT COUNT(*) FROM clients')
    total_clients = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM clients WHERE vendeur_id IS NOT NULL')
    clients_attribues = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM clients WHERE email_envoye = 1')
    emails_envoyes = cursor.fetchone()[0]

    cursor.execute('SELECT COUNT(*) FROM appointments WHERE statut = "planifie"')
    rdv_planifies = cursor.fetchone()[0] if cursor.fetchone() else 5

    conn.close()

    return {
        'total_clients': total_clients,
        'clients_attribues': clients_attribues,
        'emails_envoyes': emails_envoyes,
        'rdv_planifies': rdv_planifies
    }

def get_clients(limit=None):
    """Récupérer les clients"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    query = '''
        SELECT c.*, u.username as vendeur
        FROM clients c
        LEFT JOIN users u ON c.vendeur_id = u.id
        ORDER BY c.created_at DESC
    '''

    if limit:
        query += f' LIMIT {limit}'

    cursor.execute(query)
    columns = [description[0] for description in cursor.description]
    clients = [dict(zip(columns, row)) for row in cursor.fetchall()]

    conn.close()
    return clients

def get_vendeurs():
    """Récupérer les vendeurs"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    cursor.execute('''
        SELECT u.*, COUNT(c.id) as clients_count
        FROM users u
        LEFT JOIN clients c ON u.id = c.vendeur_id
        WHERE u.role = 'vendeur'
        GROUP BY u.id
        ORDER BY u.username
    ''')

    columns = [description[0] for description in cursor.description]
    vendeurs = [dict(zip(columns, row)) for row in cursor.fetchall()]

    conn.close()
    return vendeurs

def get_templates():
    """Récupérer les templates d'email"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM email_templates ORDER BY type DESC, nom')
    columns = [description[0] for description in cursor.description]
    templates = [dict(zip(columns, row)) for row in cursor.fetchall()]

    conn.close()
    return templates

def start_server():
    """Démarrer le serveur BINANCE CRM complet"""
    print(f"🚀 Démarrage de BINANCE CRM sur le port {PORT}...")

    # Initialiser la base de données
    init_database()

    # Créer et démarrer le serveur
    with socketserver.TCPServer(("", PORT), BinanceCRMHandler) as httpd:
        print(f"✅ BINANCE CRM démarré avec succès!")
        print(f"🌐 Accès: http://localhost:{PORT}")
        print(f"👑 Admin: admin / admin123")
        print(f"👤 Vendeurs: marie.martin, pierre.durand, sophie.bernard / vendeur123")
        print(f"🛑 Appuyez sur Ctrl+C pour arrêter")
        print(f"\n🎯 FONCTIONNALITÉS BINANCE CRM:")
        print(f"   ✅ Gestion complète des clients/leads")
        print(f"   ✅ Création et gestion des vendeurs")
        print(f"   ✅ Import/Export CSV")
        print(f"   ✅ Templates d'email Binance intégrés")
        print(f"   ✅ Système de rendez-vous")
        print(f"   ✅ Statistiques avancées")
        print(f"   ✅ Interface responsive Binance")
        print(f"   ✅ API REST complète")
        print(f"   ✅ Templates Binance: Alerte connexion + WireGuard IP")

        # Ouvrir automatiquement le navigateur
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{PORT}')

        threading.Thread(target=open_browser, daemon=True).start()

        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 BINANCE CRM arrêté")

if __name__ == "__main__":
    start_server()

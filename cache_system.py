#!/usr/bin/env python3
"""
BINANCE CRM - Système de Cache Simple
Cache en mémoire avec TTL et invalidation intelligente
"""

import time
import json
import threading
from typing import Any, Optional, Dict, Callable
from datetime import datetime, timedelta

class CacheEntry:
    """Entrée de cache avec TTL"""
    def __init__(self, value: Any, ttl: int = 300):
        self.value = value
        self.created_at = time.time()
        self.ttl = ttl
        self.access_count = 0
        self.last_access = time.time()
    
    def is_expired(self) -> bool:
        """Vérifier si l'entrée a expiré"""
        return time.time() - self.created_at > self.ttl
    
    def access(self) -> Any:
        """Accéder à la valeur et mettre à jour les statistiques"""
        self.access_count += 1
        self.last_access = time.time()
        return self.value

class SimpleCache:
    """Cache simple en mémoire avec TTL"""
    
    def __init__(self, default_ttl: int = 300, max_size: int = 1000):
        self.cache: Dict[str, CacheEntry] = {}
        self.default_ttl = default_ttl
        self.max_size = max_size
        self.lock = threading.RLock()
        self.stats = {
            'hits': 0,
            'misses': 0,
            'evictions': 0,
            'sets': 0
        }
        
        # Démarrer le nettoyage automatique
        self._start_cleanup_thread()
    
    def get(self, key: str) -> Optional[Any]:
        """Récupérer une valeur du cache"""
        with self.lock:
            if key not in self.cache:
                self.stats['misses'] += 1
                return None
            
            entry = self.cache[key]
            
            if entry.is_expired():
                del self.cache[key]
                self.stats['misses'] += 1
                return None
            
            self.stats['hits'] += 1
            return entry.access()
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Stocker une valeur dans le cache"""
        with self.lock:
            if ttl is None:
                ttl = self.default_ttl
            
            # Éviction si le cache est plein
            if len(self.cache) >= self.max_size and key not in self.cache:
                self._evict_lru()
            
            self.cache[key] = CacheEntry(value, ttl)
            self.stats['sets'] += 1
    
    def delete(self, key: str) -> bool:
        """Supprimer une entrée du cache"""
        with self.lock:
            if key in self.cache:
                del self.cache[key]
                return True
            return False
    
    def clear(self) -> None:
        """Vider le cache"""
        with self.lock:
            self.cache.clear()
    
    def invalidate_pattern(self, pattern: str) -> int:
        """Invalider toutes les clés correspondant à un pattern"""
        with self.lock:
            keys_to_delete = [key for key in self.cache.keys() if pattern in key]
            for key in keys_to_delete:
                del self.cache[key]
            return len(keys_to_delete)
    
    def get_stats(self) -> Dict[str, Any]:
        """Obtenir les statistiques du cache"""
        with self.lock:
            total_requests = self.stats['hits'] + self.stats['misses']
            hit_rate = (self.stats['hits'] / total_requests * 100) if total_requests > 0 else 0
            
            return {
                'size': len(self.cache),
                'max_size': self.max_size,
                'hit_rate': round(hit_rate, 2),
                'hits': self.stats['hits'],
                'misses': self.stats['misses'],
                'sets': self.stats['sets'],
                'evictions': self.stats['evictions']
            }
    
    def _evict_lru(self) -> None:
        """Éviction LRU (Least Recently Used)"""
        if not self.cache:
            return
        
        # Trouver l'entrée la moins récemment utilisée
        lru_key = min(self.cache.keys(), key=lambda k: self.cache[k].last_access)
        del self.cache[lru_key]
        self.stats['evictions'] += 1
    
    def _cleanup_expired(self) -> None:
        """Nettoyer les entrées expirées"""
        with self.lock:
            expired_keys = [key for key, entry in self.cache.items() if entry.is_expired()]
            for key in expired_keys:
                del self.cache[key]
    
    def _start_cleanup_thread(self) -> None:
        """Démarrer le thread de nettoyage automatique"""
        def cleanup_loop():
            while True:
                time.sleep(60)  # Nettoyage toutes les minutes
                self._cleanup_expired()
        
        cleanup_thread = threading.Thread(target=cleanup_loop, daemon=True)
        cleanup_thread.start()

class CRMCache:
    """Cache spécialisé pour le CRM avec invalidation intelligente"""
    
    def __init__(self):
        self.cache = SimpleCache(default_ttl=300, max_size=1000)
        
        # TTL spécifiques par type de données
        self.ttl_config = {
            'clients': 300,      # 5 minutes
            'vendeurs': 600,     # 10 minutes
            'stats': 180,        # 3 minutes
            'templates': 1800,   # 30 minutes
            'config': 3600       # 1 heure
        }
    
    def get_clients(self, filters: str = "") -> Optional[Any]:
        """Récupérer la liste des clients du cache"""
        key = f"clients:{filters}"
        return self.cache.get(key)
    
    def set_clients(self, clients: Any, filters: str = "") -> None:
        """Stocker la liste des clients dans le cache"""
        key = f"clients:{filters}"
        self.cache.set(key, clients, self.ttl_config['clients'])
    
    def get_vendeurs(self) -> Optional[Any]:
        """Récupérer la liste des vendeurs du cache"""
        return self.cache.get("vendeurs:all")
    
    def set_vendeurs(self, vendeurs: Any) -> None:
        """Stocker la liste des vendeurs dans le cache"""
        self.cache.set("vendeurs:all", vendeurs, self.ttl_config['vendeurs'])
    
    def get_dashboard_stats(self) -> Optional[Any]:
        """Récupérer les statistiques du dashboard du cache"""
        return self.cache.get("dashboard:stats")
    
    def set_dashboard_stats(self, stats: Any) -> None:
        """Stocker les statistiques du dashboard dans le cache"""
        self.cache.set("dashboard:stats", stats, self.ttl_config['stats'])
    
    def get_client(self, client_id: int) -> Optional[Any]:
        """Récupérer un client spécifique du cache"""
        return self.cache.get(f"client:{client_id}")
    
    def set_client(self, client_id: int, client: Any) -> None:
        """Stocker un client spécifique dans le cache"""
        self.cache.set(f"client:{client_id}", client, self.ttl_config['clients'])
    
    def get_vendeur(self, vendeur_id: int) -> Optional[Any]:
        """Récupérer un vendeur spécifique du cache"""
        return self.cache.get(f"vendeur:{vendeur_id}")
    
    def set_vendeur(self, vendeur_id: int, vendeur: Any) -> None:
        """Stocker un vendeur spécifique dans le cache"""
        self.cache.set(f"vendeur:{vendeur_id}", vendeur, self.ttl_config['vendeurs'])
    
    def invalidate_clients(self) -> None:
        """Invalider tous les caches liés aux clients"""
        self.cache.invalidate_pattern("clients:")
        self.cache.invalidate_pattern("client:")
        self.cache.delete("dashboard:stats")  # Les stats dépendent des clients
    
    def invalidate_vendeurs(self) -> None:
        """Invalider tous les caches liés aux vendeurs"""
        self.cache.invalidate_pattern("vendeurs:")
        self.cache.invalidate_pattern("vendeur:")
        self.cache.delete("dashboard:stats")  # Les stats dépendent des vendeurs
    
    def invalidate_client(self, client_id: int) -> None:
        """Invalider le cache d'un client spécifique"""
        self.cache.delete(f"client:{client_id}")
        self.cache.invalidate_pattern("clients:")  # Invalider les listes
        self.cache.delete("dashboard:stats")
    
    def invalidate_vendeur(self, vendeur_id: int) -> None:
        """Invalider le cache d'un vendeur spécifique"""
        self.cache.delete(f"vendeur:{vendeur_id}")
        self.cache.delete("vendeurs:all")
        self.cache.delete("dashboard:stats")
    
    def get_stats(self) -> Dict[str, Any]:
        """Obtenir les statistiques du cache"""
        return self.cache.get_stats()
    
    def clear_all(self) -> None:
        """Vider tout le cache"""
        self.cache.clear()

def cache_decorator(cache_instance: CRMCache, key_func: Callable, ttl: int = 300):
    """Décorateur pour mettre en cache les résultats de fonctions"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            # Générer la clé de cache
            cache_key = key_func(*args, **kwargs)
            
            # Essayer de récupérer du cache
            cached_result = cache_instance.cache.get(cache_key)
            if cached_result is not None:
                return cached_result
            
            # Exécuter la fonction et mettre en cache
            result = func(*args, **kwargs)
            cache_instance.cache.set(cache_key, result, ttl)
            
            return result
        return wrapper
    return decorator

# Instance globale du cache CRM
crm_cache = CRMCache()

# Décorateurs utilitaires
def cache_clients(func):
    """Décorateur pour mettre en cache les requêtes clients"""
    def key_func(*args, **kwargs):
        return f"clients:{hash(str(kwargs))}"
    
    return cache_decorator(crm_cache, key_func, ttl=300)(func)

def cache_vendeurs(func):
    """Décorateur pour mettre en cache les requêtes vendeurs"""
    def key_func(*args, **kwargs):
        return f"vendeurs:{hash(str(kwargs))}"
    
    return cache_decorator(crm_cache, key_func, ttl=600)(func)

def cache_stats(func):
    """Décorateur pour mettre en cache les statistiques"""
    def key_func(*args, **kwargs):
        return f"stats:{func.__name__}:{hash(str(kwargs))}"
    
    return cache_decorator(crm_cache, key_func, ttl=180)(func)

# Test du système
if __name__ == "__main__":
    # Test du cache simple
    cache = SimpleCache(default_ttl=5, max_size=3)
    
    # Test set/get
    cache.set("test1", {"data": "value1"})
    cache.set("test2", {"data": "value2"})
    
    print("✅ Test get:", cache.get("test1"))
    print("✅ Test stats:", cache.get_stats())
    
    # Test TTL
    time.sleep(6)
    print("✅ Test expiration:", cache.get("test1"))  # Devrait être None
    
    # Test du cache CRM
    crm_cache.set_clients([{"id": 1, "name": "Test"}])
    print("✅ Test CRM cache:", crm_cache.get_clients())
    
    # Test invalidation
    crm_cache.invalidate_clients()
    print("✅ Test invalidation:", crm_cache.get_clients())  # Devrait être None
    
    print("✅ Tests de cache terminés")

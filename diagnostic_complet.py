#!/usr/bin/env python3
"""
BINANCE CRM - Diagnostic Complet du Système
Analyse exhaustive de toutes les fonctionnalités et identification des lacunes
"""

import requests
import json
import sqlite3
import os
import time
from pathlib import Path

class SystemDiagnostic:
    def __init__(self, base_url='http://localhost:8000'):
        self.base_url = base_url
        self.issues = []
        self.missing_features = []
        self.recommendations = []
        
    def log_issue(self, category, issue, severity="MEDIUM"):
        """Enregistrer un problème identifié"""
        self.issues.append({
            'category': category,
            'issue': issue,
            'severity': severity
        })
        
    def log_missing_feature(self, feature, description):
        """Enregistrer une fonctionnalité manquante"""
        self.missing_features.append({
            'feature': feature,
            'description': description
        })
        
    def log_recommendation(self, recommendation):
        """Enregistrer une recommandation"""
        self.recommendations.append(recommendation)
    
    def check_file_structure(self):
        """Vérifier la structure des fichiers"""
        print("📁 Vérification de la structure des fichiers...")
        
        required_files = [
            'database_server.py',
            'email_server.py', 
            'pdf_server.py',
            'dashboard.html',
            'clients.html',
            'vendeurs.html',
            'emails.html',
            'reports.html',
            'admin_config.html',
            'login.html'
        ]
        
        missing_files = []
        for file in required_files:
            if not os.path.exists(file):
                missing_files.append(file)
                self.log_issue("FILES", f"Fichier manquant: {file}", "HIGH")
            else:
                print(f"  ✅ {file}")
        
        if missing_files:
            print(f"  ❌ {len(missing_files)} fichiers manquants")
        else:
            print("  ✅ Tous les fichiers principaux sont présents")
    
    def check_database_structure(self):
        """Vérifier la structure de la base de données"""
        print("🗄️  Vérification de la structure de la base de données...")
        
        try:
            conn = sqlite3.connect('binance_crm.db')
            cursor = conn.cursor()
            
            # Vérifier les tables existantes
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = [
                'users', 'clients', 'email_templates', 
                'import_history', 'imported_clients'
            ]
            
            missing_tables = []
            for table in required_tables:
                if table in tables:
                    print(f"  ✅ Table {table}")
                else:
                    missing_tables.append(table)
                    self.log_issue("DATABASE", f"Table manquante: {table}", "HIGH")
            
            # Vérifier les colonnes critiques
            self.check_table_columns(cursor)
            
            # Vérifier les index
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
            indexes = [row[0] for row in cursor.fetchall()]
            
            if len(indexes) < 10:
                self.log_issue("DATABASE", f"Seulement {len(indexes)} index trouvés, optimisation nécessaire", "MEDIUM")
            else:
                print(f"  ✅ {len(indexes)} index de performance trouvés")
            
            conn.close()
            
        except Exception as e:
            self.log_issue("DATABASE", f"Erreur d'accès à la base: {e}", "HIGH")
    
    def check_table_columns(self, cursor):
        """Vérifier les colonnes des tables principales"""
        
        # Colonnes attendues pour la table clients
        cursor.execute("PRAGMA table_info(clients)")
        client_columns = [row[1] for row in cursor.fetchall()]
        
        expected_client_columns = [
            'id', 'first_name', 'last_name', 'email', 'phone',
            'birth_date', 'address', 'postal_code', 'city', 
            'status', 'assigned_to', 'notes', 'created_date'
        ]
        
        missing_client_columns = []
        for col in expected_client_columns:
            if col not in client_columns:
                missing_client_columns.append(col)
        
        if missing_client_columns:
            self.log_issue("DATABASE", f"Colonnes manquantes dans clients: {missing_client_columns}", "HIGH")
        else:
            print("  ✅ Table clients complète")
        
        # Colonnes attendues pour la table users
        cursor.execute("PRAGMA table_info(users)")
        user_columns = [row[1] for row in cursor.fetchall()]
        
        expected_user_columns = [
            'id', 'username', 'email', 'first_name', 'last_name',
            'phone', 'role', 'status', 'territory', 'hire_date',
            'monthly_target', 'notes'
        ]
        
        missing_user_columns = []
        for col in expected_user_columns:
            if col not in user_columns:
                missing_user_columns.append(col)
        
        if missing_user_columns:
            self.log_issue("DATABASE", f"Colonnes manquantes dans users: {missing_user_columns}", "HIGH")
        else:
            print("  ✅ Table users complète")
    
    def check_api_endpoints(self):
        """Vérifier tous les endpoints API"""
        print("🌐 Test complet des endpoints API...")
        
        # Test de connectivité serveur
        try:
            response = requests.get(f"{self.base_url}/api/dashboard-stats", timeout=5)
            if response.status_code != 200:
                self.log_issue("API", "Serveur API non accessible", "HIGH")
                return
        except:
            self.log_issue("API", "Serveur API non démarré", "HIGH")
            return
        
        # Endpoints GET à tester
        get_endpoints = [
            '/api/clients',
            '/api/users', 
            '/api/vendeurs',
            '/api/email-templates',
            '/api/email-campaigns',
            '/api/import-template',
            '/api/import-history',
            '/api/dashboard-stats'
        ]
        
        for endpoint in get_endpoints:
            try:
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                if response.status_code == 200:
                    print(f"  ✅ GET {endpoint}")
                else:
                    print(f"  ❌ GET {endpoint}: {response.status_code}")
                    self.log_issue("API", f"Endpoint GET {endpoint} non fonctionnel", "MEDIUM")
            except Exception as e:
                print(f"  ❌ GET {endpoint}: {e}")
                self.log_issue("API", f"Endpoint GET {endpoint} erreur: {e}", "MEDIUM")
        
        # Test des endpoints POST
        self.test_post_endpoints()
        
        # Test des endpoints PUT/DELETE
        self.test_crud_endpoints()
    
    def test_post_endpoints(self):
        """Tester les endpoints POST"""
        print("  📤 Test des endpoints POST...")
        
        # Test création client
        test_client = {
            'first_name': 'Test',
            'last_name': 'Diagnostic',
            'email': f'diagnostic.test.{int(time.time())}@example.com',
            'phone': '0123456789',
            'status': 'prospect'
        }
        
        try:
            response = requests.post(f"{self.base_url}/api/clients", json=test_client, timeout=10)
            if response.status_code == 200:
                print("    ✅ POST /api/clients")
            else:
                print(f"    ❌ POST /api/clients: {response.status_code}")
                self.log_issue("API", "Création client non fonctionnelle", "HIGH")
        except Exception as e:
            print(f"    ❌ POST /api/clients: {e}")
            self.log_issue("API", f"Erreur création client: {e}", "HIGH")
    
    def test_crud_endpoints(self):
        """Tester les opérations CRUD complètes"""
        print("  🔄 Test des opérations CRUD...")
        
        # Vérifier si PUT et DELETE sont implémentés
        crud_endpoints = [
            ('PUT', '/api/clients/1'),
            ('DELETE', '/api/clients/1'),
            ('PUT', '/api/vendeurs/1'),
            ('DELETE', '/api/vendeurs/1')
        ]
        
        for method, endpoint in crud_endpoints:
            try:
                if method == 'PUT':
                    response = requests.put(f"{self.base_url}{endpoint}", json={'test': 'data'}, timeout=5)
                else:
                    response = requests.delete(f"{self.base_url}{endpoint}", timeout=5)
                
                if response.status_code in [200, 404]:  # 404 acceptable si l'ID n'existe pas
                    print(f"    ✅ {method} {endpoint}")
                else:
                    print(f"    ❌ {method} {endpoint}: {response.status_code}")
                    self.log_issue("API", f"Endpoint {method} {endpoint} non fonctionnel", "MEDIUM")
            except Exception as e:
                print(f"    ❌ {method} {endpoint}: {e}")
                self.log_issue("API", f"Erreur {method} {endpoint}: {e}", "MEDIUM")
    
    def check_frontend_functionality(self):
        """Vérifier les fonctionnalités frontend"""
        print("🌐 Vérification des fonctionnalités frontend...")
        
        pages = [
            'dashboard.html',
            'clients.html',
            'vendeurs.html', 
            'emails.html',
            'reports.html',
            'admin_config.html'
        ]
        
        for page in pages:
            try:
                response = requests.get(f"{self.base_url}/{page}", timeout=10)
                if response.status_code == 200:
                    print(f"  ✅ {page}")
                    # Vérifier le contenu de base
                    content = response.text
                    if 'BINANCE CRM' not in content:
                        self.log_issue("FRONTEND", f"{page} ne contient pas le titre BINANCE CRM", "LOW")
                    if 'bootstrap' not in content.lower():
                        self.log_issue("FRONTEND", f"{page} ne semble pas utiliser Bootstrap", "MEDIUM")
                else:
                    print(f"  ❌ {page}: {response.status_code}")
                    self.log_issue("FRONTEND", f"Page {page} non accessible", "HIGH")
            except Exception as e:
                print(f"  ❌ {page}: {e}")
                self.log_issue("FRONTEND", f"Erreur page {page}: {e}", "HIGH")
    
    def check_missing_features(self):
        """Identifier les fonctionnalités manquantes"""
        print("🔍 Identification des fonctionnalités manquantes...")
        
        # Fonctionnalités avancées manquantes
        missing_features = [
            ("Authentification 2FA", "Authentification à deux facteurs pour sécurité renforcée"),
            ("Système de notifications", "Notifications push/email pour événements importants"),
            ("Audit trail", "Journal des modifications pour traçabilité complète"),
            ("Sauvegarde automatique", "Système de sauvegarde automatique de la base de données"),
            ("API rate limiting", "Limitation du taux de requêtes pour éviter les abus"),
            ("Chiffrement des données", "Chiffrement des données sensibles en base"),
            ("Système de cache", "Cache Redis/Memcached pour améliorer les performances"),
            ("Monitoring système", "Monitoring des performances et alertes"),
            ("Tests automatisés", "Suite de tests unitaires et d'intégration"),
            ("Documentation API", "Documentation Swagger/OpenAPI"),
            ("Gestion des rôles", "Système de permissions granulaires"),
            ("Export PDF avancé", "Génération de rapports PDF personnalisés"),
            ("Intégration webhook", "Webhooks pour intégrations tierces"),
            ("Système de queue", "Queue de traitement pour tâches lourdes"),
            ("Multi-tenant", "Support multi-entreprise"),
            ("API versioning", "Versioning de l'API pour compatibilité"),
            ("Logs structurés", "Logging structuré avec niveaux"),
            ("Health check", "Endpoint de vérification de santé système"),
            ("Métriques business", "Tableaux de bord avec KPI avancés"),
            ("Synchronisation temps réel", "WebSocket pour mises à jour temps réel")
        ]
        
        for feature, description in missing_features:
            self.log_missing_feature(feature, description)
            print(f"  📋 {feature}: {description}")
    
    def check_security_issues(self):
        """Vérifier les problèmes de sécurité"""
        print("🔒 Vérification de la sécurité...")
        
        security_checks = [
            ("Mots de passe hashés", self.check_password_hashing),
            ("Protection CSRF", self.check_csrf_protection),
            ("Validation des entrées", self.check_input_validation),
            ("HTTPS", self.check_https),
            ("Headers de sécurité", self.check_security_headers)
        ]
        
        for check_name, check_func in security_checks:
            try:
                result = check_func()
                if result:
                    print(f"  ✅ {check_name}")
                else:
                    print(f"  ⚠️  {check_name}")
            except Exception as e:
                print(f"  ❌ {check_name}: {e}")
                self.log_issue("SECURITY", f"Erreur vérification {check_name}: {e}", "HIGH")
    
    def check_password_hashing(self):
        """Vérifier le hashage des mots de passe"""
        try:
            conn = sqlite3.connect('binance_crm.db')
            cursor = conn.cursor()
            cursor.execute("SELECT password_hash FROM users LIMIT 1")
            result = cursor.fetchone()
            conn.close()
            
            if result and len(result[0]) > 20:  # Hash devrait être long
                return True
            else:
                self.log_issue("SECURITY", "Mots de passe possiblement non hashés", "HIGH")
                return False
        except:
            return False
    
    def check_csrf_protection(self):
        """Vérifier la protection CSRF"""
        # Vérifier si des tokens CSRF sont utilisés
        self.log_issue("SECURITY", "Protection CSRF non implémentée", "MEDIUM")
        return False
    
    def check_input_validation(self):
        """Vérifier la validation des entrées"""
        # Test d'injection SQL basique
        try:
            malicious_data = {"email": "'; DROP TABLE clients; --"}
            response = requests.post(f"{self.base_url}/api/clients", json=malicious_data, timeout=5)
            # Si le serveur ne crash pas, c'est bon signe
            return True
        except:
            return False
    
    def check_https(self):
        """Vérifier l'utilisation d'HTTPS"""
        if self.base_url.startswith('https'):
            return True
        else:
            self.log_issue("SECURITY", "HTTPS non utilisé", "MEDIUM")
            return False
    
    def check_security_headers(self):
        """Vérifier les headers de sécurité"""
        try:
            response = requests.get(f"{self.base_url}/dashboard.html", timeout=5)
            headers = response.headers
            
            security_headers = [
                'X-Content-Type-Options',
                'X-Frame-Options', 
                'X-XSS-Protection',
                'Strict-Transport-Security'
            ]
            
            missing_headers = []
            for header in security_headers:
                if header not in headers:
                    missing_headers.append(header)
            
            if missing_headers:
                self.log_issue("SECURITY", f"Headers de sécurité manquants: {missing_headers}", "MEDIUM")
                return False
            return True
        except:
            return False
    
    def generate_recommendations(self):
        """Générer des recommandations d'amélioration"""
        print("💡 Génération des recommandations...")
        
        # Recommandations basées sur les problèmes identifiés
        high_priority_issues = [issue for issue in self.issues if issue['severity'] == 'HIGH']
        
        if high_priority_issues:
            self.log_recommendation("🚨 PRIORITÉ HAUTE: Corriger les problèmes critiques identifiés")
        
        # Recommandations générales
        general_recommendations = [
            "🔒 Implémenter l'authentification 2FA pour la sécurité",
            "📊 Ajouter un système de monitoring et d'alertes",
            "🧪 Créer une suite de tests automatisés",
            "📚 Documenter l'API avec Swagger/OpenAPI",
            "⚡ Implémenter un système de cache (Redis)",
            "🔄 Ajouter des webhooks pour les intégrations",
            "📈 Créer des métriques business avancées",
            "🛡️  Renforcer la sécurité avec HTTPS et headers",
            "🔍 Implémenter un audit trail complet",
            "📦 Ajouter un système de sauvegarde automatique"
        ]
        
        for rec in general_recommendations:
            self.log_recommendation(rec)
    
    def run_complete_diagnostic(self):
        """Exécuter le diagnostic complet"""
        print("🔍 DIAGNOSTIC COMPLET DU SYSTÈME BINANCE CRM")
        print("=" * 60)
        
        # Exécuter tous les tests
        self.check_file_structure()
        print()
        self.check_database_structure()
        print()
        self.check_api_endpoints()
        print()
        self.check_frontend_functionality()
        print()
        self.check_missing_features()
        print()
        self.check_security_issues()
        print()
        self.generate_recommendations()
        
        # Générer le rapport final
        self.generate_final_report()
    
    def generate_final_report(self):
        """Générer le rapport final"""
        print(f"\n📊 RAPPORT FINAL DU DIAGNOSTIC")
        print("=" * 50)
        
        # Résumé des problèmes
        high_issues = [i for i in self.issues if i['severity'] == 'HIGH']
        medium_issues = [i for i in self.issues if i['severity'] == 'MEDIUM']
        low_issues = [i for i in self.issues if i['severity'] == 'LOW']
        
        print(f"🚨 PROBLÈMES CRITIQUES: {len(high_issues)}")
        for issue in high_issues:
            print(f"   - {issue['category']}: {issue['issue']}")
        
        print(f"\n⚠️  PROBLÈMES MOYENS: {len(medium_issues)}")
        for issue in medium_issues[:5]:  # Limiter l'affichage
            print(f"   - {issue['category']}: {issue['issue']}")
        
        print(f"\n📋 FONCTIONNALITÉS MANQUANTES: {len(self.missing_features)}")
        for feature in self.missing_features[:5]:  # Limiter l'affichage
            print(f"   - {feature['feature']}")
        
        print(f"\n💡 RECOMMANDATIONS: {len(self.recommendations)}")
        for rec in self.recommendations[:5]:  # Limiter l'affichage
            print(f"   - {rec}")
        
        # Score global
        total_issues = len(self.issues)
        critical_issues = len(high_issues)
        
        if critical_issues == 0 and total_issues < 5:
            score = "EXCELLENT"
            color = "🟢"
        elif critical_issues == 0 and total_issues < 10:
            score = "BON"
            color = "🟡"
        elif critical_issues < 3:
            score = "MOYEN"
            color = "🟠"
        else:
            score = "CRITIQUE"
            color = "🔴"
        
        print(f"\n{color} SCORE GLOBAL: {score}")
        print(f"Total des problèmes identifiés: {total_issues}")
        print(f"Problèmes critiques: {critical_issues}")

if __name__ == "__main__":
    diagnostic = SystemDiagnostic()
    diagnostic.run_complete_diagnostic()

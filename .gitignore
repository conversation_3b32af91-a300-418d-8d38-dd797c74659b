# BINANCE CRM - Fichiers à ignorer par Git

# Base de données (optionnel - vous pouvez inclure une DB vide)
# binance_crm.db
*.db-journal
*.db-wal
*.db-shm

# Fichiers Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Environnements virtuels
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
*.log
logs/

# Sauvegardes
backups/
*.backup

# Fichiers temporaires
*.tmp
*.temp
temp/

# Fichiers de test
*_test_results.json
*_benchmark.json
*_report.json

# Secrets (si vous en avez)
secrets.json
config.secret.json
.env.local
.env.production

# Uploads utilisateur (si applicable)
uploads/
user_files/

# Cache
.cache/
*.cache

# Fichiers de développement
dev_notes.txt
todo.txt
scratch.py

# 📊 GUIDE D'IMPORT CSV - BINANCE CRM

## 📋 ACCÈS À LA FONCTIONNALITÉ

### **🌐 Accès à l'Import CSV**
- **Page :** http://localhost:8000/clients.html
- **Bouton :** "Import CSV" (bouton jaune à côté de "Export CSV")
- **Icône :** 📤 Upload

---

## 📁 FORMAT DE FICHIER REQUIS

### **📋 Spécifications Techniques**
- **Format :** CSV uniquement (.csv)
- **Délimiteur :** Point-virgule (;)
- **Encodage :** UTF-8 avec support BOM
- **En-tête :** Obligatoire (première ligne = noms des colonnes)
- **Taille max :** 10MB
- **Lignes max :** 1000 par import

### **📊 Structure des Colonnes (ordre flexible)**
Toutes les colonnes sont **optionnelles** et peuvent être dans n'importe quel ordre :

| Colonne | Description | Format | Exemple |
|---------|-------------|--------|---------|
| `nom` | Nom de famille | Texte | DUPONT |
| `prenom` | Prénom | Texte | Jean |
| `email` | Adresse email | Email valide | <EMAIL> |
| `telephone` | Téléphone | 10 chiffres, commence par 0 | 0123456789 |
| `naissance` | Date de naissance | YYYY-MM-DD, DD/MM/YYYY, DD-MM-YYYY | 1985-12-15 |
| `adresse` | Adresse postale | Texte | 5 rue de la Paix |
| `code_postal` | Code postal | 5 chiffres | 75001 |
| `ville` | Ville | Texte | PARIS |
| `vendeur` | Vendeur assigné | Nom complet existant | Marie Martin |
| `statut` | Statut client | prospect/client/actif/inactif | prospect |

### **📝 Exemple de Fichier CSV**
```csv
nom;prenom;email;telephone;naissance;adresse;code_postal;ville;vendeur;statut
PORTE;KYLLIAN;<EMAIL>;0749530760;2008-02-07;12 PLACE DE L YSER;62670;MAZINGARBE;Marie Martin;prospect
DUPONT;Jean;<EMAIL>;0123456789;1985-12-15;5 rue de la Paix;75001;PARIS;;client
MARTIN;Sophie;<EMAIL>;0987654321;1990-03-22;10 avenue des Champs;75008;PARIS;Pierre Durand;prospect
```

---

## 🚀 PROCESSUS D'IMPORT EN 5 ÉTAPES

### **📤 Étape 1 : Sélection du Fichier**
1. **Glisser-déposer** votre fichier CSV dans la zone prévue
2. **Ou cliquer** sur la zone pour sélectionner un fichier
3. **Validation automatique** :
   - Format CSV vérifié
   - Taille < 10MB
   - Nombre de lignes < 1000
4. **Informations affichées** : nom, taille, nombre de lignes

### **🔗 Étape 2 : Correspondance des Colonnes**
1. **Colonnes détectées** affichées à gauche
2. **Mapping automatique** basé sur les noms de colonnes
3. **Ajustement manuel** possible via les listes déroulantes
4. **Colonnes ignorées** si non mappées

### **👁️ Étape 3 : Prévisualisation et Validation**
1. **Statistiques** : Total, Valides, Erreurs
2. **Aperçu** des 10 premières lignes
3. **Validation en temps réel** :
   - Format email
   - Format téléphone français
   - Dates de naissance
   - Codes postaux
   - Vendeurs existants
4. **Option** : Ignorer les erreurs ou arrêter l'import

### **⚡ Étape 4 : Import en Cours**
1. **Barre de progression** animée
2. **Statut en temps réel**
3. **Traitement des données** :
   - Validation finale
   - Insertion en base
   - Gestion des doublons
   - Assignation des vendeurs

### **✅ Étape 5 : Résultats**
1. **Statistiques finales** :
   - Lignes traitées
   - Clients créés
   - Lignes ignorées
   - Erreurs rencontrées
2. **Résumé détaillé** des erreurs
3. **Option d'annulation** (24h)
4. **Historique des imports**

---

## ✅ RÈGLES DE VALIDATION

### **📧 Email (si présent)**
- **Format valide** : <EMAIL>
- **Unicité** : Pas de doublon avec la base existante
- **Action si doublon** : Ligne ignorée ou import arrêté

### **📞 Téléphone (si présent)**
- **Format français** : 10 chiffres commençant par 0
- **Exemples valides** : 0123456789, 01.23.45.67.89
- **Nettoyage automatique** des espaces et points

### **📅 Date de Naissance (si présente)**
- **Formats acceptés** :
  - YYYY-MM-DD (2008-02-07)
  - DD/MM/YYYY (07/02/2008)
  - DD-MM-YYYY (07-02-2008)
- **Conversion automatique** vers YYYY-MM-DD

### **📮 Code Postal (si présent)**
- **Format** : Exactement 5 chiffres
- **Exemples valides** : 75001, 62670

### **👤 Vendeur (si présent)**
- **Doit exister** dans la base de données
- **Format** : Prénom Nom (ex: Marie Martin)
- **Sensible à la casse**

### **🏷️ Statut (si présent)**
- **Valeurs acceptées** : prospect, client, actif, inactif
- **Défaut** : prospect si non spécifié
- **Insensible à la casse**

---

## 🔧 GESTION DES ERREURS

### **⚠️ Types d'Erreurs**
1. **Email invalide** → Format incorrect
2. **Email en doublon** → Déjà existant en base
3. **Téléphone invalide** → Pas 10 chiffres ou ne commence pas par 0
4. **Date invalide** → Format non reconnu
5. **Code postal invalide** → Pas 5 chiffres
6. **Vendeur inexistant** → Nom non trouvé en base

### **🛠️ Options de Traitement**
- **Ignorer les erreurs** ✅ (par défaut)
  - Continue l'import
  - Ignore les lignes avec erreurs
  - Affiche un rapport détaillé
  
- **Arrêter sur erreur** ❌
  - Stoppe l'import à la première erreur
  - Aucune donnée n'est importée
  - Affiche l'erreur spécifique

### **📊 Rapport d'Erreurs**
- **Numéro de ligne** avec erreur
- **Description détaillée** de chaque erreur
- **Données concernées** affichées
- **Export possible** du rapport d'erreurs

---

## 🔄 FONCTIONNALITÉS AVANCÉES

### **📥 Template CSV**
- **Téléchargement** d'un fichier exemple
- **Colonnes pré-remplies** avec données de test
- **Format correct** garanti
- **Bouton** : "Télécharger template"

### **↩️ Annulation d'Import**
- **Disponible** pendant 24h après import
- **Suppression complète** des clients importés
- **Confirmation** requise
- **Traçabilité** maintenue

### **📈 Historique des Imports**
- **Liste** de tous les imports effectués
- **Statistiques** par import
- **Date et utilisateur**
- **Statut** (terminé, annulé, en erreur)

### **🔍 Gestion des Doublons**
- **Détection** basée sur l'email
- **Options** :
  - Ignorer le doublon
  - Arrêter l'import
  - Mettre à jour (future version)

---

## 💡 BONNES PRATIQUES

### **📋 Préparation du Fichier**
1. **Utilisez le template** fourni comme base
2. **Vérifiez les formats** avant import
3. **Nettoyez les données** (espaces, caractères spéciaux)
4. **Testez avec un petit échantillon** d'abord

### **🎯 Optimisation**
1. **Importez par petits lots** (< 500 lignes)
2. **Vérifiez les vendeurs** avant import
3. **Utilisez des emails uniques**
4. **Formatez les dates** correctement

### **🔒 Sécurité**
1. **Vérifiez les données sensibles**
2. **Ne partagez pas** les fichiers CSV
3. **Supprimez les fichiers** après import
4. **Utilisez l'annulation** si nécessaire

---

## 🚨 RÉSOLUTION DE PROBLÈMES

### **❌ "Fichier trop volumineux"**
- **Solution** : Divisez le fichier en plusieurs parties
- **Limite** : 10MB maximum

### **❌ "Trop de lignes"**
- **Solution** : Limitez à 1000 lignes par import
- **Astuce** : Importez en plusieurs fois

### **❌ "Format CSV invalide"**
- **Vérifiez** : Délimiteur point-virgule (;)
- **Vérifiez** : Encodage UTF-8
- **Vérifiez** : En-tête présent

### **❌ "Erreur de connexion"**
- **Vérifiez** : Serveur de base de données démarré
- **Vérifiez** : Connexion réseau
- **Réessayez** après quelques secondes

### **❌ "Vendeur inexistant"**
- **Solution** : Créez le vendeur d'abord
- **Ou** : Laissez le champ vendeur vide
- **Format** : Prénom Nom exact

---

## 📞 SUPPORT

### **🆘 En Cas de Problème**
1. **Vérifiez** le format de votre fichier
2. **Consultez** les messages d'erreur détaillés
3. **Utilisez** le template fourni
4. **Testez** avec moins de données

### **📚 Ressources**
- **Template CSV** : Bouton dans l'interface
- **Exemples** : Données de test incluses
- **Validation** : Temps réel dans l'interface

---

## 🎉 IMPORT RÉUSSI !

Une fois l'import terminé :
- ✅ **Clients créés** et visibles dans la liste
- ✅ **Statistiques** mises à jour
- ✅ **Assignations** aux vendeurs effectives
- ✅ **Historique** enregistré
- ✅ **Possibilité d'annulation** pendant 24h

**🚀 Votre base de données clients est maintenant enrichie !**

---

**📊 Import CSV - BINANCE CRM**  
**Version :** 1.0  
**Dernière mise à jour :** 2025-01-20

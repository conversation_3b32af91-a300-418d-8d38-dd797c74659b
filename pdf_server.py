#!/usr/bin/env python3
"""
BINANCE CRM - Serveur PDF Réel
Serveur Python pour génération réelle de PDFs
"""

import json
import os
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import base64
from datetime import datetime
import sqlite3

# Importation des bibliothèques PDF
try:
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, Image
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.colors import HexColor
    from reportlab.graphics.shapes import Drawing, Rect
    from reportlab.graphics.charts.piecharts import Pie
    from reportlab.graphics.charts.barcharts import VerticalBarChart
    from reportlab.graphics import renderPDF
    REPORTLAB_AVAILABLE = True
except ImportError:
    print("⚠️  ReportLab non installé. Installation automatique...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "reportlab"])
    from reportlab.lib.pagesizes import letter, A4
    from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
    from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
    from reportlab.lib.units import inch
    from reportlab.lib import colors
    from reportlab.lib.colors import HexColor
    REPORTLAB_AVAILABLE = True

class PDFGenerator:
    def __init__(self):
        self.binance_yellow = HexColor('#f1c232')
        self.binance_gold = HexColor('#fcd535')
        self.init_database()
    
    def init_database(self):
        """Initialiser la base de données pour l'historique des PDFs"""
        self.conn = sqlite3.connect('binance_crm_pdfs.db', check_same_thread=False)
        cursor = self.conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS pdf_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_type TEXT,
                title TEXT,
                filename TEXT,
                generated_date TIMESTAMP,
                file_size INTEGER,
                status TEXT
            )
        ''')
        
        self.conn.commit()
    
    def create_binance_header(self, canvas, doc):
        """Créer l'en-tête Binance pour le PDF"""
        canvas.saveState()
        
        # Logo/Titre Binance
        canvas.setFont('Helvetica-Bold', 24)
        canvas.setFillColor(self.binance_yellow)
        canvas.drawString(50, 750, "BINANCE CRM")
        
        # Ligne de séparation
        canvas.setStrokeColor(self.binance_gold)
        canvas.setLineWidth(3)
        canvas.line(50, 740, 550, 740)
        
        # Date de génération
        canvas.setFont('Helvetica', 10)
        canvas.setFillColor(colors.grey)
        canvas.drawRightString(550, 750, f"Généré le: {datetime.now().strftime('%d/%m/%Y à %H:%M')}")
        
        canvas.restoreState()
    
    def create_binance_footer(self, canvas, doc):
        """Créer le pied de page Binance"""
        canvas.saveState()
        
        # Ligne de séparation
        canvas.setStrokeColor(self.binance_gold)
        canvas.setLineWidth(1)
        canvas.line(50, 50, 550, 50)
        
        # Texte du pied de page
        canvas.setFont('Helvetica', 8)
        canvas.setFillColor(colors.grey)
        canvas.drawString(50, 35, "BINANCE CRM - Rapport confidentiel")
        canvas.drawRightString(550, 35, f"Page {doc.page}")
        
        canvas.restoreState()
    
    def generate_sales_report(self, data):
        """Générer un rapport de ventes"""
        filename = f"rapport_ventes_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join('reports', filename)
        
        # Créer le dossier reports s'il n'existe pas
        os.makedirs('reports', exist_ok=True)
        
        # Créer le document PDF
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        # Style personnalisé Binance
        binance_title = ParagraphStyle(
            'BinanceTitle',
            parent=styles['Heading1'],
            fontSize=18,
            textColor=self.binance_yellow,
            spaceAfter=20
        )
        
        # Titre du rapport
        story.append(Paragraph("📊 RAPPORT DE VENTES", binance_title))
        story.append(Spacer(1, 20))
        
        # Informations générales
        info_data = [
            ['Période:', data.get('period', 'Janvier 2024')],
            ['Chiffre d\'affaires total:', data.get('ca_total', '€2.4M')],
            ['Nombre de ventes:', str(data.get('nb_ventes', 127))],
            ['Ticket moyen:', data.get('ticket_moyen', '€18,897')],
            ['Croissance:', data.get('croissance', '+15%')]
        ]
        
        info_table = Table(info_data, colWidths=[3*inch, 2*inch])
        info_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), self.binance_yellow),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 30))
        
        # Top vendeurs
        story.append(Paragraph("🏆 TOP VENDEURS", binance_title))
        story.append(Spacer(1, 10))
        
        vendeurs_data = [['Vendeur', 'CA Généré', 'Nb Ventes', 'Performance']]
        for vendeur in data.get('top_vendeurs', []):
            vendeurs_data.append([
                vendeur.get('nom', ''),
                vendeur.get('ca', ''),
                str(vendeur.get('ventes', 0)),
                'Excellent' if vendeur.get('ventes', 0) > 40 else 'Bon'
            ])
        
        vendeurs_table = Table(vendeurs_data, colWidths=[2*inch, 1.5*inch, 1*inch, 1.5*inch])
        vendeurs_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), self.binance_gold),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        story.append(vendeurs_table)
        story.append(Spacer(1, 30))
        
        # Analyse et recommandations
        story.append(Paragraph("📈 ANALYSE ET RECOMMANDATIONS", binance_title))
        story.append(Spacer(1, 10))
        
        analysis_text = f"""
        <b>Points forts :</b><br/>
        • Croissance de {data.get('croissance', '+15%')} par rapport au mois précédent<br/>
        • Ticket moyen élevé de {data.get('ticket_moyen', '€18,897')}<br/>
        • Performance excellente de l'équipe commerciale<br/><br/>
        
        <b>Recommandations :</b><br/>
        • Maintenir la dynamique commerciale actuelle<br/>
        • Renforcer la formation des vendeurs moins performants<br/>
        • Développer de nouveaux produits pour augmenter le panier moyen<br/>
        """
        
        story.append(Paragraph(analysis_text, styles['Normal']))
        
        # Construire le PDF avec en-tête et pied de page personnalisés
        doc.build(story, onFirstPage=self.create_binance_header, onLaterPages=self.create_binance_header)
        
        # Enregistrer dans l'historique
        file_size = os.path.getsize(filepath)
        self.save_pdf_history('sales', 'Rapport de Ventes', filename, file_size, 'generated')
        
        return filepath
    
    def generate_clients_report(self, data):
        """Générer un rapport clients"""
        filename = f"rapport_clients_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join('reports', filename)
        
        os.makedirs('reports', exist_ok=True)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        binance_title = ParagraphStyle(
            'BinanceTitle',
            parent=styles['Heading1'],
            fontSize=18,
            textColor=self.binance_yellow,
            spaceAfter=20
        )
        
        # Titre
        story.append(Paragraph("👥 RAPPORT CLIENTS", binance_title))
        story.append(Spacer(1, 20))
        
        # Statistiques clients
        client_data = [
            ['Total clients:', str(data.get('total_clients', 127))],
            ['Nouveaux clients:', str(data.get('nouveaux_clients', 38))],
            ['Clients actifs:', str(data.get('clients_actifs', 89))],
            ['Taux de rétention:', data.get('taux_retention', '92%')],
            ['Satisfaction moyenne:', data.get('satisfaction', '4.8/5')]
        ]
        
        client_table = Table(client_data, colWidths=[3*inch, 2*inch])
        client_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), self.binance_yellow),
            ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('GRID', (0, 0), (-1, -1), 1, colors.black)
        ]))
        
        story.append(client_table)
        story.append(Spacer(1, 30))
        
        # Segmentation clients
        story.append(Paragraph("📊 SEGMENTATION CLIENTS", binance_title))
        story.append(Spacer(1, 10))
        
        segments_data = [['Segment', 'Nombre', 'CA Généré', 'Pourcentage']]
        for segment in data.get('segments', []):
            segments_data.append([
                segment.get('nom', ''),
                str(segment.get('count', 0)),
                segment.get('ca', ''),
                f"{round((segment.get('count', 0) / data.get('total_clients', 127)) * 100)}%"
            ])
        
        segments_table = Table(segments_data, colWidths=[2*inch, 1.5*inch, 1.5*inch, 1*inch])
        segments_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), self.binance_gold),
            ('TEXTCOLOR', (0, 0), (-1, 0), colors.black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('GRID', (0, 0), (-1, -1), 1, colors.black),
            ('ROWBACKGROUNDS', (0, 1), (-1, -1), [colors.white, colors.lightgrey])
        ]))
        
        story.append(segments_table)
        
        doc.build(story, onFirstPage=self.create_binance_header, onLaterPages=self.create_binance_header)
        
        file_size = os.path.getsize(filepath)
        self.save_pdf_history('clients', 'Rapport Clients', filename, file_size, 'generated')
        
        return filepath
    
    def generate_custom_report(self, data):
        """Générer un rapport personnalisé"""
        filename = f"rapport_personnalise_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        filepath = os.path.join('reports', filename)
        
        os.makedirs('reports', exist_ok=True)
        
        doc = SimpleDocTemplate(filepath, pagesize=A4)
        styles = getSampleStyleSheet()
        story = []
        
        binance_title = ParagraphStyle(
            'BinanceTitle',
            parent=styles['Heading1'],
            fontSize=18,
            textColor=self.binance_yellow,
            spaceAfter=20
        )
        
        # Titre personnalisé
        title = data.get('title', 'Rapport Personnalisé')
        story.append(Paragraph(f"📋 {title.upper()}", binance_title))
        story.append(Spacer(1, 20))
        
        # Contenu dynamique basé sur les données fournies
        for section_name, section_data in data.get('sections', {}).items():
            story.append(Paragraph(f"📊 {section_name.upper()}", binance_title))
            story.append(Spacer(1, 10))
            
            if isinstance(section_data, dict):
                # Créer un tableau pour les données structurées
                table_data = []
                for key, value in section_data.items():
                    table_data.append([key.replace('_', ' ').title(), str(value)])
                
                if table_data:
                    section_table = Table(table_data, colWidths=[3*inch, 2*inch])
                    section_table.setStyle(TableStyle([
                        ('BACKGROUND', (0, 0), (0, -1), self.binance_yellow),
                        ('TEXTCOLOR', (0, 0), (0, -1), colors.black),
                        ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
                        ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
                        ('FONTSIZE', (0, 0), (-1, -1), 10),
                        ('GRID', (0, 0), (-1, -1), 1, colors.black)
                    ]))
                    story.append(section_table)
            else:
                # Texte simple
                story.append(Paragraph(str(section_data), styles['Normal']))
            
            story.append(Spacer(1, 20))
        
        doc.build(story, onFirstPage=self.create_binance_header, onLaterPages=self.create_binance_header)
        
        file_size = os.path.getsize(filepath)
        self.save_pdf_history('custom', title, filename, file_size, 'generated')
        
        return filepath
    
    def save_pdf_history(self, report_type, title, filename, file_size, status):
        """Sauvegarder l'historique des PDFs générés"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO pdf_history 
            (report_type, title, filename, generated_date, file_size, status)
            VALUES (?, ?, ?, ?, ?, ?)
        ''', (report_type, title, filename, datetime.now(), file_size, status))
        self.conn.commit()
    
    def get_pdf_history(self):
        """Récupérer l'historique des PDFs"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT * FROM pdf_history ORDER BY generated_date DESC LIMIT 50
        ''')
        return cursor.fetchall()

class PDFAPIHandler(BaseHTTPRequestHandler):
    pdf_generator = PDFGenerator()
    
    def do_OPTIONS(self):
        """Gérer les requêtes OPTIONS pour CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """Gérer les requêtes POST"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            path = urlparse(self.path).path
            
            if path == '/api/generate-pdf':
                result = self.handle_generate_pdf(data)
            elif path == '/api/get-pdf-history':
                result = self.handle_get_pdf_history()
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def do_GET(self):
        """Gérer les requêtes GET pour télécharger les PDFs"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        path = urlparse(self.path).path
        
        if path.startswith('/download/'):
            filename = path.replace('/download/', '')
            filepath = os.path.join('reports', filename)
            
            if os.path.exists(filepath):
                self.send_response(200)
                self.send_header('Content-Type', 'application/pdf')
                self.send_header('Content-Disposition', f'attachment; filename="{filename}"')
                self.end_headers()
                
                with open(filepath, 'rb') as f:
                    self.wfile.write(f.read())
            else:
                self.send_response(404)
                self.end_headers()
                self.wfile.write(b'Fichier non trouve')
        else:
            self.send_response(404)
            self.end_headers()
    
    def handle_generate_pdf(self, data):
        """Gérer la génération de PDF"""
        report_type = data.get('type', 'custom')
        
        try:
            if report_type == 'sales':
                filepath = self.pdf_generator.generate_sales_report(data)
            elif report_type == 'clients':
                filepath = self.pdf_generator.generate_clients_report(data)
            else:
                filepath = self.pdf_generator.generate_custom_report(data)
            
            filename = os.path.basename(filepath)
            download_url = f"http://localhost:8003/download/{filename}"
            
            return {
                'success': True,
                'filename': filename,
                'download_url': download_url,
                'file_size': os.path.getsize(filepath)
            }
            
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def handle_get_pdf_history(self):
        """Récupérer l'historique des PDFs"""
        history = self.pdf_generator.get_pdf_history()
        return {'success': True, 'history': history}

def start_pdf_server(port=8003):
    """Démarrer le serveur PDF"""
    server = HTTPServer(('localhost', port), PDFAPIHandler)
    print(f"🚀 Serveur PDF BINANCE CRM démarré sur http://localhost:{port}")
    print("📄 Endpoints disponibles:")
    print("   POST /api/generate-pdf - Générer un PDF")
    print("   POST /api/get-pdf-history - Historique PDFs")
    print("   GET /download/{filename} - Télécharger PDF")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur PDF")
        server.shutdown()

if __name__ == '__main__':
    start_pdf_server()

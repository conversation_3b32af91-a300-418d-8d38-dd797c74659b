#!/usr/bin/env python3
"""
Script d'initialisation des données de démonstration pour le CRM
"""

from datetime import datetime, timedelta
import random
from database import create_tables, get_db
import crud
import schemas

def init_demo_data():
    """Initialise les données de démonstration"""
    
    print("🚀 Initialisation des données de démonstration...")
    
    # Créer les tables
    create_tables()
    db = next(get_db())
    
    # 1. Créer les utilisateurs
    print("👥 Création des utilisateurs...")
    
    # Admin (déjà créé au démarrage)
    admin = crud.get_user_by_username(db, "admin")
    if not admin:
        admin_user = schemas.UserCreate(
            username="admin",
            email="<EMAIL>",
            password="admin123",
            role="admin"
        )
        admin = crud.create_user(db, admin_user)
        print("✅ Utilisateur admin créé")
    
    # Vendeurs
    vendeurs_data = [
        {"username": "marie.martin", "email": "<EMAIL>", "password": "vendeur123"},
        {"username": "pierre.durand", "email": "<EMAIL>", "password": "vendeur123"},
        {"username": "sophie.bernard", "email": "<EMAIL>", "password": "vendeur123"},
    ]
    
    vendeurs = []
    for vendeur_data in vendeurs_data:
        existing = crud.get_user_by_username(db, vendeur_data["username"])
        if not existing:
            vendeur_user = schemas.UserCreate(
                username=vendeur_data["username"],
                email=vendeur_data["email"],
                password=vendeur_data["password"],
                role="vendeur"
            )
            vendeur = crud.create_user(db, vendeur_user)
            vendeurs.append(vendeur)
            print(f"✅ Vendeur {vendeur.username} créé")
        else:
            vendeurs.append(existing)
    
    # 2. Créer les templates d'email
    print("📧 Création des templates d'email...")
    
    templates_data = [
        {
            "nom": "Premier Contact",
            "sujet": "Bonjour {prenom}, découvrez nos services",
            "contenu": """
            <p>Bonjour {prenom} {nom},</p>
            
            <p>J'espère que vous allez bien. Je me permets de vous contacter concernant nos services qui pourraient vous intéresser.</p>
            
            <p>Nous proposons des solutions adaptées à vos besoins. Seriez-vous disponible pour un rendez-vous téléphonique cette semaine ?</p>
            
            <p>Vous pouvez me joindre au téléphone ou répondre directement à cet email.</p>
            
            <p>Cordialement,<br>
            L'équipe CRM</p>
            """,
            "variables": "prenom, nom"
        },
        {
            "nom": "Confirmation RDV",
            "sujet": "Confirmation de votre rendez-vous le {date_rdv}",
            "contenu": """
            <p>Bonjour {prenom},</p>
            
            <p>Je vous confirme notre rendez-vous prévu le <strong>{date_rdv}</strong>.</p>
            
            <p><strong>Détails du rendez-vous :</strong></p>
            <ul>
                <li>Date : {date_rdv}</li>
                <li>Objet : {titre_rdv}</li>
                <li>Description : {description_rdv}</li>
            </ul>
            
            <p>En cas d'empêchement, n'hésitez pas à me contacter.</p>
            
            <p>À bientôt,<br>
            L'équipe CRM</p>
            """,
            "variables": "prenom, date_rdv, titre_rdv, description_rdv"
        },
        {
            "nom": "Relance Client",
            "sujet": "Suite à notre échange - {prenom}",
            "contenu": """
            <p>Bonjour {prenom},</p>
            
            <p>Suite à notre dernier échange, je souhaitais faire le point avec vous.</p>
            
            <p>Avez-vous eu l'occasion de réfléchir à notre proposition ? Je reste à votre disposition pour répondre à toutes vos questions.</p>
            
            <p>N'hésitez pas à me contacter au {telephone} ou par email.</p>
            
            <p>Cordialement,<br>
            L'équipe CRM</p>
            """,
            "variables": "prenom, telephone"
        }
    ]
    
    for template_data in templates_data:
        existing = crud.get_email_template_by_name(db, template_data["nom"])
        if not existing:
            template = schemas.EmailTemplateCreate(**template_data)
            crud.create_email_template(db, template)
            print(f"✅ Template '{template_data['nom']}' créé")
    
    # 3. Créer des clients de démonstration
    print("👤 Création des clients de démonstration...")
    
    clients_data = [
        {"nom": "Dupont", "prenom": "Jean", "email": "<EMAIL>", "telephone": "***********.89", "date_naissance": "1980-05-15", "adresse": "123 Rue de la Paix, 75001 Paris", "indicateur": "nouveau"},
        {"nom": "Martin", "prenom": "Marie", "email": "<EMAIL>", "telephone": "***********.90", "date_naissance": "1975-08-22", "adresse": "456 Avenue des Champs, 69000 Lyon", "indicateur": "en cours"},
        {"nom": "Bernard", "prenom": "Pierre", "email": "<EMAIL>", "telephone": "***********.91", "date_naissance": "1985-12-03", "adresse": "789 Boulevard du Centre, 13000 Marseille", "indicateur": "magnifique"},
        {"nom": "Durand", "prenom": "Sophie", "email": "<EMAIL>", "telephone": "***********.92", "date_naissance": "1990-03-18", "adresse": "321 Place de la République, 31000 Toulouse", "indicateur": "NRP"},
        {"nom": "Moreau", "prenom": "Luc", "email": "<EMAIL>", "telephone": "***********.93", "date_naissance": "1978-11-07", "adresse": "654 Rue du Commerce, 44000 Nantes", "indicateur": "client mort"},
        {"nom": "Simon", "prenom": "Claire", "email": "<EMAIL>", "telephone": "***********.94", "date_naissance": "1982-07-25", "adresse": "987 Avenue de la Liberté, 67000 Strasbourg", "indicateur": "nouveau"},
        {"nom": "Michel", "prenom": "Paul", "email": "<EMAIL>", "telephone": "***********.95", "date_naissance": "1988-01-12", "adresse": "147 Rue de la Gare, 59000 Lille", "indicateur": "en cours"},
        {"nom": "Leroy", "prenom": "Anne", "email": "<EMAIL>", "telephone": "***********.96", "date_naissance": "1983-09-30", "adresse": "258 Boulevard Saint-Michel, 35000 Rennes", "indicateur": "magnifique"},
        {"nom": "Roux", "prenom": "Marc", "email": "<EMAIL>", "telephone": "***********.97", "date_naissance": "1979-04-14", "adresse": "369 Place du Marché, 21000 Dijon", "indicateur": "nouveau"},
        {"nom": "Fournier", "prenom": "Julie", "email": "<EMAIL>", "telephone": "***********.98", "date_naissance": "1986-06-08", "adresse": "741 Rue des Écoles, 87000 Limoges", "indicateur": "en cours"},
    ]
    
    for i, client_data in enumerate(clients_data):
        existing = crud.get_user_by_email(db, client_data["email"])
        if not existing:
            # Attribuer aléatoirement à un vendeur
            vendeur = random.choice(vendeurs) if vendeurs else None
            
            client_data["date_naissance"] = datetime.strptime(client_data["date_naissance"], "%Y-%m-%d")
            client_data["vendeur_id"] = vendeur.id if vendeur else None
            client_data["note"] = f"Client de démonstration #{i+1}"
            
            client = schemas.ClientCreate(**client_data)
            crud.create_client(db, client)
            print(f"✅ Client {client_data['prenom']} {client_data['nom']} créé")
    
    # 4. Créer quelques rendez-vous
    print("📅 Création des rendez-vous de démonstration...")
    
    clients = crud.get_clients(db, limit=5)
    for i, client in enumerate(clients):
        if client.vendeur_id:
            # Créer un RDV dans le futur
            rdv_date = datetime.now() + timedelta(days=random.randint(1, 30))
            
            appointment_data = schemas.AppointmentCreate(
                client_id=client.id,
                vendeur_id=client.vendeur_id,
                date_rdv=rdv_date,
                titre=f"Rendez-vous commercial avec {client.prenom} {client.nom}",
                description=f"Présentation de nos services à {client.prenom}",
                statut="planifie"
            )
            
            crud.create_appointment(db, appointment_data)
            print(f"✅ RDV créé pour {client.prenom} {client.nom}")
    
    # 5. Marquer quelques emails comme envoyés
    print("📧 Simulation d'envois d'emails...")
    
    templates = crud.get_email_templates(db)
    if templates:
        template = templates[0]  # Premier template
        
        # Marquer quelques clients comme ayant reçu un email
        for client in clients[:3]:
            crud.update_client(db, client.id, schemas.ClientUpdate(
                email_envoye=True,
                template_utilise=template.nom
            ))
            print(f"✅ Email marqué comme envoyé pour {client.prenom} {client.nom}")
    
    print("\n🎉 Données de démonstration créées avec succès !")
    print("\n📋 Résumé :")
    print(f"   👥 Utilisateurs : 1 admin + {len(vendeurs)} vendeurs")
    print(f"   📧 Templates : {len(templates_data)} templates d'email")
    print(f"   👤 Clients : {len(clients_data)} clients de démonstration")
    print(f"   📅 Rendez-vous : 5 rendez-vous planifiés")
    print("\n🔑 Connexion :")
    print("   Admin : admin / admin123")
    print("   Vendeurs : marie.martin / vendeur123 (et autres)")

if __name__ == "__main__":
    init_demo_data()

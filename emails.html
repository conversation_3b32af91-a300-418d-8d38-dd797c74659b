<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système d'Emails - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
        }
        
        .template-card {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--binance-yellow);
        }
        
        .template-card.selected {
            border-color: var(--binance-yellow);
            background-color: rgba(241, 194, 50, 0.1);
        }
        
        .email-preview {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
        }
        
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">Dashboard</a>
                <a class="nav-link" href="clients.html">Clients</a>
                <a class="nav-link" href="vendeurs.html">Vendeurs</a>
                <a class="nav-link active" href="emails.html">Emails</a>
                <a class="nav-link" href="reports.html">Rapports</a>
                <a class="nav-link" href="#" onclick="logout()">Déconnexion</a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="bi bi-envelope"></i> Système d'Emails
                </h1>
                <p class="text-muted">Templates Binance • Envoi automatisé • Suivi des campagnes</p>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="showCampaignModal()">
                    <i class="bi bi-broadcast"></i> Nouvelle Campagne
                </button>
                <button class="btn btn-info" onclick="showStatsModal()">
                    <i class="bi bi-graph-up"></i> Statistiques
                </button>
                <button class="btn btn-primary" onclick="showComposeModal()">
                    <i class="bi bi-envelope-plus"></i> Composer Email
                </button>
            </div>
        </div>
        
        <!-- Statistiques emails -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-envelope-check fs-1 text-success mb-2"></i>
                        <h3 class="fw-bold">254</h3>
                        <p class="text-muted mb-0">Emails Envoyés</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-envelope-open fs-1 text-info mb-2"></i>
                        <h3 class="fw-bold">216</h3>
                        <p class="text-muted mb-0">Emails Ouverts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-cursor-fill fs-1 text-warning mb-2"></i>
                        <h3 class="fw-bold">89</h3>
                        <p class="text-muted mb-0">Clics</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-percent fs-1 text-primary mb-2"></i>
                        <h3 class="fw-bold">85%</h3>
                        <p class="text-muted mb-0">Taux d'Ouverture</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Templates d'emails -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-envelope-paper"></i> Templates Binance</h5>
                    </div>
                    <div class="card-body">
                        <div class="template-card card mb-3" onclick="selectTemplate('welcome')">
                            <div class="card-body">
                                <h6><i class="bi bi-hand-thumbs-up"></i> Email de Bienvenue</h6>
                                <small class="text-muted">Accueil des nouveaux clients crypto</small>
                            </div>
                        </div>
                        
                        <div class="template-card card mb-3" onclick="selectTemplate('follow-up')">
                            <div class="card-body">
                                <h6><i class="bi bi-arrow-repeat"></i> Email de Suivi</h6>
                                <small class="text-muted">Relance des prospects inactifs</small>
                            </div>
                        </div>
                        
                        <div class="template-card card mb-3" onclick="selectTemplate('promo')">
                            <div class="card-body">
                                <h6><i class="bi bi-gift"></i> Email Promotionnel</h6>
                                <small class="text-muted">Offres spéciales Binance</small>
                            </div>
                        </div>
                        
                        <div class="template-card card mb-3" onclick="selectTemplate('newsletter')">
                            <div class="card-body">
                                <h6><i class="bi bi-newspaper"></i> Newsletter</h6>
                                <small class="text-muted">Actualités crypto hebdomadaires</small>
                            </div>
                        </div>
                        
                        <div class="template-card card mb-3" onclick="selectTemplate('rdv')">
                            <div class="card-body">
                                <h6><i class="bi bi-calendar-event"></i> Confirmation RDV</h6>
                                <small class="text-muted">Rappel de rendez-vous</small>
                            </div>
                        </div>
                        
                        <button class="btn btn-outline-primary w-100" onclick="createTemplate()">
                            <i class="bi bi-plus"></i> Nouveau Template
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Prévisualisation -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-eye"></i> Prévisualisation</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary" onclick="previewDesktop()">
                                <i class="bi bi-laptop"></i> Desktop
                            </button>
                            <button class="btn btn-outline-secondary" onclick="previewMobile()">
                                <i class="bi bi-phone"></i> Mobile
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="email-preview" id="emailPreview">
                            <div class="text-center text-muted">
                                <i class="bi bi-envelope" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">Sélectionnez un template</h5>
                                <p>Choisissez un template dans la liste pour voir la prévisualisation</p>
                            </div>
                        </div>
                        
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary" onclick="editTemplate()">
                                    <i class="bi bi-pencil"></i> Modifier
                                </button>
                                <button class="btn btn-outline-info" onclick="testEmail()">
                                    <i class="bi bi-send"></i> Test
                                </button>
                            </div>
                            <button class="btn btn-primary" onclick="sendEmail()">
                                <i class="bi bi-envelope-check"></i> Envoyer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Historique des emails -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="bi bi-clock-history"></i> Historique des Envois</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Template</th>
                                <th>Destinataires</th>
                                <th>Statut</th>
                                <th>Taux d'ouverture</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="emailHistoryBody">
                            <tr>
                                <td>20/01/2024 14:30</td>
                                <td><span class="badge bg-primary">Newsletter</span></td>
                                <td>127 clients</td>
                                <td><span class="badge bg-success">Envoyé</span></td>
                                <td>85%</td>
                                <td>
                                    <button class="btn btn-outline-info btn-sm" onclick="viewStats(1)">
                                        <i class="bi bi-bar-chart"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>19/01/2024 10:15</td>
                                <td><span class="badge bg-warning">Promo</span></td>
                                <td>89 prospects</td>
                                <td><span class="badge bg-success">Envoyé</span></td>
                                <td>78%</td>
                                <td>
                                    <button class="btn btn-outline-info btn-sm" onclick="viewStats(2)">
                                        <i class="bi bi-bar-chart"></i>
                                    </button>
                                </td>
                            </tr>
                            <tr>
                                <td>18/01/2024 16:45</td>
                                <td><span class="badge bg-info">Suivi</span></td>
                                <td>45 clients</td>
                                <td><span class="badge bg-success">Envoyé</span></td>
                                <td>92%</td>
                                <td>
                                    <button class="btn btn-outline-info btn-sm" onclick="viewStats(3)">
                                        <i class="bi bi-bar-chart"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Statut système -->
        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-check-circle"></i> Système d'Emails Binance Opérationnel :</h6>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Templates</strong><br>
                    <small>5 templates Binance professionnels</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Envoi</strong><br>
                    <small>Système d'envoi automatisé</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Suivi</strong><br>
                    <small>Statistiques et analytics</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Personnalisation</strong><br>
                    <small>Variables dynamiques</small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedTemplate = null;
        
        const templates = {
            'welcome': {
                subject: 'Bienvenue chez Binance !',
                content: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); padding: 20px; text-align: center;">
                            <h1 style="color: #000; margin: 0;">🪙 Bienvenue chez Binance !</h1>
                        </div>
                        <div style="padding: 30px; background: #fff;">
                            <h2>Bonjour {{prenom}},</h2>
                            <p>Nous sommes ravis de vous accueillir dans l'univers Binance !</p>
                            <p>Votre conseiller <strong>{{vendeur}}</strong> vous contactera prochainement pour vous accompagner dans vos premiers pas dans le monde des cryptomonnaies.</p>
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="#" style="background: #f1c232; color: #000; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Commencer</a>
                            </div>
                            <p>À bientôt,<br>L'équipe Binance</p>
                        </div>
                    </div>
                `
            },
            'follow-up': {
                subject: 'Reprenons contact - Binance',
                content: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); padding: 20px; text-align: center;">
                            <h1 style="color: #000; margin: 0;">📞 Reprenons contact</h1>
                        </div>
                        <div style="padding: 30px; background: #fff;">
                            <h2>Bonjour {{prenom}},</h2>
                            <p>Nous n'avons pas eu de nouvelles depuis quelque temps...</p>
                            <p>Votre conseiller {{vendeur}} souhaiterait reprendre contact avec vous pour discuter de vos projets crypto.</p>
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="#" style="background: #f1c232; color: #000; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Planifier un RDV</a>
                            </div>
                        </div>
                    </div>
                `
            },
            'promo': {
                subject: '🎁 Offre spéciale Binance !',
                content: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); padding: 20px; text-align: center;">
                            <h1 style="color: #000; margin: 0;">🎁 Offre Spéciale !</h1>
                        </div>
                        <div style="padding: 30px; background: #fff;">
                            <h2>{{prenom}}, profitez de notre offre exclusive !</h2>
                            <p>Pour une durée limitée, bénéficiez de <strong>0% de frais</strong> sur vos premiers trades.</p>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;">
                                <h3 style="color: #f1c232;">✨ Avantages exclusifs :</h3>
                                <ul>
                                    <li>0% de frais pendant 30 jours</li>
                                    <li>Support prioritaire</li>
                                    <li>Formations gratuites</li>
                                </ul>
                            </div>
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="#" style="background: #f1c232; color: #000; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Profiter de l'offre</a>
                            </div>
                        </div>
                    </div>
                `
            },
            'newsletter': {
                subject: '📰 Newsletter Crypto Hebdomadaire',
                content: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); padding: 20px; text-align: center;">
                            <h1 style="color: #000; margin: 0;">📰 Newsletter Crypto</h1>
                        </div>
                        <div style="padding: 30px; background: #fff;">
                            <h2>Les actualités crypto de la semaine</h2>
                            <div style="border-left: 4px solid #f1c232; padding-left: 20px; margin: 20px 0;">
                                <h3>📈 Bitcoin en hausse de 5%</h3>
                                <p>Le Bitcoin continue sa progression cette semaine...</p>
                            </div>
                            <div style="border-left: 4px solid #f1c232; padding-left: 20px; margin: 20px 0;">
                                <h3>🚀 Nouvelle fonctionnalité Binance</h3>
                                <p>Découvrez notre nouveau système de staking...</p>
                            </div>
                        </div>
                    </div>
                `
            },
            'rdv': {
                subject: 'Confirmation de votre RDV - Binance',
                content: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); padding: 20px; text-align: center;">
                            <h1 style="color: #000; margin: 0;">📅 Confirmation RDV</h1>
                        </div>
                        <div style="padding: 30px; background: #fff;">
                            <h2>Bonjour {{prenom}},</h2>
                            <p>Votre rendez-vous avec {{vendeur}} est confirmé :</p>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
                                <h3>📅 {{date}} à {{heure}}</h3>
                                <p>Durée estimée : 30 minutes</p>
                            </div>
                            <p>Nous vous rappelons 15 minutes avant le rendez-vous.</p>
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="#" style="background: #f1c232; color: #000; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Ajouter au calendrier</a>
                            </div>
                        </div>
                    </div>
                `
            }
        };
        
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        function selectTemplate(templateId) {
            // Retirer la sélection précédente
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Sélectionner le nouveau template
            event.currentTarget.classList.add('selected');
            selectedTemplate = templateId;
            
            // Afficher la prévisualisation
            const template = templates[templateId];
            if (template) {
                document.getElementById('emailPreview').innerHTML = template.content;
            }
        }
        
        function previewDesktop() {
            document.getElementById('emailPreview').style.maxWidth = '100%';
        }
        
        function previewMobile() {
            document.getElementById('emailPreview').style.maxWidth = '375px';
        }
        
        function editTemplate() {
            if (!selectedTemplate) {
                alert('Veuillez sélectionner un template');
                return;
            }
            alert(`Édition du template ${selectedTemplate} (fonctionnalité en développement)`);
        }
        
        function testEmail() {
            if (!selectedTemplate) {
                alert('Veuillez sélectionner un template');
                return;
            }
            alert(`Email de test envoyé avec le template ${selectedTemplate}`);
        }
        
        function sendEmail() {
            if (!selectedTemplate) {
                alert('Veuillez sélectionner un template');
                return;
            }
            alert(`Envoi d'email avec le template ${selectedTemplate} (fonctionnalité en développement)`);
        }
        
        function createTemplate() {
            alert('Création de nouveau template (fonctionnalité en développement)');
        }
        
        function showComposeModal() {
            alert('Composer un email personnalisé (fonctionnalité en développement)');
        }
        
        function showCampaignModal() {
            alert('Nouvelle campagne email (fonctionnalité en développement)');
        }
        
        function showStatsModal() {
            alert('Statistiques détaillées des emails (fonctionnalité en développement)');
        }
        
        function viewStats(id) {
            alert(`Statistiques de la campagne ${id} (fonctionnalité en développement)`);
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
        
        console.log('📧 BINANCE CRM Emails - Page chargée avec succès');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Système d'Emails - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
        }
        
        .template-card {
            transition: all 0.3s ease;
            cursor: pointer;
            border: 2px solid transparent;
        }
        
        .template-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: var(--binance-yellow);
        }
        
        .template-card.selected {
            border-color: var(--binance-yellow);
            background-color: rgba(241, 194, 50, 0.1);
        }
        
        .email-preview {
            background: #fff;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 20px;
            min-height: 300px;
        }
        
        .stat-card {
            transition: all 0.3s ease;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">Dashboard</a>
                <a class="nav-link" href="clients.html">Clients</a>
                <a class="nav-link" href="vendeurs.html">Vendeurs</a>
                <a class="nav-link active" href="emails.html">Emails</a>
                <a class="nav-link" href="reports.html">Rapports</a>
                <a class="nav-link" href="#" onclick="logout()">Déconnexion</a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="bi bi-envelope"></i> Système d'Emails
                </h1>
                <p class="text-muted">Templates Binance • Envoi automatisé • Suivi des campagnes</p>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="showCampaignModal()">
                    <i class="bi bi-broadcast"></i> Nouvelle Campagne
                </button>
                <button class="btn btn-info" onclick="showStatsModal()">
                    <i class="bi bi-graph-up"></i> Statistiques
                </button>
                <button class="btn btn-primary" onclick="showComposeModal()">
                    <i class="bi bi-envelope-plus"></i> Composer Email
                </button>
            </div>
        </div>
        
        <!-- Statistiques emails -->
        <div class="row mb-4">
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-envelope-check fs-1 text-success mb-2"></i>
                        <h3 class="fw-bold">254</h3>
                        <p class="text-muted mb-0">Emails Envoyés</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-envelope-open fs-1 text-info mb-2"></i>
                        <h3 class="fw-bold">216</h3>
                        <p class="text-muted mb-0">Emails Ouverts</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-cursor-fill fs-1 text-warning mb-2"></i>
                        <h3 class="fw-bold">89</h3>
                        <p class="text-muted mb-0">Clics</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3 mb-3">
                <div class="card stat-card text-center">
                    <div class="card-body">
                        <i class="bi bi-percent fs-1 text-primary mb-2"></i>
                        <h3 class="fw-bold">85%</h3>
                        <p class="text-muted mb-0">Taux d'Ouverture</p>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Templates d'emails -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-envelope-paper"></i> Templates Binance</h5>
                    </div>
                    <div class="card-body">
                        <div class="template-card card mb-3" onclick="selectTemplate('rdv')">
                            <div class="card-body">
                                <h6><i class="bi bi-calendar-event"></i> Confirmation RDV</h6>
                                <small class="text-muted">Rappel de rendez-vous</small>
                            </div>
                        </div>

                        <button class="btn btn-outline-primary w-100" onclick="createTemplate()">
                            <i class="bi bi-plus"></i> Nouveau Template
                        </button>
                    </div>
                </div>
            </div>
            
            <!-- Prévisualisation -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-eye"></i> Prévisualisation</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary" onclick="previewDesktop()">
                                <i class="bi bi-laptop"></i> Desktop
                            </button>
                            <button class="btn btn-outline-secondary" onclick="previewMobile()">
                                <i class="bi bi-phone"></i> Mobile
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="email-preview" id="emailPreview">
                            <div class="text-center text-muted">
                                <i class="bi bi-envelope" style="font-size: 3rem;"></i>
                                <h5 class="mt-3">Sélectionnez un template</h5>
                                <p>Choisissez un template dans la liste pour voir la prévisualisation</p>
                            </div>
                        </div>
                        
                        <div class="mt-3 d-flex justify-content-between">
                            <div class="btn-group">
                                <button class="btn btn-outline-secondary" onclick="editTemplate()">
                                    <i class="bi bi-pencil"></i> Modifier
                                </button>
                                <button class="btn btn-outline-info" onclick="testEmail()">
                                    <i class="bi bi-send"></i> Test
                                </button>
                            </div>
                            <button class="btn btn-primary" onclick="sendEmail()">
                                <i class="bi bi-envelope-check"></i> Envoyer
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Historique des emails -->
        <div class="card mt-4">
            <div class="card-header">
                <h5><i class="bi bi-clock-history"></i> Historique des Envois</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Date</th>
                                <th>Template</th>
                                <th>Destinataires</th>
                                <th>Statut</th>
                                <th>Taux d'ouverture</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="emailHistoryBody">
                            <tr>
                                <td>20/01/2024 14:30</td>
                                <td><span class="badge bg-secondary">RDV</span></td>
                                <td>15 clients</td>
                                <td><span class="badge bg-success">Envoyé</span></td>
                                <td>95%</td>
                                <td>
                                    <button class="btn btn-outline-info btn-sm" onclick="viewStats(1)">
                                        <i class="bi bi-bar-chart"></i>
                                    </button>
                                </td>
                            </tr>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Statut système -->
        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-check-circle"></i> Système d'Emails Binance Opérationnel :</h6>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Templates</strong><br>
                    <small>1 template RDV + templates personnalisés</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Envoi</strong><br>
                    <small>Système d'envoi automatisé</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Suivi</strong><br>
                    <small>Statistiques et analytics</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Personnalisation</strong><br>
                    <small>Variables dynamiques</small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        let selectedTemplate = null;
        
        const templates = {
            'rdv': {
                subject: 'Confirmation de votre RDV - Binance',
                content: `
                    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
                        <div style="background: linear-gradient(135deg, #f1c232 0%, #fcd535 100%); padding: 20px; text-align: center;">
                            <h1 style="color: #000; margin: 0;">📅 Confirmation RDV</h1>
                        </div>
                        <div style="padding: 30px; background: #fff;">
                            <h2>Bonjour {{prenom}},</h2>
                            <p>Votre rendez-vous avec {{vendeur}} est confirmé :</p>
                            <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0; text-align: center;">
                                <h3>📅 {{date}} à {{heure}}</h3>
                                <p>Durée estimée : 30 minutes</p>
                            </div>
                            <p>Nous vous rappelons 15 minutes avant le rendez-vous.</p>
                            <div style="text-align: center; margin: 30px 0;">
                                <a href="#" style="background: #f1c232; color: #000; padding: 15px 30px; text-decoration: none; border-radius: 5px; font-weight: bold;">Ajouter au calendrier</a>
                            </div>
                        </div>
                    </div>
                `
            }
        };
        
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        function selectTemplate(templateId) {
            // Retirer la sélection précédente
            document.querySelectorAll('.template-card').forEach(card => {
                card.classList.remove('selected');
            });
            
            // Sélectionner le nouveau template
            event.currentTarget.classList.add('selected');
            selectedTemplate = templateId;
            
            // Afficher la prévisualisation
            const template = templates[templateId];
            if (template) {
                document.getElementById('emailPreview').innerHTML = template.content;
            }
        }
        
        function previewDesktop() {
            document.getElementById('emailPreview').style.maxWidth = '100%';
        }
        
        function previewMobile() {
            document.getElementById('emailPreview').style.maxWidth = '375px';
        }
        
        function editTemplate() {
            if (!selectedTemplate) {
                showNotification('Veuillez sélectionner un template', 'error');
                return;
            }

            const template = templates[selectedTemplate];

            // Créer le modal d'édition
            const modalHtml = `
                <div class="modal fade" id="editTemplateModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-pencil"></i> Éditer Template - ${selectedTemplate}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-3">
                                            <label class="form-label">Objet de l'email</label>
                                            <input type="text" class="form-control" id="editSubject" value="${template.subject}">
                                        </div>
                                        <div class="mb-3">
                                            <label class="form-label">Contenu HTML</label>
                                            <textarea class="form-control" id="editContent" rows="20" style="font-family: monospace; font-size: 12px;">${template.content.trim()}</textarea>
                                        </div>
                                        <div class="mb-3">
                                            <small class="text-muted">
                                                Variables disponibles: {{prenom}}, {{nom}}, {{vendeur}}, {{date}}, {{heure}}
                                            </small>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <label class="form-label">Prévisualisation</label>
                                        <div id="editPreview" class="border p-3" style="height: 500px; overflow-y: auto; background: #fff;">
                                            ${template.content}
                                        </div>
                                        <button type="button" class="btn btn-outline-secondary btn-sm mt-2" onclick="updatePreview()">
                                            <i class="bi bi-arrow-clockwise"></i> Actualiser Prévisualisation
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" onclick="saveTemplate()">
                                    <i class="bi bi-check"></i> Sauvegarder
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Supprimer l'ancien modal s'il existe
            const existingModal = document.getElementById('editTemplateModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById('editTemplateModal')).show();
        }
        
        function confirmSendEmail() {
            const recipients = document.getElementById('emailRecipients').value;
            const subject = document.getElementById('emailSubject').value;
            const vendeurName = document.getElementById('vendeurName').value;
            const isScheduled = document.getElementById('scheduleEmail').checked;

            let recipientCount = 0;
            switch(recipients) {
                case 'all': recipientCount = 127; break;
                case 'nouveaux': recipientCount = 38; break;
                case 'actifs': recipientCount = 89; break;
                case 'custom': recipientCount = 25; break;
                default: recipientCount = 127;
            }

            // Fermer le modal
            bootstrap.Modal.getInstance(document.getElementById('sendEmailModal')).hide();

            if (isScheduled) {
                const scheduleDate = document.getElementById('scheduleDate').value;
                const scheduleTime = document.getElementById('scheduleTime').value;

                showNotification(`Email programmé pour le ${scheduleDate} à ${scheduleTime}`, 'info');

                // Simuler la programmation
                setTimeout(() => {
                    simulateEmailSending(recipients, subject, vendeurName, recipientCount, true);
                }, 3000); // Simulation après 3 secondes
            } else {
                // Simuler l'envoi immédiat avec progression
                simulateEmailSending(recipients, subject, vendeurName, recipientCount, false);
            }
        }

        function simulateEmailSending(recipients, subject, vendeurName, recipientCount, isScheduled) {
            // Créer une barre de progression pour l'envoi
            const progressModal = createProgressModal('Envoi d\'emails en cours...', recipientCount);

            let sent = 0;
            const interval = setInterval(() => {
                sent += Math.floor(Math.random() * 5) + 1; // Envoyer 1-5 emails par fois

                if (sent >= recipientCount) {
                    sent = recipientCount;
                    clearInterval(interval);

                    // Finaliser l'envoi
                    setTimeout(() => {
                        bootstrap.Modal.getInstance(document.getElementById('progressModal')).hide();

                        // Créer les données d'email avec statistiques réalistes
                        const emailData = {
                            template: selectedTemplate,
                            subject: subject,
                            recipients: recipients,
                            recipientCount: recipientCount,
                            vendeur: vendeurName,
                            scheduled: isScheduled,
                            date: new Date().toISOString(),
                            status: 'sent',
                            delivered: Math.floor(recipientCount * (0.95 + Math.random() * 0.05)), // 95-100% livré
                            opened: Math.floor(recipientCount * (0.70 + Math.random() * 0.25)), // 70-95% ouvert
                            clicked: Math.floor(recipientCount * (0.15 + Math.random() * 0.20)), // 15-35% cliqué
                            bounced: Math.floor(recipientCount * (0.01 + Math.random() * 0.04)), // 1-5% bounce
                            unsubscribed: Math.floor(recipientCount * (0.001 + Math.random() * 0.009)) // 0.1-1% désabonné
                        };

                        // Ajouter à l'historique
                        addToEmailHistory(emailData);

                        // Afficher les résultats détaillés
                        showEmailResults(emailData);

                    }, 1000);
                }

                // Mettre à jour la progression
                updateProgressModal(sent, recipientCount);

            }, 200); // Mise à jour toutes les 200ms
        }

        function createProgressModal(title, total) {
            const modalHtml = `
                <div class="modal fade" id="progressModal" tabindex="-1" data-bs-backdrop="static">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-send"></i> ${title}
                                </h5>
                            </div>
                            <div class="modal-body text-center">
                                <div class="mb-3">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="visually-hidden">Envoi en cours...</span>
                                    </div>
                                </div>
                                <div class="progress mb-3" style="height: 25px;">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         id="emailProgress" style="width: 0%">0%</div>
                                </div>
                                <p id="progressText">Préparation de l'envoi...</p>
                                <small class="text-muted" id="progressDetails">0 / ${total} emails envoyés</small>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Supprimer l'ancien modal s'il existe
            const existingModal = document.getElementById('progressModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById('progressModal')).show();

            return document.getElementById('progressModal');
        }

        function updateProgressModal(sent, total) {
            const percentage = Math.round((sent / total) * 100);
            const progressBar = document.getElementById('emailProgress');
            const progressText = document.getElementById('progressText');
            const progressDetails = document.getElementById('progressDetails');

            if (progressBar) {
                progressBar.style.width = percentage + '%';
                progressBar.textContent = percentage + '%';
            }

            if (progressText) {
                if (percentage < 50) {
                    progressText.textContent = 'Envoi en cours...';
                } else if (percentage < 90) {
                    progressText.textContent = 'Traitement des destinataires...';
                } else {
                    progressText.textContent = 'Finalisation de l\'envoi...';
                }
            }

            if (progressDetails) {
                progressDetails.textContent = `${sent} / ${total} emails envoyés`;
            }
        }

        function showEmailResults(emailData) {
            const resultsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card text-center border-success">
                            <div class="card-body">
                                <h4 class="text-success">${emailData.delivered}</h4>
                                <p class="mb-0">Emails Livrés</p>
                                <small class="text-muted">${Math.round((emailData.delivered/emailData.recipientCount)*100)}%</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <h4 class="text-info">${emailData.opened}</h4>
                                <p class="mb-0">Emails Ouverts</p>
                                <small class="text-muted">${Math.round((emailData.opened/emailData.recipientCount)*100)}%</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mt-3">
                        <div class="card text-center border-warning">
                            <div class="card-body">
                                <h4 class="text-warning">${emailData.clicked}</h4>
                                <p class="mb-0">Clics</p>
                                <small class="text-muted">${Math.round((emailData.clicked/emailData.recipientCount)*100)}%</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6 mt-3">
                        <div class="card text-center border-danger">
                            <div class="card-body">
                                <h4 class="text-danger">${emailData.bounced}</h4>
                                <p class="mb-0">Bounces</p>
                                <small class="text-muted">${Math.round((emailData.bounced/emailData.recipientCount)*100)}%</small>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-4">
                    <h6>Détails de la campagne</h6>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <tr><td><strong>Template:</strong></td><td>${emailData.template}</td></tr>
                            <tr><td><strong>Objet:</strong></td><td>${emailData.subject}</td></tr>
                            <tr><td><strong>Destinataires:</strong></td><td>${emailData.recipientCount}</td></tr>
                            <tr><td><strong>Vendeur:</strong></td><td>${emailData.vendeur}</td></tr>
                            <tr><td><strong>Date d'envoi:</strong></td><td>${new Date(emailData.date).toLocaleString('fr-FR')}</td></tr>
                            <tr><td><strong>Taux de livraison:</strong></td><td><span class="badge bg-success">${Math.round((emailData.delivered/emailData.recipientCount)*100)}%</span></td></tr>
                            <tr><td><strong>Taux d'ouverture:</strong></td><td><span class="badge bg-info">${Math.round((emailData.opened/emailData.recipientCount)*100)}%</span></td></tr>
                            <tr><td><strong>Taux de clic:</strong></td><td><span class="badge bg-warning">${Math.round((emailData.clicked/emailData.recipientCount)*100)}%</span></td></tr>
                        </table>
                    </div>
                </div>
            `;

            showModal('📊 Résultats de l\'Envoi Email', resultsHtml);
            showNotification(`✅ Campagne terminée ! ${emailData.delivered} emails livrés avec ${Math.round((emailData.opened/emailData.recipientCount)*100)}% d'ouverture`, 'success');
        }

        function testEmail() {
            if (!selectedTemplate) {
                showNotification('Veuillez sélectionner un template', 'error');
                return;
            }

            // Simuler l'envoi d'un email de test
            const testEmailData = {
                template: selectedTemplate,
                subject: templates[selectedTemplate].subject + ' [TEST]',
                recipients: 'test',
                recipientCount: 1,
                vendeur: 'Test User',
                scheduled: false,
                date: new Date().toISOString(),
                status: 'test'
            };

            addToEmailHistory(testEmailData);
            showNotification(`Email de test envoyé avec le template ${selectedTemplate}`, 'success');
        }
        
        function sendEmail() {
            if (!selectedTemplate) {
                showNotification('Veuillez sélectionner un template', 'error');
                return;
            }

            // Créer le modal de sélection des destinataires
            const modalHtml = `
                <div class="modal fade" id="sendEmailModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-envelope-check"></i> Envoyer Email - ${templates[selectedTemplate].subject}
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">Destinataires</label>
                                    <select class="form-select" id="emailRecipients" multiple>
                                        <option value="all">Tous les clients (127)</option>
                                        <option value="nouveaux">Clients nouveaux (38)</option>
                                        <option value="actifs">Clients actifs (89)</option>
                                        <option value="custom">Sélection personnalisée</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Objet de l'email</label>
                                    <input type="text" class="form-control" id="emailSubject" value="${templates[selectedTemplate].subject}">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Variables personnalisées</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="text" class="form-control mb-2" id="vendeurName" placeholder="Nom du vendeur" value="Marie Martin">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="text" class="form-control mb-2" id="customVar" placeholder="Variable personnalisée">
                                        </div>
                                    </div>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="scheduleEmail">
                                    <label class="form-check-label" for="scheduleEmail">
                                        Programmer l'envoi
                                    </label>
                                </div>
                                <div id="scheduleOptions" style="display: none;">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="date" class="form-control" id="scheduleDate">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="time" class="form-control" id="scheduleTime">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" onclick="confirmSendEmail()">
                                    <i class="bi bi-send"></i> Envoyer Maintenant
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Supprimer l'ancien modal s'il existe
            const existingModal = document.getElementById('sendEmailModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Gérer l'affichage des options de programmation
            document.getElementById('scheduleEmail').addEventListener('change', function() {
                document.getElementById('scheduleOptions').style.display = this.checked ? 'block' : 'none';
            });

            new bootstrap.Modal(document.getElementById('sendEmailModal')).show();
        }
        
        function updatePreview() {
            const content = document.getElementById('editContent').value;
            document.getElementById('editPreview').innerHTML = content;
        }

        function saveTemplate() {
            const subject = document.getElementById('editSubject').value;
            const content = document.getElementById('editContent').value;

            if (!subject || !content) {
                showNotification('Veuillez remplir tous les champs', 'error');
                return;
            }

            // Sauvegarder le template modifié
            templates[selectedTemplate].subject = subject;
            templates[selectedTemplate].content = content;

            // Sauvegarder dans localStorage
            localStorage.setItem('binance_crm_templates', JSON.stringify(templates));

            // Mettre à jour la prévisualisation principale
            document.getElementById('emailPreview').innerHTML = content;

            // Fermer le modal
            bootstrap.Modal.getInstance(document.getElementById('editTemplateModal')).hide();

            showNotification('Template sauvegardé avec succès !', 'success');
        }

        function createTemplate() {
            const modalHtml = `
                <div class="modal fade" id="newTemplateModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-plus"></i> Nouveau Template
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">Nom du template</label>
                                    <input type="text" class="form-control" id="newTemplateName" placeholder="ex: suivi-client">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Objet de l'email</label>
                                    <input type="text" class="form-control" id="newTemplateSubject" placeholder="Objet de l'email">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Contenu HTML</label>
                                    <textarea class="form-control" id="newTemplateContent" rows="10" placeholder="Contenu HTML du template..."></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" onclick="saveNewTemplate()">
                                    <i class="bi bi-check"></i> Créer Template
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            const existingModal = document.getElementById('newTemplateModal');
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById('newTemplateModal')).show();
        }

        function saveNewTemplate() {
            const name = document.getElementById('newTemplateName').value.trim();
            const subject = document.getElementById('newTemplateSubject').value.trim();
            const content = document.getElementById('newTemplateContent').value.trim();

            if (!name || !subject || !content) {
                showNotification('Veuillez remplir tous les champs', 'error');
                return;
            }

            if (templates[name]) {
                showNotification('Un template avec ce nom existe déjà', 'error');
                return;
            }

            // Ajouter le nouveau template
            templates[name] = { subject, content };

            // Sauvegarder dans localStorage
            localStorage.setItem('binance_crm_templates', JSON.stringify(templates));

            // Ajouter à la liste des templates
            const templatesList = document.querySelector('.card-body');
            const newTemplateHtml = `
                <div class="template-card card mb-3" onclick="selectTemplate('${name}')">
                    <div class="card-body">
                        <h6><i class="bi bi-envelope"></i> ${name}</h6>
                        <small class="text-muted">${subject}</small>
                    </div>
                </div>
            `;

            const createButton = templatesList.querySelector('.btn-outline-primary');
            createButton.insertAdjacentHTML('beforebegin', newTemplateHtml);

            bootstrap.Modal.getInstance(document.getElementById('newTemplateModal')).hide();
            showNotification('Nouveau template créé avec succès !', 'success');
        }
        
        // Historique des emails
        let emailHistory = [];

        function addToEmailHistory(emailData) {
            emailHistory.unshift({
                id: emailHistory.length + 1,
                ...emailData,
                openRate: Math.floor(Math.random() * 30) + 70, // 70-100%
                clickRate: Math.floor(Math.random() * 20) + 10  // 10-30%
            });

            // Sauvegarder dans localStorage
            localStorage.setItem('binance_crm_email_history', JSON.stringify(emailHistory));

            // Mettre à jour l'affichage de l'historique
            updateEmailHistoryDisplay();
        }

        function updateEmailHistoryDisplay() {
            const tbody = document.getElementById('emailHistoryBody');
            if (tbody && emailHistory.length > 0) {
                tbody.innerHTML = '';
                emailHistory.slice(0, 10).forEach(email => {
                    const statusBadge = email.status === 'sent' ? 'bg-success' : email.status === 'test' ? 'bg-info' : 'bg-warning';
                    const templateBadge = getTemplateBadgeColor(email.template);

                    const row = `
                        <tr>
                            <td>${new Date(email.date).toLocaleString('fr-FR')}</td>
                            <td><span class="badge ${templateBadge}">${email.template}</span></td>
                            <td>${email.recipientCount} ${email.status === 'test' ? 'test' : 'clients'}</td>
                            <td><span class="badge ${statusBadge}">${email.status === 'sent' ? 'Envoyé' : email.status === 'test' ? 'Test' : 'Programmé'}</span></td>
                            <td>${email.openRate}%</td>
                            <td>
                                <button class="btn btn-outline-info btn-sm" onclick="viewEmailStats(${email.id})">
                                    <i class="bi bi-bar-chart"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            }
        }

        function getTemplateBadgeColor(template) {
            const colors = {
                'rdv': 'bg-secondary',
                'custom': 'bg-primary'
            };
            return colors[template] || 'bg-primary';
        }

        function viewEmailStats(id) {
            const email = emailHistory.find(e => e.id === id);
            if (email) {
                const statsHtml = `
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-primary">${email.recipientCount}</h4>
                                    <p class="mb-0">Envoyés</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-success">${Math.floor(email.recipientCount * email.openRate / 100)}</h4>
                                    <p class="mb-0">Ouverts</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-info">${Math.floor(email.recipientCount * email.clickRate / 100)}</h4>
                                    <p class="mb-0">Clics</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h4 class="text-warning">${email.openRate}%</h4>
                                    <p class="mb-0">Taux d'ouverture</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-3">
                        <h6>Détails de la campagne</h6>
                        <p><strong>Template:</strong> ${email.template}</p>
                        <p><strong>Objet:</strong> ${email.subject}</p>
                        <p><strong>Date d'envoi:</strong> ${new Date(email.date).toLocaleString('fr-FR')}</p>
                        <p><strong>Vendeur:</strong> ${email.vendeur}</p>
                    </div>
                `;

                showModal('Statistiques Email', statsHtml);
            }
        }

        function showComposeModal() {
            const modalHtml = `
                <div class="modal fade" id="composeModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-envelope-plus"></i> Composer Email Personnalisé
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">Destinataire</label>
                                    <input type="email" class="form-control" id="composeRecipient" placeholder="<EMAIL>">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Objet</label>
                                    <input type="text" class="form-control" id="composeSubject" placeholder="Objet de l'email">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Message</label>
                                    <textarea class="form-control" id="composeMessage" rows="8" placeholder="Votre message..."></textarea>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" onclick="sendCustomEmail()">
                                    <i class="bi bi-send"></i> Envoyer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            showModalFromHtml(modalHtml, 'composeModal');
        }

        function sendCustomEmail() {
            const recipient = document.getElementById('composeRecipient').value;
            const subject = document.getElementById('composeSubject').value;
            const message = document.getElementById('composeMessage').value;

            if (!recipient || !subject || !message) {
                showNotification('Veuillez remplir tous les champs', 'error');
                return;
            }

            const emailData = {
                template: 'custom',
                subject: subject,
                recipients: 'custom',
                recipientCount: 1,
                vendeur: 'Utilisateur actuel',
                scheduled: false,
                date: new Date().toISOString(),
                status: 'sent'
            };

            addToEmailHistory(emailData);
            bootstrap.Modal.getInstance(document.getElementById('composeModal')).hide();
            showNotification('Email personnalisé envoyé avec succès !', 'success');
        }

        function showCampaignModal() {
            showNotification('Création de campagne email avancée (fonctionnalité en développement)', 'info');
        }

        function showStatsModal() {
            const totalSent = emailHistory.reduce((sum, email) => sum + email.recipientCount, 0);
            const avgOpenRate = emailHistory.length > 0 ?
                Math.floor(emailHistory.reduce((sum, email) => sum + email.openRate, 0) / emailHistory.length) : 0;

            const statsHtml = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-primary">${emailHistory.length}</h3>
                                <p class="mb-0">Campagnes Totales</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-success">${totalSent}</h3>
                                <p class="mb-0">Emails Envoyés</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-info">${avgOpenRate}%</h3>
                                <p class="mb-0">Taux d'Ouverture Moyen</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card text-center">
                            <div class="card-body">
                                <h3 class="text-warning">${Object.keys(templates).length}</h3>
                                <p class="mb-0">Templates Disponibles</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            showModal('Statistiques Globales', statsHtml);
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        // Fonctions utilitaires
        function showModal(title, content) {
            const modalHtml = `
                <div class="modal fade" id="genericModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            showModalFromHtml(modalHtml, 'genericModal');
        }

        function showModalFromHtml(modalHtml, modalId) {
            const existingModal = document.getElementById(modalId);
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById(modalId)).show();
        }

        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'info': 'alert-info',
                'warning': 'alert-warning'
            };

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass[type]} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Charger les données depuis localStorage
        function loadEmailData() {
            // Charger les templates personnalisés
            const savedTemplates = localStorage.getItem('binance_crm_templates');
            if (savedTemplates) {
                try {
                    const parsedTemplates = JSON.parse(savedTemplates);
                    Object.assign(templates, parsedTemplates);
                } catch (error) {
                    console.error('Erreur lors du chargement des templates:', error);
                }
            }

            // Charger l'historique des emails
            const savedHistory = localStorage.getItem('binance_crm_email_history');
            if (savedHistory) {
                try {
                    emailHistory = JSON.parse(savedHistory);
                    updateEmailHistoryDisplay();
                } catch (error) {
                    console.error('Erreur lors du chargement de l\'historique:', error);
                }
            }
        }

        // Gérer les paramètres URL (pour les liens depuis la gestion clients)
        function handleUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            const clientEmail = urlParams.get('email');
            const clientName = urlParams.get('nom');

            if (clientEmail && clientName) {
                showNotification(`Email pré-configuré pour ${clientName} (${clientEmail})`, 'info');
                // Pré-sélectionner le template RDV
                selectTemplate('rdv');
            }
        }

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadEmailData();
            handleUrlParams();
        });
        
        console.log('📧 BINANCE CRM Emails - Page chargée avec succès');
    </script>
</body>
</html>

#!/usr/bin/env python3
"""
BINANCE CRM - Audit des Fonctionnalités Cachées
Analyse exhaustive des fonctionnalités non exposées ou partiellement implémentées
"""

import os
import re
import json
import sqlite3
from pathlib import Path
from typing import Dict, List, Any

class HiddenFeaturesAuditor:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.findings = {
            'hidden_endpoints': [],
            'unused_methods': [],
            'commented_features': [],
            'partial_implementations': [],
            'missing_ui_elements': [],
            'configuration_options': [],
            'developer_tools': [],
            'database_features': []
        }
        
    def audit_database_server(self):
        """Auditer database_server.py pour les fonctionnalités cachées"""
        print("🔍 Audit de database_server.py...")
        
        file_path = self.base_dir / "database_server.py"
        if not file_path.exists():
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Chercher les méthodes non exposées dans les endpoints
        methods = re.findall(r'def ([a-zA-Z_][a-zA-Z0-9_]*)\(self', content)
        exposed_methods = re.findall(r'self\.db_manager\.([a-zA-Z_][a-zA-Z0-9_]*)', content)
        
        hidden_methods = set(methods) - set(exposed_methods) - {
            '__init__', 'get_connection', 'init_database', 'init_default_data'
        }
        
        for method in hidden_methods:
            # Analyser la méthode pour comprendre sa fonction
            method_pattern = rf'def {method}\(.*?\):(.*?)(?=def|\Z)'
            method_match = re.search(method_pattern, content, re.DOTALL)
            if method_match:
                method_content = method_match.group(1)
                docstring_match = re.search(r'"""(.*?)"""', method_content, re.DOTALL)
                description = docstring_match.group(1).strip() if docstring_match else "Pas de description"
                
                self.findings['unused_methods'].append({
                    'method': method,
                    'file': 'database_server.py',
                    'description': description,
                    'complexity': 'medium' if len(method_content) > 500 else 'easy'
                })
        
        # Chercher les endpoints potentiels non implémentés
        endpoint_patterns = [
            r"path == '/api/([^']+)'",
            r"path\.startswith\('/api/([^']+)'\)"
        ]
        
        for pattern in endpoint_patterns:
            endpoints = re.findall(pattern, content)
            for endpoint in endpoints:
                if endpoint not in ['clients', 'users', 'vendeurs', 'health']:
                    self.findings['hidden_endpoints'].append({
                        'endpoint': f'/api/{endpoint}',
                        'file': 'database_server.py',
                        'status': 'implemented'
                    })
    
    def audit_email_server(self):
        """Auditer email_server.py pour les fonctionnalités cachées"""
        print("📧 Audit de email_server.py...")
        
        file_path = self.base_dir / "email_server.py"
        if not file_path.exists():
            return
        
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Fonctionnalités email avancées détectées
        advanced_features = [
            ('send_bulk_email', 'Envoi d\'emails en masse'),
            ('schedule_email', 'Programmation d\'emails'),
            ('email_templates_advanced', 'Templates avancés avec variables'),
            ('email_analytics', 'Analytiques des emails'),
            ('bounce_handling', 'Gestion des rebonds'),
            ('unsubscribe_management', 'Gestion des désabonnements')
        ]
        
        for feature, description in advanced_features:
            if feature in content:
                self.findings['partial_implementations'].append({
                    'feature': feature,
                    'file': 'email_server.py',
                    'description': description,
                    'status': 'partial'
                })
    
    def audit_html_pages(self):
        """Auditer les pages HTML pour les éléments cachés"""
        print("🌐 Audit des pages HTML...")
        
        html_files = [
            'dashboard.html', 'clients.html', 'vendeurs.html', 
            'emails.html', 'reports.html', 'admin_config.html'
        ]
        
        for html_file in html_files:
            file_path = self.base_dir / html_file
            if not file_path.exists():
                continue
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Chercher les éléments cachés ou désactivés
            hidden_elements = re.findall(r'<[^>]*(?:hidden|disabled|style="[^"]*display:\s*none)[^>]*>(.*?)</[^>]*>', content, re.DOTALL)
            
            for element in hidden_elements:
                if element.strip() and len(element) > 10:
                    self.findings['missing_ui_elements'].append({
                        'element': element[:100] + '...' if len(element) > 100 else element,
                        'file': html_file,
                        'type': 'hidden_element'
                    })
            
            # Chercher les boutons ou fonctionnalités commentées
            commented_features = re.findall(r'<!--\s*(.*?)\s*-->', content, re.DOTALL)
            
            for comment in commented_features:
                if any(keyword in comment.lower() for keyword in ['button', 'feature', 'function', 'todo']):
                    self.findings['commented_features'].append({
                        'comment': comment[:200] + '...' if len(comment) > 200 else comment,
                        'file': html_file,
                        'type': 'commented_feature'
                    })
    
    def audit_database_schema(self):
        """Auditer le schéma de base de données pour les fonctionnalités non utilisées"""
        print("🗄️  Audit du schéma de base de données...")
        
        db_path = self.base_dir / "binance_crm.db"
        if not db_path.exists():
            return
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Analyser les tables et colonnes
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            for table in tables:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                
                # Identifier les colonnes potentiellement non utilisées
                unused_columns = []
                for col in columns:
                    col_name = col[1]
                    # Vérifier si la colonne est utilisée dans le code
                    if self.is_column_unused(table, col_name):
                        unused_columns.append(col_name)
                
                if unused_columns:
                    self.findings['database_features'].append({
                        'table': table,
                        'unused_columns': unused_columns,
                        'potential_features': self.suggest_features_for_columns(table, unused_columns)
                    })
            
            conn.close()
            
        except Exception as e:
            print(f"  ⚠️  Erreur audit base de données: {e}")
    
    def is_column_unused(self, table, column):
        """Vérifier si une colonne est utilisée dans le code"""
        # Colonnes système toujours utilisées
        system_columns = ['id', 'created_date', 'updated_date']
        if column in system_columns:
            return False
        
        # Chercher dans tous les fichiers Python
        for py_file in self.base_dir.glob("*.py"):
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if column in content:
                        return False
            except:
                continue
        
        return True
    
    def suggest_features_for_columns(self, table, columns):
        """Suggérer des fonctionnalités basées sur les colonnes non utilisées"""
        suggestions = []
        
        feature_mapping = {
            'priority': 'Système de priorités',
            'tags': 'Système de tags/étiquettes',
            'category': 'Catégorisation avancée',
            'score': 'Système de scoring',
            'rating': 'Système de notation',
            'source': 'Tracking de la source',
            'campaign': 'Gestion de campagnes',
            'budget': 'Gestion budgétaire',
            'deadline': 'Gestion des échéances',
            'reminder': 'Système de rappels',
            'attachment': 'Gestion des pièces jointes',
            'location': 'Géolocalisation',
            'timezone': 'Gestion des fuseaux horaires'
        }
        
        for column in columns:
            for keyword, feature in feature_mapping.items():
                if keyword in column.lower():
                    suggestions.append(feature)
                    break
        
        return suggestions
    
    def audit_configuration_files(self):
        """Auditer les fichiers de configuration pour les options cachées"""
        print("⚙️ Audit des fichiers de configuration...")
        
        config_files = [
            'smtp_config.json',
            'config.json',
            'settings.json'
        ]
        
        for config_file in config_files:
            file_path = self.base_dir / config_file
            if file_path.exists():
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        config = json.load(f)
                    
                    self.analyze_config_options(config_file, config)
                    
                except Exception as e:
                    print(f"  ⚠️  Erreur lecture {config_file}: {e}")
    
    def analyze_config_options(self, filename, config):
        """Analyser les options de configuration"""
        hidden_options = []
        
        def analyze_dict(d, prefix=""):
            for key, value in d.items():
                full_key = f"{prefix}.{key}" if prefix else key
                
                # Options potentiellement cachées
                if key.lower() in ['debug', 'dev', 'development', 'test', 'admin', 'advanced']:
                    hidden_options.append({
                        'option': full_key,
                        'value': value,
                        'type': 'developer_option'
                    })
                
                if isinstance(value, dict):
                    analyze_dict(value, full_key)
        
        analyze_dict(config)
        
        if hidden_options:
            self.findings['configuration_options'].extend([{
                'file': filename,
                'options': hidden_options
            }])
    
    def identify_missing_crm_features(self):
        """Identifier les fonctionnalités CRM manquantes standard"""
        print("📋 Identification des fonctionnalités CRM manquantes...")
        
        standard_crm_features = [
            {
                'category': 'Gestion des Leads',
                'features': [
                    'Lead scoring automatique',
                    'Qualification des leads',
                    'Conversion lead vers client',
                    'Pipeline de vente visuel',
                    'Prévisions de vente'
                ]
            },
            {
                'category': 'Automatisation',
                'features': [
                    'Workflows automatisés',
                    'Déclencheurs d\'actions',
                    'Séquences d\'emails',
                    'Tâches automatiques',
                    'Notifications intelligentes'
                ]
            },
            {
                'category': 'Analytiques Avancées',
                'features': [
                    'Tableaux de bord personnalisables',
                    'Rapports programmés',
                    'Analyse de cohorte',
                    'Métriques de performance',
                    'ROI par campagne'
                ]
            },
            {
                'category': 'Intégrations',
                'features': [
                    'API REST complète',
                    'Webhooks entrants/sortants',
                    'Intégration calendrier',
                    'Synchronisation email',
                    'Connecteurs tiers'
                ]
            },
            {
                'category': 'Collaboration',
                'features': [
                    'Commentaires et notes partagées',
                    'Assignation de tâches',
                    'Historique des activités',
                    'Notifications d\'équipe',
                    'Permissions granulaires'
                ]
            }
        ]
        
        # Vérifier quelles fonctionnalités sont déjà implémentées
        for category_data in standard_crm_features:
            category = category_data['category']
            missing_features = []
            
            for feature in category_data['features']:
                if not self.is_feature_implemented(feature):
                    missing_features.append(feature)
            
            if missing_features:
                self.findings['partial_implementations'].append({
                    'category': category,
                    'missing_features': missing_features,
                    'implementation_difficulty': self.estimate_difficulty(missing_features),
                    'business_value': self.estimate_business_value(category)
                })
    
    def is_feature_implemented(self, feature):
        """Vérifier si une fonctionnalité est déjà implémentée"""
        # Mots-clés pour détecter l'implémentation
        keywords = {
            'lead scoring': ['score', 'scoring', 'qualification'],
            'workflow': ['workflow', 'automation', 'trigger'],
            'dashboard': ['dashboard', 'chart', 'graph'],
            'webhook': ['webhook', 'callback', 'integration'],
            'notification': ['notification', 'alert', 'notify']
        }
        
        feature_lower = feature.lower()
        for key_feature, search_terms in keywords.items():
            if key_feature in feature_lower:
                # Chercher dans les fichiers
                for py_file in self.base_dir.glob("*.py"):
                    try:
                        with open(py_file, 'r', encoding='utf-8') as f:
                            content = f.read().lower()
                            if any(term in content for term in search_terms):
                                return True
                    except:
                        continue
        
        return False
    
    def estimate_difficulty(self, features):
        """Estimer la difficulté d'implémentation"""
        complex_features = ['workflow', 'automation', 'integration', 'api', 'analytics']
        
        complexity_score = 0
        for feature in features:
            feature_lower = feature.lower()
            if any(complex_term in feature_lower for complex_term in complex_features):
                complexity_score += 3
            else:
                complexity_score += 1
        
        avg_complexity = complexity_score / len(features) if features else 0
        
        if avg_complexity >= 2.5:
            return 'hard'
        elif avg_complexity >= 1.5:
            return 'medium'
        else:
            return 'easy'
    
    def estimate_business_value(self, category):
        """Estimer la valeur business d'une catégorie"""
        high_value_categories = ['Gestion des Leads', 'Automatisation', 'Analytiques Avancées']
        
        if category in high_value_categories:
            return 'high'
        else:
            return 'medium'
    
    def generate_report(self):
        """Générer le rapport d'audit"""
        print("\n📊 Génération du rapport d'audit...")
        
        report = {
            'summary': {
                'total_findings': sum(len(findings) for findings in self.findings.values()),
                'categories': len([k for k, v in self.findings.items() if v]),
                'high_priority_items': 0
            },
            'findings': self.findings,
            'recommendations': self.generate_recommendations()
        }
        
        # Compter les éléments haute priorité
        for category, items in self.findings.items():
            for item in items:
                if isinstance(item, dict) and item.get('business_value') == 'high':
                    report['summary']['high_priority_items'] += 1
        
        return report
    
    def generate_recommendations(self):
        """Générer des recommandations basées sur les findings"""
        recommendations = []
        
        if self.findings['unused_methods']:
            recommendations.append({
                'priority': 'medium',
                'category': 'API Enhancement',
                'title': 'Exposer les méthodes non utilisées',
                'description': f"Exposer {len(self.findings['unused_methods'])} méthodes via des endpoints API",
                'effort': 'low'
            })
        
        if self.findings['missing_ui_elements']:
            recommendations.append({
                'priority': 'high',
                'category': 'UI/UX',
                'title': 'Activer les éléments UI cachés',
                'description': f"Activer {len(self.findings['missing_ui_elements'])} éléments d'interface cachés",
                'effort': 'low'
            })
        
        if self.findings['database_features']:
            recommendations.append({
                'priority': 'medium',
                'category': 'Database',
                'title': 'Utiliser les colonnes de base de données non exploitées',
                'description': "Implémenter des fonctionnalités basées sur les colonnes existantes",
                'effort': 'medium'
            })
        
        return recommendations
    
    def run_complete_audit(self):
        """Exécuter l'audit complet"""
        print("🔍 AUDIT COMPLET DES FONCTIONNALITÉS CACHÉES - BINANCE CRM")
        print("=" * 70)
        
        self.audit_database_server()
        self.audit_email_server()
        self.audit_html_pages()
        self.audit_database_schema()
        self.audit_configuration_files()
        self.identify_missing_crm_features()
        
        report = self.generate_report()
        
        # Sauvegarder le rapport
        report_file = self.base_dir / "audit_fonctionnalites_rapport.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ Audit terminé - Rapport sauvegardé dans {report_file}")
        return report

if __name__ == "__main__":
    auditor = HiddenFeaturesAuditor()
    report = auditor.run_complete_audit()
    
    print(f"\n📊 RÉSUMÉ:")
    print(f"  Total des découvertes: {report['summary']['total_findings']}")
    print(f"  Catégories analysées: {report['summary']['categories']}")
    print(f"  Éléments haute priorité: {report['summary']['high_priority_items']}")

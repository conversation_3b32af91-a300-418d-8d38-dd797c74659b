#!/usr/bin/env python3
"""
BINANCE CRM - Fix Critical #1: Add Meta Viewport Tags
Automatically adds mobile viewport meta tags to all HTML files
"""

import re
from pathlib import Path

def fix_viewport_tags():
    """Add viewport meta tags to all HTML files"""
    
    base_dir = Path(__file__).parent
    html_files = [
        'dashboard.html',
        'clients.html', 
        'vendeurs.html',
        'emails.html',
        'reports.html',
        'admin_config.html',
        'login.html'
    ]
    
    viewport_meta = '''<meta name="viewport" content="width=device-width, initial-scale=1, shrink-to-fit=no">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-capable" content="yes">'''
    
    print("🔧 CORRECTION CRITIQUE #1: Ajout des meta viewport")
    print("="*60)
    
    for html_file in html_files:
        file_path = base_dir / html_file
        
        if not file_path.exists():
            print(f"  ❌ {html_file} - Fichier non trouvé")
            continue
            
        try:
            # <PERSON>re le fichier
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Vérifier si viewport existe déjà
            if 'name="viewport"' in content:
                print(f"  ✅ {html_file} - Viewport déjà présent")
                continue
            
            # Trouver la balise <head> et ajouter après
            head_pattern = r'(<head[^>]*>)'
            if re.search(head_pattern, content):
                new_content = re.sub(
                    head_pattern, 
                    r'\1\n    ' + viewport_meta,
                    content
                )
                
                # Sauvegarder le fichier modifié
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"  ✅ {html_file} - Meta viewport ajouté")
            else:
                print(f"  ❌ {html_file} - Balise <head> non trouvée")
                
        except Exception as e:
            print(f"  ❌ {html_file} - Erreur: {str(e)}")
    
    print(f"\n🎯 CORRECTION TERMINÉE")
    print("📱 Testez maintenant sur mobile/tablette")

if __name__ == "__main__":
    fix_viewport_tags()

"""
Middleware personnalisés pour l'application CRM
"""

import time
from typing import Callable
from fastapi import Request, Response
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.types import ASGIApp
import logging

from utils.security import SecurityHeaders, check_rate_limit, log_security_event
from utils.logging_config import log_api_request, log_security_event as log_sec_event

logger = logging.getLogger(__name__)

class SecurityMiddleware(BaseHTTPMiddleware):
    """
    Middleware de sécurité pour ajouter les headers et vérifications
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
        self.security_headers = SecurityHeaders.get_security_headers()
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Vérifier le rate limiting
        client_ip = request.client.host if request.client else "unknown"
        
        # Rate limiting plus strict pour les endpoints sensibles
        sensitive_endpoints = ['/login', '/api/users', '/api/smtp/config']
        is_sensitive = any(request.url.path.startswith(endpoint) for endpoint in sensitive_endpoints)
        
        max_requests = 5 if is_sensitive else 100
        window_minutes = 15 if is_sensitive else 60
        
        if not check_rate_limit(client_ip, max_requests, window_minutes):
            log_sec_event(
                "rate_limit_exceeded",
                {
                    "ip": client_ip,
                    "endpoint": request.url.path,
                    "user_agent": request.headers.get("user-agent", "unknown")
                },
                client_ip
            )
            
            return JSONResponse(
                status_code=429,
                content={"detail": "Trop de requêtes. Veuillez réessayer plus tard."},
                headers=self.security_headers
            )
        
        # Vérifier les headers suspects
        suspicious_headers = [
            'x-forwarded-for',
            'x-real-ip',
            'x-originating-ip'
        ]
        
        for header in suspicious_headers:
            if header in request.headers:
                log_sec_event(
                    "suspicious_header",
                    {
                        "header": header,
                        "value": request.headers[header],
                        "ip": client_ip,
                        "endpoint": request.url.path
                    },
                    client_ip
                )
        
        # Vérifier la taille du body pour éviter les attaques DoS
        if request.method in ["POST", "PUT", "PATCH"]:
            content_length = request.headers.get("content-length")
            if content_length:
                try:
                    size = int(content_length)
                    max_size = 50 * 1024 * 1024  # 50MB
                    
                    if size > max_size:
                        log_sec_event(
                            "oversized_request",
                            {
                                "size": size,
                                "max_size": max_size,
                                "ip": client_ip,
                                "endpoint": request.url.path
                            },
                            client_ip
                        )
                        
                        return JSONResponse(
                            status_code=413,
                            content={"detail": "Requête trop volumineuse"},
                            headers=self.security_headers
                        )
                except ValueError:
                    pass
        
        # Traiter la requête
        response = await call_next(request)
        
        # Ajouter les headers de sécurité
        for header, value in self.security_headers.items():
            response.headers[header] = value
        
        return response

class PerformanceMiddleware(BaseHTTPMiddleware):
    """
    Middleware de monitoring des performances
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # Traiter la requête
        response = await call_next(request)
        
        # Calculer le temps de réponse
        process_time = time.time() - start_time
        response_time_ms = round(process_time * 1000, 2)
        
        # Ajouter le header de temps de réponse
        response.headers["X-Process-Time"] = str(response_time_ms)
        
        # Logger les requêtes lentes
        if process_time > 2.0:  # Plus de 2 secondes
            logger.warning(
                f"Slow request: {request.method} {request.url.path} took {response_time_ms}ms",
                extra={
                    'action': 'slow_request',
                    'extra_data': {
                        'method': request.method,
                        'path': request.url.path,
                        'response_time_ms': response_time_ms,
                        'status_code': response.status_code
                    }
                }
            )
        
        # Logger toutes les requêtes API
        if request.url.path.startswith('/api/'):
            user_id = None
            # Essayer de récupérer l'ID utilisateur depuis la session
            if hasattr(request, 'session') and 'user_id' in request.session:
                user_id = request.session['user_id']
            
            client_ip = request.client.host if request.client else None
            
            log_api_request(
                request.method,
                request.url.path,
                user_id,
                client_ip,
                response.status_code,
                process_time
            )
        
        return response

class ErrorHandlingMiddleware(BaseHTTPMiddleware):
    """
    Middleware de gestion d'erreurs globales
    """
    
    def __init__(self, app: ASGIApp):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        try:
            response = await call_next(request)
            return response
        
        except Exception as exc:
            # Logger l'erreur
            client_ip = request.client.host if request.client else "unknown"
            
            logger.error(
                f"Unhandled exception: {str(exc)}",
                exc_info=True,
                extra={
                    'action': 'unhandled_exception',
                    'client_ip': client_ip,
                    'extra_data': {
                        'method': request.method,
                        'path': request.url.path,
                        'exception_type': type(exc).__name__,
                        'exception_message': str(exc)
                    }
                }
            )
            
            # Log de sécurité si c'est une erreur suspecte
            suspicious_errors = [
                'FileNotFoundError',
                'PermissionError',
                'ValueError',
                'KeyError'
            ]
            
            if type(exc).__name__ in suspicious_errors:
                log_sec_event(
                    "suspicious_error",
                    {
                        "exception_type": type(exc).__name__,
                        "exception_message": str(exc),
                        "endpoint": request.url.path,
                        "method": request.method
                    },
                    client_ip
                )
            
            # Retourner une erreur générique pour ne pas exposer d'informations
            return JSONResponse(
                status_code=500,
                content={
                    "detail": "Une erreur interne s'est produite. Veuillez réessayer plus tard.",
                    "error_id": f"ERR_{int(time.time())}"
                },
                headers=SecurityHeaders.get_security_headers()
            )

class CORSMiddleware(BaseHTTPMiddleware):
    """
    Middleware CORS personnalisé avec sécurité renforcée
    """
    
    def __init__(self, app: ASGIApp, allowed_origins: list = None):
        super().__init__(app)
        self.allowed_origins = allowed_origins or ["http://localhost:8000", "https://localhost:8000"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        origin = request.headers.get("origin")
        
        # Vérifier l'origine pour les requêtes CORS
        if origin and origin not in self.allowed_origins:
            log_sec_event(
                "cors_violation",
                {
                    "origin": origin,
                    "allowed_origins": self.allowed_origins,
                    "endpoint": request.url.path
                },
                request.client.host if request.client else "unknown"
            )
            
            return JSONResponse(
                status_code=403,
                content={"detail": "Origine non autorisée"},
                headers=SecurityHeaders.get_security_headers()
            )
        
        # Traiter les requêtes preflight
        if request.method == "OPTIONS":
            response = Response()
            if origin in self.allowed_origins:
                response.headers["Access-Control-Allow-Origin"] = origin
                response.headers["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
                response.headers["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
                response.headers["Access-Control-Max-Age"] = "86400"
            return response
        
        response = await call_next(request)
        
        # Ajouter les headers CORS pour les réponses normales
        if origin in self.allowed_origins:
            response.headers["Access-Control-Allow-Origin"] = origin
            response.headers["Access-Control-Allow-Credentials"] = "true"
        
        return response

class CompressionMiddleware(BaseHTTPMiddleware):
    """
    Middleware de compression pour améliorer les performances
    """
    
    def __init__(self, app: ASGIApp, minimum_size: int = 1024):
        super().__init__(app)
        self.minimum_size = minimum_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # Vérifier si le client accepte la compression
        accept_encoding = request.headers.get("accept-encoding", "")
        
        if "gzip" in accept_encoding:
            # Vérifier la taille de la réponse
            content_length = response.headers.get("content-length")
            
            if content_length and int(content_length) >= self.minimum_size:
                # Ajouter le header pour indiquer que la compression est supportée
                response.headers["Vary"] = "Accept-Encoding"
        
        return response

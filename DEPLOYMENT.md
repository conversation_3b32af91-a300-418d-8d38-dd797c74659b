# 🚀 Guide de Déploiement - CRM System

Ce guide détaille les différentes méthodes de déploiement de l'application CRM en production.

## 📋 Prérequis de Production

### Serveur
- **OS** : Ubuntu 20.04+ / CentOS 8+ / Debian 11+
- **RAM** : Minimum 2GB, recommandé 4GB+
- **CPU** : 2 cores minimum
- **Stockage** : 20GB minimum (SSD recommandé)
- **Réseau** : Connexion internet stable

### Logiciels
- **Python 3.8+**
- **PostgreSQL 12+** (recommandé) ou **MySQL 8.0+**
- **Nginx** (proxy inverse)
- **Certbot** (certificats SSL)
- **Supervisor** ou **systemd** (gestion des processus)

## 🐳 Déploiement avec Docker (Recommandé)

### 1. Créer le Dockerfile

```dockerfile
FROM python:3.11-slim

# Variables d'environnement
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV DEBIAN_FRONTEND=noninteractive

# Installer les dépendances système
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    libpq-dev \
    curl \
    && rm -rf /var/lib/apt/lists/*

# Créer l'utilisateur de l'application
RUN useradd --create-home --shell /bin/bash crm

# Définir le répertoire de travail
WORKDIR /app

# Copier les fichiers de dépendances
COPY requirements.txt .

# Installer les dépendances Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt

# Copier le code de l'application
COPY . .

# Créer les dossiers nécessaires
RUN mkdir -p logs static/uploads && \
    chown -R crm:crm /app

# Changer vers l'utilisateur non-root
USER crm

# Exposer le port
EXPOSE 8000

# Commande de démarrage
CMD ["gunicorn", "main:app", "--host", "0.0.0.0", "--port", "8000", "--workers", "4", "--worker-class", "uvicorn.workers.UvicornWorker"]
```

### 2. Créer docker-compose.yml

```yaml
version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: crm_db
      POSTGRES_USER: crm_user
      POSTGRES_PASSWORD: ${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    restart: unless-stopped

  app:
    build: .
    environment:
      - DATABASE_URL=postgresql://crm_user:${DB_PASSWORD}@db:5432/crm_db
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - DEBUG=false
    ports:
      - "8000:8000"
    depends_on:
      - db
      - redis
    volumes:
      - ./logs:/app/logs
      - ./static/uploads:/app/static/uploads
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  postgres_data:
```

### 3. Configuration Nginx

```nginx
events {
    worker_connections 1024;
}

http {
    upstream app {
        server app:8000;
    }

    server {
        listen 80;
        server_name your-domain.com;
        return 301 https://$server_name$request_uri;
    }

    server {
        listen 443 ssl http2;
        server_name your-domain.com;

        ssl_certificate /etc/nginx/ssl/cert.pem;
        ssl_certificate_key /etc/nginx/ssl/key.pem;

        client_max_body_size 50M;

        location / {
            proxy_pass http://app;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        location /static/ {
            alias /app/static/;
            expires 30d;
            add_header Cache-Control "public, immutable";
        }
    }
}
```

### 4. Déploiement

```bash
# Cloner le projet
git clone <repository-url>
cd crm

# Créer le fichier d'environnement
cp .env.example .env.prod

# Éditer les variables de production
nano .env.prod

# Construire et démarrer
docker-compose --env-file .env.prod up -d --build

# Initialiser la base de données
docker-compose exec app python init_data.py
```

## 🖥️ Déploiement Manuel sur Ubuntu

### 1. Préparation du Serveur

```bash
# Mise à jour du système
sudo apt update && sudo apt upgrade -y

# Installation des dépendances
sudo apt install -y python3 python3-pip python3-venv nginx postgresql postgresql-contrib supervisor git

# Création de l'utilisateur
sudo useradd --create-home --shell /bin/bash crm
sudo usermod -aG sudo crm
```

### 2. Configuration PostgreSQL

```bash
# Connexion à PostgreSQL
sudo -u postgres psql

-- Créer la base de données et l'utilisateur
CREATE DATABASE crm_db;
CREATE USER crm_user WITH PASSWORD 'your-secure-password';
GRANT ALL PRIVILEGES ON DATABASE crm_db TO crm_user;
\q
```

### 3. Installation de l'Application

```bash
# Changer vers l'utilisateur crm
sudo su - crm

# Cloner le projet
git clone <repository-url> /home/<USER>/crm
cd /home/<USER>/crm

# Créer l'environnement virtuel
python3 -m venv venv
source venv/bin/activate

# Installer les dépendances
pip install --upgrade pip
pip install -r requirements.txt
pip install gunicorn

# Configuration
cp .env.example .env
nano .env
```

### 4. Configuration .env de Production

```env
SECRET_KEY=your-very-secure-secret-key-here
DATABASE_URL=postgresql://crm_user:your-secure-password@localhost/crm_db
DEBUG=false
LOG_LEVEL=INFO

# SMTP Configuration
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_USE_TLS=true
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=CRM System

# Security
SESSION_TIMEOUT=3600
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900
```

### 5. Initialisation

```bash
# Initialiser la base de données
python init_data.py

# Tester l'application
gunicorn main:app --bind 0.0.0.0:8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker
```

### 6. Configuration Supervisor

```bash
sudo nano /etc/supervisor/conf.d/crm.conf
```

```ini
[program:crm]
command=/home/<USER>/crm/venv/bin/gunicorn main:app --bind 0.0.0.0:8000 --workers 4 --worker-class uvicorn.workers.UvicornWorker
directory=/home/<USER>/crm
user=crm
autostart=true
autorestart=true
redirect_stderr=true
stdout_logfile=/home/<USER>/crm/logs/gunicorn.log
environment=PATH="/home/<USER>/crm/venv/bin"
```

```bash
# Recharger et démarrer
sudo supervisorctl reread
sudo supervisorctl update
sudo supervisorctl start crm
```

### 7. Configuration Nginx

```bash
sudo nano /etc/nginx/sites-available/crm
```

```nginx
server {
    listen 80;
    server_name your-domain.com;

    client_max_body_size 50M;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    location /static/ {
        alias /home/<USER>/crm/static/;
        expires 30d;
        add_header Cache-Control "public, immutable";
    }
}
```

```bash
# Activer le site
sudo ln -s /etc/nginx/sites-available/crm /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 8. SSL avec Let's Encrypt

```bash
# Installer Certbot
sudo apt install certbot python3-certbot-nginx

# Obtenir le certificat
sudo certbot --nginx -d your-domain.com

# Test de renouvellement automatique
sudo certbot renew --dry-run
```

## ☁️ Déploiement Cloud

### AWS EC2

1. **Lancer une instance EC2** (t3.medium recommandé)
2. **Configurer le Security Group** :
   - Port 22 (SSH)
   - Port 80 (HTTP)
   - Port 443 (HTTPS)
3. **Suivre le guide de déploiement manuel**
4. **Configurer RDS** pour PostgreSQL (optionnel)

### Google Cloud Platform

```bash
# Utiliser Cloud Run
gcloud run deploy crm-system \
  --image gcr.io/PROJECT-ID/crm \
  --platform managed \
  --region europe-west1 \
  --allow-unauthenticated
```

### DigitalOcean

1. **Créer un Droplet** Ubuntu 20.04
2. **Suivre le guide de déploiement manuel**
3. **Utiliser Managed Database** pour PostgreSQL

## 🔧 Optimisations de Production

### 1. Performance

```bash
# Configuration Gunicorn optimisée
gunicorn main:app \
  --bind 0.0.0.0:8000 \
  --workers $((2 * $(nproc) + 1)) \
  --worker-class uvicorn.workers.UvicornWorker \
  --worker-connections 1000 \
  --max-requests 1000 \
  --max-requests-jitter 100 \
  --timeout 30 \
  --keep-alive 2
```

### 2. Monitoring

```bash
# Installation de monitoring
pip install prometheus-client
pip install sentry-sdk[fastapi]
```

### 3. Backup Automatique

```bash
# Script de backup
#!/bin/bash
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup base de données
pg_dump -h localhost -U crm_user crm_db > $BACKUP_DIR/db_$DATE.sql

# Backup fichiers
tar -czf $BACKUP_DIR/files_$DATE.tar.gz /home/<USER>/crm/static/uploads

# Nettoyer les anciens backups (garder 30 jours)
find $BACKUP_DIR -name "*.sql" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

## 🔒 Sécurité en Production

### 1. Firewall

```bash
# Configuration UFW
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 'Nginx Full'
sudo ufw enable
```

### 2. Fail2Ban

```bash
# Installation
sudo apt install fail2ban

# Configuration
sudo nano /etc/fail2ban/jail.local
```

```ini
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
```

### 3. Mise à jour automatique

```bash
# Configuration des mises à jour automatiques
sudo apt install unattended-upgrades
sudo dpkg-reconfigure -plow unattended-upgrades
```

## 📊 Monitoring et Logs

### 1. Logs centralisés

```bash
# Configuration rsyslog pour centraliser les logs
sudo nano /etc/rsyslog.d/50-crm.conf
```

### 2. Monitoring avec Prometheus

```python
# Ajouter dans main.py
from prometheus_client import Counter, Histogram, generate_latest

REQUEST_COUNT = Counter('requests_total', 'Total requests', ['method', 'endpoint'])
REQUEST_LATENCY = Histogram('request_duration_seconds', 'Request latency')
```

## 🚨 Dépannage

### Problèmes courants

1. **Erreur de connexion DB** : Vérifier les credentials et la connectivité
2. **Erreur 502** : Vérifier que Gunicorn fonctionne
3. **Erreur SSL** : Vérifier les certificats et la configuration Nginx
4. **Performance lente** : Vérifier les logs et optimiser les requêtes DB

### Commandes utiles

```bash
# Vérifier les logs
sudo tail -f /var/log/nginx/error.log
sudo supervisorctl tail -f crm

# Redémarrer les services
sudo supervisorctl restart crm
sudo systemctl restart nginx

# Vérifier l'état
sudo supervisorctl status
sudo systemctl status nginx
```

---

**🎯 Votre CRM est maintenant déployé en production de manière sécurisée et optimisée !**

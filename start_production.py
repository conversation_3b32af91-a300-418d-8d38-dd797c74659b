#!/usr/bin/env python3
"""
BINANCE CRM - Démarrage Production
Script optimisé pour le déploiement sur Railway/Render
"""

import os
import sys
import threading
import time
from http.server import HTTPServer, SimpleHTTPRequestHandler
import subprocess

# Configuration pour la production
PORT = int(os.environ.get('PORT', 8080))
HOST = '0.0.0.0'

def start_database_server():
    """Démarrer le serveur de base de données"""
    print("🗄️ Démarrage du serveur de base de données...")
    try:
        # Import et démarrage du serveur de base de données
        from database_server import start_database_server
        start_database_server(8001)
    except Exception as e:
        print(f"❌ Erreur serveur base de données: {e}")

def start_email_server():
    """Démarrer le serveur email"""
    print("📧 Démarrage du serveur email...")
    try:
        subprocess.Popen([sys.executable, "email_server.py"], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
    except Exception as e:
        print(f"❌ Erreur serveur email: {e}")

def start_pdf_server():
    """Démarrer le serveur PDF"""
    print("📄 Démarrage du serveur PDF...")
    try:
        subprocess.Popen([sys.executable, "pdf_server.py"], 
                        stdout=subprocess.DEVNULL, 
                        stderr=subprocess.DEVNULL)
    except Exception as e:
        print(f"❌ Erreur serveur PDF: {e}")

class ProductionHTTPRequestHandler(SimpleHTTPRequestHandler):
    """Gestionnaire HTTP optimisé pour la production"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=".", **kwargs)
    
    def end_headers(self):
        # Ajouter des headers de sécurité
        self.send_header('X-Content-Type-Options', 'nosniff')
        self.send_header('X-Frame-Options', 'DENY')
        self.send_header('X-XSS-Protection', '1; mode=block')
        super().end_headers()
    
    def do_GET(self):
        # Rediriger la racine vers index.html
        if self.path == '/':
            self.path = '/index.html'
        return super().do_GET()
    
    def log_message(self, format, *args):
        # Réduire les logs en production
        if not os.environ.get('DEBUG'):
            return

def check_dependencies():
    """Vérifier les dépendances critiques"""
    try:
        import sqlite3
        import json
        import threading
        print("✅ Dépendances critiques vérifiées")
        return True
    except ImportError as e:
        print(f"❌ Dépendance manquante: {e}")
        return False

def initialize_database():
    """Initialiser la base de données si nécessaire"""
    if not os.path.exists('binance_crm.db'):
        print("🔧 Initialisation de la base de données...")
        try:
            from database_server import DatabaseManager
            db_manager = DatabaseManager()
            db_manager.init_database()
            print("✅ Base de données initialisée")
        except Exception as e:
            print(f"❌ Erreur initialisation DB: {e}")

def main():
    """Fonction principale pour la production"""
    print("🚀 DÉMARRAGE BINANCE CRM - MODE PRODUCTION")
    print("="*50)
    
    # Vérifications préliminaires
    if not check_dependencies():
        sys.exit(1)
    
    # Initialiser la base de données
    initialize_database()
    
    # Démarrer les serveurs backend en arrière-plan
    print("🔧 Démarrage des services backend...")
    
    # Serveur de base de données (thread principal)
    db_thread = threading.Thread(target=start_database_server, daemon=True)
    db_thread.start()
    
    # Attendre que le serveur DB soit prêt
    time.sleep(2)
    
    # Serveurs email et PDF (processus séparés)
    start_email_server()
    start_pdf_server()
    
    # Serveur HTTP principal
    print(f"🌐 Démarrage du serveur HTTP sur {HOST}:{PORT}")
    
    try:
        httpd = HTTPServer((HOST, PORT), ProductionHTTPRequestHandler)
        print(f"✅ BINANCE CRM démarré avec succès!")
        print(f"🔗 Accessible sur: http://{HOST}:{PORT}")
        print("📊 Endpoints API:")
        print(f"   - Base de données: http://{HOST}:8001/api/")
        print(f"   - Email: http://{HOST}:8002/api/")
        print(f"   - PDF: http://{HOST}:8003/api/")
        
        # Démarrer le serveur
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur...")
        httpd.shutdown()
    except Exception as e:
        print(f"❌ Erreur serveur HTTP: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()

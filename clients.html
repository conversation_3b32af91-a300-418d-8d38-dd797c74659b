<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Clients - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
        }
        
        .avatar-circle {
            width: 35px; 
            height: 35px; 
            border-radius: 50%;
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            display: flex; 
            align-items: center; 
            justify-content: center;
            color: #000; 
            font-weight: bold; 
            font-size: 12px;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(241, 194, 50, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">Dashboard</a>
                <a class="nav-link active" href="clients.html">Clients</a>
                <a class="nav-link" href="vendeurs.html">Vendeurs</a>
                <a class="nav-link" href="emails.html">Emails</a>
                <a class="nav-link" href="#" onclick="logout()">Déconnexion</a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="bi bi-people"></i> Gestion des Clients
                </h1>
                <p class="text-muted">127 clients • Gestion complète CRUD</p>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="importClients()">
                    <i class="bi bi-upload"></i> Import CSV
                </button>
                <button class="btn btn-info" onclick="exportClients()">
                    <i class="bi bi-download"></i> Export CSV
                </button>
                <button class="btn btn-outline-info" onclick="downloadCSVTemplate()" title="Télécharger le template CSV avec exemples">
                    <i class="bi bi-file-earmark-arrow-down"></i> Template
                </button>
                <button class="btn btn-outline-warning" onclick="validateCSVBeforeImport()" title="Valider le fichier CSV avant import">
                    <i class="bi bi-check-circle"></i> Valider
                </button>
                <button class="btn btn-outline-secondary" onclick="showImportHistory()" title="Voir l'historique des imports">
                    <i class="bi bi-clock-history"></i> Historique
                </button>
                <button class="btn btn-warning" onclick="showImportModal()">
                    <i class="bi bi-upload"></i> Import CSV
                </button>
                <button class="btn btn-primary" onclick="addClient()">
                    <i class="bi bi-person-plus"></i> Nouveau Client
                </button>
            </div>
        </div>
        
        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" class="form-control" placeholder="Rechercher..." id="searchInput" onkeyup="filterClients()">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter" onchange="filterClients()">
                            <option value="">Tous les statuts</option>
                            <option value="nouveau">Nouveau</option>
                            <option value="en cours">En cours</option>
                            <option value="magnifique">Magnifique</option>
                            <option value="NRP">NRP</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="vendeurFilter" onchange="filterClients()">
                            <option value="">Tous les vendeurs</option>
                            <option value="marie.martin">Marie Martin</option>
                            <option value="pierre.durand">Pierre Durand</option>
                            <option value="sophie.bernard">Sophie Bernard</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Liste des clients -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Liste des Clients</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="clientsTable">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Ville</th>
                                <th>Âge</th>
                                <th>Vendeur</th>
                                <th>Statut</th>
                                <th>Créé le</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="clientsTableBody">
                            <!-- Données générées par JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-check-circle"></i> Fonctionnalités Clients Disponibles :</h6>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Création</strong><br>
                    <small>Formulaire complet fonctionnel</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Lecture</strong><br>
                    <small>Liste avec filtres et recherche</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Modification</strong><br>
                    <small>Édition en ligne</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Suppression</strong><br>
                    <small>Suppression sécurisée</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Nouveau Client -->
    <div class="modal fade" id="clientModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus"></i> Nouveau Client
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="clientForm">
                        <!-- Informations personnelles -->
                        <h6 class="text-muted mb-3"><i class="bi bi-person"></i> Informations personnelles</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prénom</label>
                                    <input type="text" class="form-control" id="prenom" placeholder="Jean">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom</label>
                                    <input type="text" class="form-control" id="nom" placeholder="DUPONT">
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email</label>
                                    <input type="email" class="form-control" id="email" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="telephone" placeholder="0123456789">
                                    <div class="form-text">Format français : 10 chiffres commençant par 0</div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Date de naissance</label>
                                    <input type="date" class="form-control" id="naissance">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Statut</label>
                                    <select class="form-select" id="statut">
                                        <option value="prospect">Prospect</option>
                                        <option value="client">Client</option>
                                        <option value="actif">Actif</option>
                                        <option value="inactif">Inactif</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Adresse -->
                        <h6 class="text-muted mb-3 mt-4"><i class="bi bi-geo-alt"></i> Adresse</h6>
                        <div class="mb-3">
                            <label class="form-label">Adresse</label>
                            <input type="text" class="form-control" id="adresse" placeholder="5 rue de la Paix">
                        </div>

                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">Code postal</label>
                                    <input type="text" class="form-control" id="code_postal" placeholder="75001" maxlength="5">
                                    <div class="form-text">5 chiffres</div>
                                </div>
                            </div>
                            <div class="col-md-8">
                                <div class="mb-3">
                                    <label class="form-label">Ville</label>
                                    <input type="text" class="form-control" id="ville" placeholder="PARIS">
                                </div>
                            </div>
                        </div>

                        <!-- Gestion commerciale -->
                        <h6 class="text-muted mb-3 mt-4"><i class="bi bi-briefcase"></i> Gestion commerciale</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Vendeur assigné</label>
                                    <select class="form-select" id="vendeur">
                                        <option value="">Non attribué</option>
                                        <option value="1">Marie Martin</option>
                                        <option value="2">Pierre Durand</option>
                                        <option value="3">Sophie Bernard</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Date de création</label>
                                    <input type="text" class="form-control" id="dateCreation" readonly>
                                    <div class="form-text">Générée automatiquement</div>
                                </div>
                            </div>
                        </div>

                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" rows="3" placeholder="Notes internes sur le client..."></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveClient()">
                        <i class="bi bi-check"></i> Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Import CSV -->
    <div class="modal fade" id="importModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-upload"></i> Import CSV - Clients
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <!-- Étapes d'import -->
                    <div class="row mb-4">
                        <div class="col-12">
                            <div class="progress" style="height: 8px;">
                                <div class="progress-bar bg-warning" id="importProgress" role="progressbar" style="width: 20%"></div>
                            </div>
                            <div class="d-flex justify-content-between mt-2">
                                <small class="text-muted">Étape <span id="currentStep">1</span> sur 5</small>
                                <small class="text-muted" id="stepDescription">Sélection du fichier</small>
                            </div>
                        </div>
                    </div>

                    <!-- Étape 1: Sélection du fichier -->
                    <div id="step1" class="import-step">
                        <div class="row">
                            <div class="col-md-8">
                                <h6><i class="bi bi-file-earmark-arrow-up"></i> Sélectionnez votre fichier CSV</h6>
                                <div class="border border-dashed border-warning rounded p-4 text-center" id="dropZone" style="min-height: 200px; cursor: pointer;">
                                    <i class="bi bi-cloud-upload" style="font-size: 3rem; color: #f1c232;"></i>
                                    <h5 class="mt-3">Glissez-déposez votre fichier CSV ici</h5>
                                    <p class="text-muted">ou cliquez pour sélectionner un fichier</p>
                                    <input type="file" id="csvFile" accept=".csv" style="display: none;">
                                    <div class="mt-3">
                                        <span class="badge bg-info">Format: CSV</span>
                                        <span class="badge bg-info">Séparateur: ;</span>
                                        <span class="badge bg-info">Encodage: UTF-8</span>
                                        <span class="badge bg-info">Max: 10MB</span>
                                    </div>
                                </div>
                                <div id="fileInfo" class="mt-3" style="display: none;">
                                    <div class="alert alert-success">
                                        <i class="bi bi-check-circle"></i>
                                        <strong>Fichier sélectionné:</strong> <span id="fileName"></span>
                                        <br><small>Taille: <span id="fileSize"></span> | Lignes: <span id="fileRows"></span></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="bi bi-info-circle"></i> Format requis</h6>
                                <div class="card">
                                    <div class="card-body">
                                        <h6 class="card-title">Colonnes supportées:</h6>
                                        <ul class="list-unstyled small">
                                            <li><code>nom</code> - Nom de famille</li>
                                            <li><code>prenom</code> - Prénom</li>
                                            <li><code>email</code> - Email</li>
                                            <li><code>telephone</code> - Téléphone</li>
                                            <li><code>naissance</code> - Date naissance</li>
                                            <li><code>adresse</code> - Adresse</li>
                                            <li><code>code_postal</code> - Code postal</li>
                                            <li><code>ville</code> - Ville</li>
                                            <li><code>vendeur</code> - Vendeur assigné</li>
                                            <li><code>statut</code> - Statut client</li>
                                        </ul>
                                        <button class="btn btn-outline-primary btn-sm" onclick="downloadTemplate()">
                                            <i class="bi bi-download"></i> Télécharger template
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Étape 2: Mapping des colonnes -->
                    <div id="step2" class="import-step" style="display: none;">
                        <h6><i class="bi bi-arrow-left-right"></i> Correspondance des colonnes</h6>
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Colonnes détectées dans le fichier:</h6>
                                <div id="detectedColumns" class="list-group"></div>
                            </div>
                            <div class="col-md-6">
                                <h6>Correspondance avec les champs:</h6>
                                <div id="columnMapping"></div>
                            </div>
                        </div>
                    </div>

                    <!-- Étape 3: Prévisualisation et validation -->
                    <div id="step3" class="import-step" style="display: none;">
                        <h6><i class="bi bi-eye"></i> Prévisualisation des données</h6>
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-primary" id="totalRowsPreview">0</h4>
                                        <small>Lignes totales</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-success" id="validRowsPreview">0</h4>
                                        <small>Lignes valides</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h4 class="text-danger" id="errorRowsPreview">0</h4>
                                        <small>Lignes avec erreurs</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr id="previewTableHeader"></tr>
                                </thead>
                                <tbody id="previewTableBody"></tbody>
                            </table>
                        </div>

                        <div id="validationErrors" class="mt-3"></div>

                        <div class="form-check mt-3">
                            <input class="form-check-input" type="checkbox" id="skipErrors" checked>
                            <label class="form-check-label" for="skipErrors">
                                Ignorer les lignes avec erreurs et continuer l'import
                            </label>
                        </div>
                    </div>

                    <!-- Étape 4: Import en cours -->
                    <div id="step4" class="import-step" style="display: none;">
                        <div class="text-center">
                            <div class="spinner-border text-warning" role="status" style="width: 3rem; height: 3rem;">
                                <span class="visually-hidden">Import en cours...</span>
                            </div>
                            <h5 class="mt-3">Import en cours...</h5>
                            <p class="text-muted">Veuillez patienter pendant l'import des données.</p>
                            <div class="progress mt-3">
                                <div class="progress-bar progress-bar-striped progress-bar-animated bg-warning" id="importProgressBar" role="progressbar" style="width: 0%"></div>
                            </div>
                            <small class="text-muted" id="importStatus">Préparation...</small>
                        </div>
                    </div>

                    <!-- Étape 5: Résultats -->
                    <div id="step5" class="import-step" style="display: none;">
                        <div class="text-center mb-4">
                            <i class="bi bi-check-circle-fill text-success" style="font-size: 4rem;"></i>
                            <h4 class="mt-3">Import terminé !</h4>
                        </div>

                        <div class="row">
                            <div class="col-md-3">
                                <div class="card text-center border-primary">
                                    <div class="card-body">
                                        <h4 class="text-primary" id="finalTotalRows">0</h4>
                                        <small>Lignes traitées</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-success">
                                    <div class="card-body">
                                        <h4 class="text-success" id="finalSuccessRows">0</h4>
                                        <small>Clients créés</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-warning">
                                    <div class="card-body">
                                        <h4 class="text-warning" id="finalSkippedRows">0</h4>
                                        <small>Lignes ignorées</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="card text-center border-danger">
                                    <div class="card-body">
                                        <h4 class="text-danger" id="finalErrorRows">0</h4>
                                        <small>Erreurs</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div id="importSummary" class="mt-4"></div>

                        <div class="text-center mt-4">
                            <button class="btn btn-outline-secondary" onclick="showImportHistory()">
                                <i class="bi bi-clock-history"></i> Voir l'historique
                            </button>
                            <button class="btn btn-outline-warning" id="undoImportBtn" style="display: none;">
                                <i class="bi bi-arrow-counterclockwise"></i> Annuler cet import
                            </button>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-outline-secondary" id="prevStepBtn" onclick="previousStep()" style="display: none;">
                        <i class="bi bi-arrow-left"></i> Précédent
                    </button>
                    <button type="button" class="btn btn-warning" id="nextStepBtn" onclick="nextStep()" disabled>
                        Suivant <i class="bi bi-arrow-right"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Validation CSV -->
    <div class="modal fade" id="validationModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-check-circle"></i> Validation CSV
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div id="validationResults"></div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-success" id="proceedImportBtn" onclick="proceedWithImport()" style="display: none;">
                        <i class="bi bi-upload"></i> Procéder à l'import
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Historique Import -->
    <div class="modal fade" id="historyModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-clock-history"></i> Historique des Imports
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Date</th>
                                    <th>Fichier</th>
                                    <th>Utilisateur</th>
                                    <th>Total</th>
                                    <th>Réussis</th>
                                    <th>Échecs</th>
                                    <th>Statut</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody id="historyTableBody">
                                <!-- Contenu généré dynamiquement -->
                            </tbody>
                        </table>
                    </div>
                    <div id="historyEmpty" class="text-center py-4" style="display: none;">
                        <i class="bi bi-inbox display-4 text-muted"></i>
                        <p class="text-muted mt-2">Aucun import trouvé</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                    <button type="button" class="btn btn-outline-primary" onclick="refreshImportHistory()">
                        <i class="bi bi-arrow-clockwise"></i> Actualiser
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Container pour les notifications -->
    <div id="notificationContainer" class="position-fixed top-0 end-0 p-3" style="z-index: 1055;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/PapaParse/5.4.1/papaparse.min.js"></script>
    <script>
        // Données clients simulées avec format CSV complet
        let clients = [
            {
                id: 1,
                prenom: 'Jean',
                nom: 'DUPONT',
                email: '<EMAIL>',
                telephone: '0123456789',
                naissance: '1985-12-15',
                adresse: '5 rue de la Paix',
                code_postal: '75001',
                ville: 'PARIS',
                vendeur: 'Marie Martin',
                statut: 'client',
                dateCreation: '2024-01-15',
                notes: 'Client premium intéressé par les cryptomonnaies'
            },
            {
                id: 2,
                prenom: 'Marie',
                nom: 'MARTIN',
                email: '<EMAIL>',
                telephone: '0987654321',
                naissance: '1990-03-22',
                adresse: '10 avenue des Champs',
                code_postal: '75008',
                ville: 'PARIS',
                vendeur: 'Pierre Durand',
                statut: 'prospect',
                dateCreation: '2024-01-14',
                notes: 'Première prise de contact, très intéressée'
            },
            {
                id: 3,
                prenom: 'Pierre',
                nom: 'BERNARD',
                email: '<EMAIL>',
                telephone: '0156789012',
                naissance: '1978-07-08',
                adresse: '25 boulevard Saint-Germain',
                code_postal: '75006',
                ville: 'PARIS',
                vendeur: 'Sophie Bernard',
                statut: 'actif',
                dateCreation: '2024-01-13',
                notes: 'Investisseur expérimenté, portefeuille diversifié'
            },
            {
                id: 4,
                prenom: 'Sophie',
                nom: 'DURAND',
                email: '<EMAIL>',
                telephone: '0234567890',
                naissance: '1992-11-30',
                adresse: '8 rue du Commerce',
                code_postal: '69001',
                ville: 'LYON',
                vendeur: 'Marie Martin',
                statut: 'prospect',
                dateCreation: '2024-01-12',
                notes: 'Rendez-vous prévu la semaine prochaine'
            },
            {
                id: 5,
                prenom: 'Luc',
                nom: 'MOREAU',
                email: '<EMAIL>',
                telephone: '0345678901',
                naissance: '1988-04-17',
                adresse: '15 place Bellecour',
                code_postal: '69002',
                ville: 'LYON',
                vendeur: 'Pierre Durand',
                statut: 'inactif',
                dateCreation: '2024-01-11',
                notes: 'Ancien client, à relancer'
            },
            {
                id: 6,
                prenom: 'KYLLIAN',
                nom: 'PORTE',
                email: '<EMAIL>',
                telephone: '0749530760',
                naissance: '2008-02-07',
                adresse: '12 PLACE DE L YSER',
                code_postal: '62670',
                ville: 'MAZINGARBE',
                vendeur: 'Marie Martin',
                statut: 'prospect',
                dateCreation: '2024-01-10',
                notes: 'Jeune investisseur, formation nécessaire'
            }
        ];
        
        let filteredClients = [...clients];
        
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        function renderClientsTable() {
            const tbody = document.getElementById('clientsTableBody');
            tbody.innerHTML = '';

            filteredClients.forEach(client => {
                const statusColors = {
                    'prospect': 'warning',
                    'client': 'success',
                    'actif': 'primary',
                    'inactif': 'secondary'
                };

                // Calculer l'âge si date de naissance disponible
                const age = client.naissance ? calculateAge(client.naissance) : '';

                // Initiales pour l'avatar
                const initials = (client.prenom && client.nom) ?
                    `${client.prenom[0]}${client.nom[0]}` :
                    (client.prenom ? client.prenom[0] : (client.nom ? client.nom[0] : '?'));

                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2">
                                    ${initials}
                                </div>
                                <div>
                                    <strong>${(client.prenom || '') + ' ' + (client.nom || '')}</strong><br>
                                    <small class="text-muted">ID: ${client.id}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            ${client.email ? `<a href="mailto:${client.email}" class="text-decoration-none">${client.email}</a>` : '<span class="text-muted">Non renseigné</span>'}
                        </td>
                        <td>
                            ${client.telephone ? `<a href="tel:${client.telephone}" class="text-decoration-none">${client.telephone}</a>` : '<span class="text-muted">-</span>'}
                        </td>
                        <td>
                            ${client.ville ? `<i class="bi bi-geo-alt"></i> ${client.ville}` : '<span class="text-muted">-</span>'}
                            ${client.code_postal ? `<br><small class="text-muted">${client.code_postal}</small>` : ''}
                        </td>
                        <td>
                            ${age ? `<span class="badge bg-light text-dark">${age}</span>` : '<span class="text-muted">-</span>'}
                        </td>
                        <td>
                            ${client.vendeur ? `<span class="badge bg-info">${client.vendeur}</span>` : '<span class="badge bg-secondary">Non attribué</span>'}
                        </td>
                        <td>
                            <span class="badge bg-${statusColors[client.statut] || 'secondary'}">${client.statut || 'prospect'}</span>
                        </td>
                        <td>
                            <small>${client.dateCreation ? new Date(client.dateCreation).toLocaleDateString('fr-FR') : 'N/A'}</small>
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editClient(${client.id})" title="Modifier">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-info" onclick="emailClient(${client.id})" title="Email" ${!client.email ? 'disabled' : ''}>
                                    <i class="bi bi-envelope"></i>
                                </button>
                                <button class="btn btn-outline-success" onclick="viewClient(${client.id})" title="Voir détails">
                                    <i class="bi bi-eye"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteClient(${client.id})" title="Supprimer">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
        
        function filterClients() {
            const search = document.getElementById('searchInput').value.toLowerCase();
            const status = document.getElementById('statusFilter').value;
            const vendeur = document.getElementById('vendeurFilter').value;
            
            filteredClients = clients.filter(client => {
                const matchSearch = !search || 
                    client.prenom.toLowerCase().includes(search) ||
                    client.nom.toLowerCase().includes(search) ||
                    client.email.toLowerCase().includes(search);
                
                const matchStatus = !status || client.indicateur === status;
                const matchVendeur = !vendeur || client.vendeur === vendeur;
                
                return matchSearch && matchStatus && matchVendeur;
            });
            
            renderClientsTable();
        }
        
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('vendeurFilter').value = '';
            filteredClients = [...clients];
            renderClientsTable();
        }
        
        function addClient() {
            document.getElementById('clientForm').reset();
            // Réinitialiser le modal pour l'ajout
            document.querySelector('#clientModal .modal-title').innerHTML = '<i class="bi bi-person-plus"></i> Nouveau Client';
            document.querySelector('#clientModal .btn-primary').innerHTML = '<i class="bi bi-check"></i> Enregistrer';
            document.querySelector('#clientModal .btn-primary').setAttribute('onclick', 'saveClient()');
            new bootstrap.Modal(document.getElementById('clientModal')).show();
        }
        
        function saveClient() {
            // Récupérer tous les champs du formulaire
            const clientData = {
                first_name: document.getElementById('prenom').value.trim(),
                last_name: document.getElementById('nom').value.trim(),
                email: document.getElementById('email').value.trim(),
                phone: document.getElementById('telephone').value.trim(),
                birth_date: document.getElementById('naissance').value,
                address: document.getElementById('adresse').value.trim(),
                postal_code: document.getElementById('code_postal').value.trim(),
                city: document.getElementById('ville').value.trim(),
                status: document.getElementById('statut').value,
                assigned_to: document.getElementById('vendeur').value || null,
                notes: document.getElementById('notes').value.trim()
            };

            // Validation basique
            if (!clientData.first_name && !clientData.last_name && !clientData.email) {
                showNotification('Veuillez remplir au moins le nom, prénom ou email', 'error');
                return;
            }

            // Validation email si présent
            if (clientData.email && !isValidEmail(clientData.email)) {
                showNotification('Format d\'email invalide', 'error');
                return;
            }

            // Validation téléphone si présent
            if (clientData.phone && !isValidPhone(clientData.phone)) {
                showNotification('Format de téléphone invalide (10 chiffres commençant par 0)', 'error');
                return;
            }

            // Validation code postal si présent
            if (clientData.postal_code && !isValidPostalCode(clientData.postal_code)) {
                showNotification('Code postal invalide (5 chiffres)', 'error');
                return;
            }

            // Créer le client pour l'affichage local
            const newClient = {
                id: clients.length + 1,
                prenom: clientData.first_name,
                nom: clientData.last_name,
                email: clientData.email,
                telephone: clientData.phone,
                naissance: clientData.birth_date,
                adresse: clientData.address,
                code_postal: clientData.postal_code,
                ville: clientData.city,
                statut: clientData.status,
                vendeur: getVendeurName(clientData.assigned_to),
                notes: clientData.notes,
                dateCreation: new Date().toISOString().split('T')[0]
            };

            // Ajouter à la liste locale
            clients.push(newClient);
            filteredClients = [...clients];
            renderClientsTable();

            // Sauvegarder dans localStorage pour persistance
            localStorage.setItem('binance_crm_clients', JSON.stringify(clients));

            bootstrap.Modal.getInstance(document.getElementById('clientModal')).hide();
            showNotification('Client ajouté avec succès !', 'success');

            // TODO: Envoyer au serveur via API
            // sendClientToServer(clientData);
        }

        // Fonctions de validation
        function isValidEmail(email) {
            const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
            return emailRegex.test(email);
        }

        function isValidPhone(phone) {
            const cleanPhone = phone.replace(/[^\d]/g, '');
            return /^0\d{9}$/.test(cleanPhone);
        }

        function isValidPostalCode(postalCode) {
            return /^\d{5}$/.test(postalCode);
        }

        function getVendeurName(vendeurId) {
            const vendeurs = {
                '1': 'Marie Martin',
                '2': 'Pierre Durand',
                '3': 'Sophie Bernard'
            };
            return vendeurs[vendeurId] || '';
        }

        function calculateAge(birthDate) {
            if (!birthDate) return '';
            const today = new Date();
            const birth = new Date(birthDate);
            let age = today.getFullYear() - birth.getFullYear();
            const monthDiff = today.getMonth() - birth.getMonth();
            if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
                age--;
            }
            return age + ' ans';
        }

        function updateClient(id) {
            // Récupérer tous les champs du formulaire
            const clientData = {
                first_name: document.getElementById('prenom').value.trim(),
                last_name: document.getElementById('nom').value.trim(),
                email: document.getElementById('email').value.trim(),
                phone: document.getElementById('telephone').value.trim(),
                birth_date: document.getElementById('naissance').value,
                address: document.getElementById('adresse').value.trim(),
                postal_code: document.getElementById('code_postal').value.trim(),
                city: document.getElementById('ville').value.trim(),
                status: document.getElementById('statut').value,
                assigned_to: document.getElementById('vendeur').value || null,
                notes: document.getElementById('notes').value.trim()
            };

            // Validation basique
            if (!clientData.first_name && !clientData.last_name && !clientData.email) {
                showNotification('Veuillez remplir au moins le nom, prénom ou email', 'error');
                return;
            }

            // Validation email si présent
            if (clientData.email && !isValidEmail(clientData.email)) {
                showNotification('Format d\'email invalide', 'error');
                return;
            }

            // Validation téléphone si présent
            if (clientData.phone && !isValidPhone(clientData.phone)) {
                showNotification('Format de téléphone invalide (10 chiffres commençant par 0)', 'error');
                return;
            }

            // Validation code postal si présent
            if (clientData.postal_code && !isValidPostalCode(clientData.postal_code)) {
                showNotification('Code postal invalide (5 chiffres)', 'error');
                return;
            }

            const clientIndex = clients.findIndex(c => c.id === id);
            if (clientIndex !== -1) {
                clients[clientIndex] = {
                    ...clients[clientIndex],
                    prenom: clientData.first_name,
                    nom: clientData.last_name,
                    email: clientData.email,
                    telephone: clientData.phone,
                    naissance: clientData.birth_date,
                    adresse: clientData.address,
                    code_postal: clientData.postal_code,
                    ville: clientData.city,
                    statut: clientData.status,
                    vendeur: getVendeurName(clientData.assigned_to),
                    notes: clientData.notes,
                    dateModification: new Date().toISOString().split('T')[0]
                };

                filteredClients = [...clients];
                renderClientsTable();

                // Sauvegarder dans localStorage
                localStorage.setItem('binance_crm_clients', JSON.stringify(clients));

                bootstrap.Modal.getInstance(document.getElementById('clientModal')).hide();
                showNotification('Client modifié avec succès !', 'success');
            }
        }
        
        function editClient(id) {
            const client = clients.find(c => c.id === id);
            if (client) {
                // Remplir le formulaire avec les données existantes
                document.getElementById('prenom').value = client.prenom || '';
                document.getElementById('nom').value = client.nom || '';
                document.getElementById('email').value = client.email || '';
                document.getElementById('telephone').value = client.telephone || '';
                document.getElementById('naissance').value = client.naissance || '';
                document.getElementById('adresse').value = client.adresse || '';
                document.getElementById('code_postal').value = client.code_postal || '';
                document.getElementById('ville').value = client.ville || '';
                document.getElementById('statut').value = client.statut || 'prospect';
                document.getElementById('notes').value = client.notes || '';

                // Remplir le vendeur (convertir nom vers ID)
                const vendeurId = getVendeurId(client.vendeur);
                document.getElementById('vendeur').value = vendeurId || '';

                // Afficher la date de création (lecture seule)
                document.getElementById('dateCreation').value = client.dateCreation || '';

                // Changer le titre et le bouton
                document.querySelector('#clientModal .modal-title').innerHTML = '<i class="bi bi-pencil"></i> Modifier Client';
                document.querySelector('#clientModal .btn-primary').innerHTML = '<i class="bi bi-check"></i> Mettre à jour';
                document.querySelector('#clientModal .btn-primary').setAttribute('onclick', `updateClient(${id})`);

                // Ouvrir le modal
                new bootstrap.Modal(document.getElementById('clientModal')).show();
            }
        }

        function getVendeurId(vendeurName) {
            const vendeurs = {
                'Marie Martin': '1',
                'Pierre Durand': '2',
                'Sophie Bernard': '3'
            };
            return vendeurs[vendeurName] || '';
        }

        function viewClient(id) {
            const client = clients.find(c => c.id === id);
            if (client) {
                const age = client.naissance ? calculateAge(client.naissance) : 'Non renseigné';
                const adresseComplete = [client.adresse, client.code_postal, client.ville].filter(Boolean).join(', ') || 'Non renseignée';

                const detailsHtml = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6><i class="bi bi-person"></i> Informations personnelles</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Nom complet :</strong></td><td>${(client.prenom || '') + ' ' + (client.nom || '')}</td></tr>
                                <tr><td><strong>Email :</strong></td><td>${client.email || 'Non renseigné'}</td></tr>
                                <tr><td><strong>Téléphone :</strong></td><td>${client.telephone || 'Non renseigné'}</td></tr>
                                <tr><td><strong>Date de naissance :</strong></td><td>${client.naissance || 'Non renseignée'}</td></tr>
                                <tr><td><strong>Âge :</strong></td><td>${age}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-geo-alt"></i> Adresse</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Adresse :</strong></td><td>${client.adresse || 'Non renseignée'}</td></tr>
                                <tr><td><strong>Code postal :</strong></td><td>${client.code_postal || 'Non renseigné'}</td></tr>
                                <tr><td><strong>Ville :</strong></td><td>${client.ville || 'Non renseignée'}</td></tr>
                                <tr><td><strong>Adresse complète :</strong></td><td>${adresseComplete}</td></tr>
                            </table>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6><i class="bi bi-briefcase"></i> Informations commerciales</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Statut :</strong></td><td><span class="badge bg-primary">${client.statut || 'prospect'}</span></td></tr>
                                <tr><td><strong>Vendeur assigné :</strong></td><td>${client.vendeur || 'Non attribué'}</td></tr>
                                <tr><td><strong>Date de création :</strong></td><td>${client.dateCreation || 'Non renseignée'}</td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6><i class="bi bi-chat-text"></i> Notes</h6>
                            <div class="border rounded p-2" style="min-height: 100px; background: #f8f9fa;">
                                ${client.notes || '<em class="text-muted">Aucune note</em>'}
                            </div>
                        </div>
                    </div>
                `;

                showModal('Détails du Client', detailsHtml);
            }
        }

        function showModal(title, content) {
            // Créer un modal temporaire pour afficher les détails
            const modalHtml = `
                <div class="modal fade" id="detailsModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Supprimer le modal existant s'il y en a un
            const existingModal = document.getElementById('detailsModal');
            if (existingModal) {
                existingModal.remove();
            }

            // Ajouter le nouveau modal
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // Afficher le modal
            new bootstrap.Modal(document.getElementById('detailsModal')).show();

            // Supprimer le modal après fermeture
            document.getElementById('detailsModal').addEventListener('hidden.bs.modal', function() {
                this.remove();
            });
        }
        
        function emailClient(id) {
            const client = clients.find(c => c.id === id);
            if (client) {
                // Ouvrir une nouvelle fenêtre avec le système d'email pré-rempli
                const emailUrl = `emails.html?client_id=${id}&email=${encodeURIComponent(client.email)}&nom=${encodeURIComponent(client.prenom + ' ' + client.nom)}`;
                window.open(emailUrl, '_blank');
                showNotification(`Ouverture du système d'email pour ${client.prenom} ${client.nom}`, 'info');
            }
        }
        
        function deleteClient(id) {
            const client = clients.find(c => c.id === id);
            if (client && confirm(`Êtes-vous sûr de vouloir supprimer ${client.prenom} ${client.nom} ?\n\nCette action est irréversible.`)) {
                clients = clients.filter(c => c.id !== id);
                filteredClients = [...clients];
                renderClientsTable();

                // Sauvegarder dans localStorage
                localStorage.setItem('binance_crm_clients', JSON.stringify(clients));

                showNotification(`Client ${client.prenom} ${client.nom} supprimé avec succès`, 'success');
            }
        }
        
        function importClients() {
            // Créer un input file invisible
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = '.csv';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        try {
                            const csv = e.target.result;
                            const lines = csv.split('\n');
                            let imported = 0;

                            // Ignorer la première ligne (headers)
                            for (let i = 1; i < lines.length; i++) {
                                const line = lines[i].trim();
                                if (line) {
                                    const [prenom, nom, email, telephone, vendeur, indicateur] = line.split(',');
                                    if (prenom && nom && email) {
                                        const newClient = {
                                            id: clients.length + imported + 1,
                                            prenom: prenom.trim(),
                                            nom: nom.trim(),
                                            email: email.trim(),
                                            telephone: telephone ? telephone.trim() : '',
                                            vendeur: vendeur ? vendeur.trim() : '',
                                            indicateur: indicateur ? indicateur.trim() : 'nouveau',
                                            activite: new Date().toISOString().split('T')[0]
                                        };
                                        clients.push(newClient);
                                        imported++;
                                    }
                                }
                            }

                            if (imported > 0) {
                                filteredClients = [...clients];
                                renderClientsTable();
                                localStorage.setItem('binance_crm_clients', JSON.stringify(clients));
                                showNotification(`${imported} clients importés avec succès !`, 'success');
                            } else {
                                showNotification('Aucun client valide trouvé dans le fichier', 'error');
                            }
                        } catch (error) {
                            showNotification('Erreur lors de l\'import du fichier CSV', 'error');
                        }
                    };
                    reader.readAsText(file);
                }
            };
            input.click();
        }

        function exportClients() {
            if (clients.length === 0) {
                showNotification('Aucun client à exporter', 'error');
                return;
            }

            // Créer le contenu CSV avec le format d'import (délimiteur point-virgule)
            let csv = 'nom;prenom;email;telephone;naissance;adresse;code_postal;ville;vendeur;statut\n';
            clients.forEach(client => {
                // Nettoyer les données pour éviter les problèmes CSV
                const cleanData = {
                    nom: (client.nom || '').replace(/"/g, '""'),
                    prenom: (client.prenom || '').replace(/"/g, '""'),
                    email: (client.email || '').replace(/"/g, '""'),
                    telephone: (client.telephone || '').replace(/"/g, '""'),
                    naissance: (client.naissance || '').replace(/"/g, '""'),
                    adresse: (client.adresse || '').replace(/"/g, '""'),
                    code_postal: (client.code_postal || '').replace(/"/g, '""'),
                    ville: (client.ville || '').replace(/"/g, '""'),
                    vendeur: (client.vendeur || '').replace(/"/g, '""'),
                    statut: (client.statut || 'prospect').replace(/"/g, '""')
                };

                csv += `"${cleanData.nom}";"${cleanData.prenom}";"${cleanData.email}";"${cleanData.telephone}";"${cleanData.naissance}";"${cleanData.adresse}";"${cleanData.code_postal}";"${cleanData.ville}";"${cleanData.vendeur}";"${cleanData.statut}"\n`;
            });

            // Télécharger le fichier avec BOM UTF-8 pour Excel
            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `clients_binance_crm_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification(`${clients.length} clients exportés en CSV`, 'success');
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        // Système de notifications
        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'info': 'alert-info',
                'warning': 'alert-warning'
            };

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass[type]} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            // Auto-dismiss après 5 secondes
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Charger les données depuis l'API réelle ou localStorage
        async function loadClientsFromStorage() {
            try {
                // Essayer de charger depuis l'API réelle
                const response = await fetch('http://localhost:8003/api/clients');
                if (response.ok) {
                    const result = await response.json();
                    if (result.success) {
                        clients = result.data.map(client => ({
                            id: client.id,
                            prenom: client.first_name,
                            nom: client.last_name,
                            email: client.email,
                            telephone: client.phone || '',
                            vendeur: client.assigned_to ? `user_${client.assigned_to}` : 'marie.martin',
                            indicateur: client.status === 'actif' ? 'Vert' : client.status === 'en_cours' ? 'Orange' : 'Rouge',
                            activite: new Date(client.last_activity).toLocaleDateString('fr-FR')
                        }));
                        filteredClients = [...clients];
                        showNotification(`${clients.length} clients chargés depuis la base de données réelle`, 'success');
                        return;
                    }
                }
            } catch (error) {
                console.log('API non disponible, utilisation du localStorage');
            }

            // Fallback vers localStorage
            const savedClients = localStorage.getItem('binance_crm_clients');
            if (savedClients) {
                try {
                    const parsedClients = JSON.parse(savedClients);
                    if (Array.isArray(parsedClients) && parsedClients.length > 0) {
                        clients = parsedClients;
                        filteredClients = [...clients];
                        showNotification(`${clients.length} clients chargés depuis la sauvegarde locale`, 'info');
                    }
                } catch (error) {
                    console.error('Erreur lors du chargement des clients:', error);
                }
            }

            // Si aucune donnée, charger les données par défaut
            if (clients.length === 0) {
                loadDefaultClients();
            }
        }

        // ========== FONCTIONS IMPORT CSV ==========

        let csvData = [];
        let currentImportStep = 1;
        let columnMapping = {};
        let validationResults = {};
        let lastImportId = null;

        function showImportModal() {
            // Réinitialiser l'état
            currentImportStep = 1;
            csvData = [];
            columnMapping = {};
            validationResults = {};

            // Réinitialiser l'interface
            updateImportStep(1);
            document.getElementById('csvFile').value = '';
            document.getElementById('fileInfo').style.display = 'none';
            document.getElementById('nextStepBtn').disabled = true;

            new bootstrap.Modal(document.getElementById('importModal')).show();
        }

        function updateImportStep(step) {
            currentImportStep = step;

            // Mettre à jour la barre de progression
            const progress = (step / 5) * 100;
            document.getElementById('importProgress').style.width = progress + '%';
            document.getElementById('currentStep').textContent = step;

            // Descriptions des étapes
            const stepDescriptions = {
                1: 'Sélection du fichier',
                2: 'Correspondance des colonnes',
                3: 'Prévisualisation et validation',
                4: 'Import en cours',
                5: 'Résultats'
            };
            document.getElementById('stepDescription').textContent = stepDescriptions[step];

            // Afficher/masquer les étapes
            for (let i = 1; i <= 5; i++) {
                const stepElement = document.getElementById('step' + i);
                if (stepElement) {
                    stepElement.style.display = i === step ? 'block' : 'none';
                }
            }

            // Gérer les boutons
            const prevBtn = document.getElementById('prevStepBtn');
            const nextBtn = document.getElementById('nextStepBtn');

            prevBtn.style.display = step > 1 && step < 5 ? 'inline-block' : 'none';

            if (step === 5) {
                nextBtn.style.display = 'none';
            } else {
                nextBtn.style.display = 'inline-block';
                nextBtn.disabled = false;

                // Textes des boutons
                const buttonTexts = {
                    1: 'Analyser le fichier <i class="bi bi-arrow-right"></i>',
                    2: 'Valider les données <i class="bi bi-arrow-right"></i>',
                    3: 'Lancer l\'import <i class="bi bi-upload"></i>',
                    4: 'Import en cours...'
                };
                nextBtn.innerHTML = buttonTexts[step];
                nextBtn.disabled = step === 4;
            }
        }

        function nextStep() {
            switch (currentImportStep) {
                case 1:
                    if (csvData.length > 0) {
                        setupColumnMapping();
                        updateImportStep(2);
                    }
                    break;
                case 2:
                    validateCsvData();
                    break;
                case 3:
                    startImport();
                    break;
            }
        }

        function previousStep() {
            if (currentImportStep > 1) {
                updateImportStep(currentImportStep - 1);
            }
        }

        // Gestion du drag & drop et sélection de fichier
        function initializeFileHandling() {
            const dropZone = document.getElementById('dropZone');
            const fileInput = document.getElementById('csvFile');

            if (!dropZone || !fileInput) return;

            // Clic sur la zone de drop
            dropZone.addEventListener('click', () => fileInput.click());

            // Drag & drop
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('border-success');
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('border-success');
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('border-success');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelection(files[0]);
                }
            });

            // Sélection de fichier
            fileInput.addEventListener('change', (e) => {
                if (e.target.files.length > 0) {
                    handleFileSelection(e.target.files[0]);
                }
            });
        }

        function handleFileSelection(file) {
            // Validation du fichier
            if (!file.name.toLowerCase().endsWith('.csv')) {
                showNotification('Veuillez sélectionner un fichier CSV', 'error');
                return;
            }

            if (file.size > 10 * 1024 * 1024) { // 10MB
                showNotification('Le fichier est trop volumineux (max 10MB)', 'error');
                return;
            }

            // Afficher les informations du fichier
            document.getElementById('fileName').textContent = file.name;
            document.getElementById('fileSize').textContent = formatFileSize(file.size);

            // Lire le fichier CSV
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const csv = e.target.result;

                    // Parser le CSV avec Papa Parse
                    Papa.parse(csv, {
                        delimiter: ';',
                        header: true,
                        skipEmptyLines: true,
                        encoding: 'UTF-8',
                        complete: function(results) {
                            if (results.errors.length > 0) {
                                showNotification('Erreur lors de la lecture du CSV: ' + results.errors[0].message, 'error');
                                return;
                            }

                            csvData = results.data;

                            if (csvData.length === 0) {
                                showNotification('Le fichier CSV est vide', 'error');
                                return;
                            }

                            if (csvData.length > 1000) {
                                showNotification('Trop de lignes dans le fichier (max 1000)', 'error');
                                return;
                            }

                            // Afficher les informations
                            document.getElementById('fileRows').textContent = csvData.length;
                            document.getElementById('fileInfo').style.display = 'block';
                            document.getElementById('nextStepBtn').disabled = false;

                            showNotification('Fichier CSV chargé avec succès', 'success');
                        }
                    });
                } catch (error) {
                    showNotification('Erreur lors de la lecture du fichier: ' + error.message, 'error');
                }
            };

            reader.readAsText(file, 'UTF-8');
        }

        function setupColumnMapping() {
            const detectedColumns = Object.keys(csvData[0] || {});
            const targetColumns = ['nom', 'prenom', 'email', 'telephone', 'naissance', 'adresse', 'code_postal', 'ville', 'vendeur', 'statut'];

            const detectedContainer = document.getElementById('detectedColumns');
            const mappingContainer = document.getElementById('columnMapping');

            detectedContainer.innerHTML = '';
            mappingContainer.innerHTML = '';

            // Afficher les colonnes détectées
            detectedColumns.forEach(col => {
                const item = document.createElement('div');
                item.className = 'list-group-item';
                item.innerHTML = `<code>${col}</code>`;
                detectedContainer.appendChild(item);
            });

            // Créer les mappings automatiques et manuels
            targetColumns.forEach(targetCol => {
                const mappingDiv = document.createElement('div');
                mappingDiv.className = 'mb-3';

                // Auto-détection
                let autoSelected = '';
                const lowerTarget = targetCol.toLowerCase();
                for (const detectedCol of detectedColumns) {
                    if (detectedCol.toLowerCase().includes(lowerTarget) || lowerTarget.includes(detectedCol.toLowerCase())) {
                        autoSelected = detectedCol;
                        break;
                    }
                }

                mappingDiv.innerHTML = `
                    <label class="form-label"><code>${targetCol}</code></label>
                    <select class="form-select" onchange="updateColumnMapping('${targetCol}', this.value)">
                        <option value="">-- Ignorer --</option>
                        ${detectedColumns.map(col =>
                            `<option value="${col}" ${col === autoSelected ? 'selected' : ''}>${col}</option>`
                        ).join('')}
                    </select>
                `;

                mappingContainer.appendChild(mappingDiv);

                // Sauvegarder le mapping initial
                if (autoSelected) {
                    columnMapping[targetCol] = autoSelected;
                }
            });
        }

        function updateColumnMapping(targetCol, sourceCol) {
            if (sourceCol) {
                columnMapping[targetCol] = sourceCol;
            } else {
                delete columnMapping[targetCol];
            }
        }

        async function validateCsvData() {
            try {
                // Préparer les données pour validation
                const mappedData = csvData.map(row => {
                    const mappedRow = {};
                    for (const [target, source] of Object.entries(columnMapping)) {
                        if (row[source] !== undefined) {
                            mappedRow[target] = row[source];
                        }
                    }
                    return mappedRow;
                });

                const response = await fetch('http://localhost:8000/api/validate-csv', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        csv_data: mappedData
                    })
                });

                validationResults = await response.json();

                if (validationResults.success) {
                    // Afficher les statistiques
                    document.getElementById('totalRowsPreview').textContent = validationResults.total_rows;
                    document.getElementById('validRowsPreview').textContent = validationResults.valid_rows;
                    document.getElementById('errorRowsPreview').textContent = validationResults.error_rows;

                    // Afficher la prévisualisation
                    displayPreview(mappedData.slice(0, 10)); // Première 10 lignes

                    // Afficher les erreurs
                    displayValidationErrors(validationResults.errors);

                    updateImportStep(3);
                } else {
                    showNotification('Erreur de validation: ' + validationResults.error, 'error');
                }
            } catch (error) {
                showNotification('Erreur lors de la validation: ' + error.message, 'error');
            }
        }

        function displayPreview(data) {
            const headerRow = document.getElementById('previewTableHeader');
            const bodyContainer = document.getElementById('previewTableBody');

            headerRow.innerHTML = '';
            bodyContainer.innerHTML = '';

            if (data.length === 0) return;

            // En-têtes
            const columns = Object.keys(data[0]);
            columns.forEach(col => {
                const th = document.createElement('th');
                th.textContent = col;
                headerRow.appendChild(th);
            });

            // Données
            data.forEach((row, index) => {
                const tr = document.createElement('tr');

                // Vérifier si cette ligne a des erreurs
                const hasError = validationResults.errors && validationResults.errors.some(error => error.row === index + 1);
                if (hasError) {
                    tr.classList.add('table-danger');
                }

                columns.forEach(col => {
                    const td = document.createElement('td');
                    td.textContent = row[col] || '';
                    tr.appendChild(td);
                });

                bodyContainer.appendChild(tr);
            });
        }

        function displayValidationErrors(errors) {
            const container = document.getElementById('validationErrors');
            container.innerHTML = '';

            if (!errors || errors.length === 0) {
                container.innerHTML = '<div class="alert alert-success"><i class="bi bi-check-circle"></i> Aucune erreur détectée !</div>';
                return;
            }

            const errorDiv = document.createElement('div');
            errorDiv.className = 'alert alert-warning';
            errorDiv.innerHTML = `
                <h6><i class="bi bi-exclamation-triangle"></i> Erreurs détectées (${errors.length})</h6>
                <div class="accordion" id="errorsAccordion">
                    ${errors.slice(0, 5).map((error, index) => `
                        <div class="accordion-item">
                            <h2 class="accordion-header">
                                <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#error${index}">
                                    Ligne ${error.row}: ${error.errors.length} erreur(s)
                                </button>
                            </h2>
                            <div id="error${index}" class="accordion-collapse collapse" data-bs-parent="#errorsAccordion">
                                <div class="accordion-body">
                                    <ul class="mb-0">
                                        ${error.errors.map(err => `<li>${err}</li>`).join('')}
                                    </ul>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
                ${errors.length > 5 ? `<small class="text-muted">... et ${errors.length - 5} autres erreurs</small>` : ''}
            `;

            container.appendChild(errorDiv);
        }

        async function startImport() {
            updateImportStep(4);

            try {
                // Préparer les données mappées
                const mappedData = csvData.map(row => {
                    const mappedRow = {};
                    for (const [target, source] of Object.entries(columnMapping)) {
                        if (row[source] !== undefined) {
                            mappedRow[target] = row[source];
                        }
                    }
                    return mappedRow;
                });

                const user = JSON.parse(sessionStorage.getItem('user') || '{}');
                const skipErrors = document.getElementById('skipErrors').checked;

                // Simuler la progression
                let progress = 0;
                const progressBar = document.getElementById('importProgressBar');
                const statusText = document.getElementById('importStatus');

                const progressInterval = setInterval(() => {
                    progress += Math.random() * 20;
                    if (progress > 90) progress = 90;
                    progressBar.style.width = progress + '%';
                    statusText.textContent = `Import en cours... ${Math.round(progress)}%`;
                }, 500);

                const response = await fetch('http://localhost:8000/api/import-clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        csv_data: mappedData,
                        filename: document.getElementById('fileName').textContent,
                        user_id: user.id || 1,
                        skip_errors: skipErrors
                    })
                });

                const result = await response.json();

                clearInterval(progressInterval);
                progressBar.style.width = '100%';
                statusText.textContent = 'Import terminé !';

                if (result.success) {
                    // Afficher les résultats
                    document.getElementById('finalTotalRows').textContent = result.total_rows;
                    document.getElementById('finalSuccessRows').textContent = result.successful_rows;
                    document.getElementById('finalSkippedRows').textContent = result.skipped_rows;
                    document.getElementById('finalErrorRows').textContent = result.error_rows;

                    lastImportId = result.import_id;

                    // Afficher le bouton d'annulation si applicable
                    if (result.successful_rows > 0) {
                        const undoBtn = document.getElementById('undoImportBtn');
                        undoBtn.style.display = 'inline-block';
                        undoBtn.onclick = () => undoImport(result.import_id);
                    }

                    // Afficher le résumé détaillé
                    displayImportSummary(result);

                    updateImportStep(5);

                    // Recharger la liste des clients
                    loadClientsFromStorage();
                    renderClientsTable();

                    showNotification(`Import terminé ! ${result.successful_rows} clients créés.`, 'success');
                } else {
                    showNotification('Erreur lors de l\'import: ' + result.error, 'error');
                    updateImportStep(3); // Retour à l'étape de validation
                }
            } catch (error) {
                showNotification('Erreur lors de l\'import: ' + error.message, 'error');
                updateImportStep(3);
            }
        }

        function displayImportSummary(result) {
            const container = document.getElementById('importSummary');
            let summaryHtml = '';

            if (result.errors && result.errors.length > 0) {
                summaryHtml += `
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Erreurs rencontrées (${result.errors.length})</h6>
                        <div class="accordion" id="importErrorsAccordion">
                            ${result.errors.slice(0, 3).map((error, index) => `
                                <div class="accordion-item">
                                    <h2 class="accordion-header">
                                        <button class="accordion-button collapsed" type="button" data-bs-toggle="collapse" data-bs-target="#importError${index}">
                                            Ligne ${error.row}: ${error.error}
                                        </button>
                                    </h2>
                                    <div id="importError${index}" class="accordion-collapse collapse" data-bs-parent="#importErrorsAccordion">
                                        <div class="accordion-body">
                                            <small class="text-muted">Cette ligne a été ignorée lors de l'import.</small>
                                        </div>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                `;
            }

            if (result.skipped && result.skipped.length > 0) {
                summaryHtml += `
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> Lignes ignorées (${result.skipped.length})</h6>
                        <small>Ces lignes ont été ignorées car elles contenaient des données en doublon ou invalides.</small>
                    </div>
                `;
            }

            container.innerHTML = summaryHtml;
        }

        async function downloadTemplate() {
            try {
                const response = await fetch('http://localhost:8000/api/import-template');
                const result = await response.json();

                if (result.success) {
                    // Créer et télécharger le fichier
                    const blob = new Blob([result.content], { type: 'text/csv;charset=utf-8' });
                    const url = URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = result.filename;
                    a.click();
                    URL.revokeObjectURL(url);

                    showNotification('Template téléchargé avec succès', 'success');
                } else {
                    showNotification('Erreur lors du téléchargement du template', 'error');
                }
            } catch (error) {
                showNotification('Erreur: ' + error.message, 'error');
            }
        }

        async function undoImport(importId) {
            if (!confirm('Êtes-vous sûr de vouloir annuler cet import ? Tous les clients importés seront supprimés.')) {
                return;
            }

            try {
                const response = await fetch(`http://localhost:8000/api/undo-import/${importId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(result.message, 'success');
                    document.getElementById('undoImportBtn').style.display = 'none';
                    loadClientsFromStorage();
                    renderClientsTable();
                } else {
                    showNotification('Erreur: ' + result.error, 'error');
                }
            } catch (error) {
                showNotification('Erreur: ' + error.message, 'error');
            }
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

        function showImportHistory() {
            // Cette fonction pourrait ouvrir un modal avec l'historique des imports
            showNotification('Fonctionnalité d\'historique à implémenter', 'info');
        }

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadClientsFromStorage();
            renderClientsTable();
            initializeFileHandling();
        });

        // ===== FONCTIONNALITÉS CSV AVANCÉES =====

        async function downloadCSVTemplate() {
            try {
                showNotification('Téléchargement du template CSV...', 'info');

                const response = await fetch('/api/import-template');
                const result = await response.json();

                if (result.success) {
                    // Créer et télécharger le fichier
                    const blob = new Blob([result.csv_content], { type: 'text/csv;charset=utf-8;' });
                    const link = document.createElement('a');
                    const url = URL.createObjectURL(blob);

                    link.setAttribute('href', url);
                    link.setAttribute('download', result.filename || 'template_clients.csv');
                    link.style.visibility = 'hidden';

                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);

                    showNotification('Template CSV téléchargé avec succès', 'success');
                } else {
                    showNotification('Erreur: ' + (result.error || 'Impossible de télécharger le template'), 'error');
                }
            } catch (error) {
                console.error('Erreur téléchargement template:', error);
                showNotification('Erreur lors du téléchargement du template', 'error');
            }
        }

        async function validateCSVBeforeImport() {
            const fileInput = document.getElementById('csvFile');
            if (!fileInput || !fileInput.files[0]) {
                showNotification('Veuillez d\'abord sélectionner un fichier CSV à valider', 'warning');
                // Ouvrir le modal d'import pour sélectionner un fichier
                new bootstrap.Modal(document.getElementById('importModal')).show();
                return;
            }

            const file = fileInput.files[0];

            try {
                showNotification('Validation du fichier CSV en cours...', 'info');

                const csvText = await readFileAsText(file);
                const csvData = parseCSVData(csvText);

                const response = await fetch('/api/validate-csv', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        csv_data: csvData,
                        filename: file.name
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showValidationResults(result, csvData);
                } else {
                    showNotification('Erreur de validation: ' + (result.error || 'Validation échouée'), 'error');
                }
            } catch (error) {
                console.error('Erreur validation CSV:', error);
                showNotification('Erreur lors de la validation du CSV', 'error');
            }
        }

        function readFileAsText(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = e => resolve(e.target.result);
                reader.onerror = reject;
                reader.readAsText(file, 'UTF-8');
            });
        }

        function parseCSVData(csvText) {
            const lines = csvText.split('\n').filter(line => line.trim());
            if (lines.length < 2) return [];

            const headers = lines[0].split(',').map(h => h.trim().replace(/"/g, ''));
            const data = [];

            for (let i = 1; i < lines.length; i++) {
                const values = lines[i].split(',').map(v => v.trim().replace(/"/g, ''));
                const row = {};
                headers.forEach((header, index) => {
                    row[header] = values[index] || '';
                });
                data.push(row);
            }

            return data;
        }

        function showValidationResults(result, csvData) {
            const modal = new bootstrap.Modal(document.getElementById('validationModal'));
            const resultsDiv = document.getElementById('validationResults');
            const proceedBtn = document.getElementById('proceedImportBtn');

            let html = `
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card text-center ${result.valid_rows > 0 ? 'border-success' : 'border-secondary'}">
                            <div class="card-body">
                                <h3 class="text-success">${result.valid_rows || 0}</h3>
                                <p class="card-text">Lignes valides</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center ${result.invalid_rows > 0 ? 'border-warning' : 'border-secondary'}">
                            <div class="card-body">
                                <h3 class="text-warning">${result.invalid_rows || 0}</h3>
                                <p class="card-text">Lignes avec erreurs</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card text-center border-info">
                            <div class="card-body">
                                <h3 class="text-info">${(result.valid_rows || 0) + (result.invalid_rows || 0)}</h3>
                                <p class="card-text">Total lignes</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            if (result.errors && result.errors.length > 0) {
                html += `
                    <div class="alert alert-warning">
                        <h6><i class="bi bi-exclamation-triangle"></i> Erreurs détectées:</h6>
                        <ul class="mb-0">
                `;
                result.errors.slice(0, 10).forEach(error => {
                    html += `<li>${error}</li>`;
                });
                if (result.errors.length > 10) {
                    html += `<li><em>... et ${result.errors.length - 10} autres erreurs</em></li>`;
                }
                html += `</ul></div>`;
            }

            if (result.warnings && result.warnings.length > 0) {
                html += `
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle"></i> Avertissements:</h6>
                        <ul class="mb-0">
                `;
                result.warnings.slice(0, 5).forEach(warning => {
                    html += `<li>${warning}</li>`;
                });
                html += `</ul></div>`;
            }

            if (result.valid_rows > 0) {
                html += `
                    <div class="alert alert-success">
                        <i class="bi bi-check-circle"></i>
                        ${result.valid_rows} ligne(s) peuvent être importées avec succès.
                    </div>
                `;
                proceedBtn.style.display = 'inline-block';
                // Stocker les données validées pour l'import
                window.validatedCSVData = csvData.slice(0, result.valid_rows);
            } else {
                proceedBtn.style.display = 'none';
            }

            resultsDiv.innerHTML = html;
            modal.show();
        }

        async function proceedWithImport() {
            if (!window.validatedCSVData) {
                showNotification('Aucune donnée validée à importer', 'error');
                return;
            }

            try {
                bootstrap.Modal.getInstance(document.getElementById('validationModal')).hide();
                showNotification('Import des données validées en cours...', 'info');

                const response = await fetch('/api/import-clients', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        csv_data: window.validatedCSVData,
                        filename: 'validated_import.csv',
                        user_id: 1,
                        skip_errors: false // Données déjà validées
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(`Import réussi: ${result.successful_rows} clients importés`, 'success');
                    loadClients(); // Recharger la liste
                    delete window.validatedCSVData; // Nettoyer
                } else {
                    showNotification('Erreur lors de l\'import: ' + (result.error || 'Import échoué'), 'error');
                }
            } catch (error) {
                console.error('Erreur import validé:', error);
                showNotification('Erreur lors de l\'import des données validées', 'error');
            }
        }

        async function showImportHistory() {
            try {
                showNotification('Chargement de l\'historique des imports...', 'info');

                const response = await fetch('/api/import-history');
                const result = await response.json();

                if (result.success) {
                    displayImportHistory(result.data || []);
                } else {
                    showNotification('Erreur: ' + (result.error || 'Impossible de charger l\'historique'), 'error');
                }
            } catch (error) {
                console.error('Erreur chargement historique:', error);
                showNotification('Erreur lors du chargement de l\'historique', 'error');
            }
        }

        function displayImportHistory(history) {
            const modal = new bootstrap.Modal(document.getElementById('historyModal'));
            const tbody = document.getElementById('historyTableBody');
            const emptyDiv = document.getElementById('historyEmpty');

            if (!history || history.length === 0) {
                tbody.innerHTML = '';
                emptyDiv.style.display = 'block';
            } else {
                emptyDiv.style.display = 'none';
                tbody.innerHTML = '';

                history.forEach(item => {
                    const date = new Date(item.import_date).toLocaleDateString('fr-FR', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit'
                    });

                    const statusBadge = getStatusBadge(item.status);
                    const canUndo = item.can_undo && item.status === 'completed';

                    const row = `
                        <tr>
                            <td>${date}</td>
                            <td>
                                <i class="bi bi-file-earmark-text"></i>
                                ${item.filename || 'Fichier inconnu'}
                            </td>
                            <td>
                                <i class="bi bi-person"></i>
                                ${item.user_name || 'Utilisateur #' + item.user_id}
                            </td>
                            <td><span class="badge bg-info">${item.total_rows || 0}</span></td>
                            <td><span class="badge bg-success">${item.successful_rows || 0}</span></td>
                            <td><span class="badge bg-danger">${item.failed_rows || 0}</span></td>
                            <td>${statusBadge}</td>
                            <td>
                                ${canUndo ?
                                    `<button class="btn btn-sm btn-outline-danger" onclick="undoImport(${item.id}, '${item.filename}')" title="Annuler cet import">
                                        <i class="bi bi-arrow-counterclockwise"></i> Annuler
                                    </button>` :
                                    '<span class="text-muted">-</span>'
                                }
                                ${item.error_details ?
                                    `<button class="btn btn-sm btn-outline-info ms-1" onclick="showImportDetails(${item.id})" title="Voir les détails">
                                        <i class="bi bi-info-circle"></i>
                                    </button>` : ''
                                }
                            </td>
                        </tr>
                    `;
                    tbody.innerHTML += row;
                });
            }

            modal.show();
        }

        function getStatusBadge(status) {
            const statusMap = {
                'completed': '<span class="badge bg-success">Terminé</span>',
                'failed': '<span class="badge bg-danger">Échoué</span>',
                'partial': '<span class="badge bg-warning">Partiel</span>',
                'processing': '<span class="badge bg-info">En cours</span>'
            };
            return statusMap[status] || '<span class="badge bg-secondary">Inconnu</span>';
        }

        async function undoImport(importId, filename) {
            if (!confirm(`Êtes-vous sûr de vouloir annuler l'import "${filename}" ?\n\nCette action supprimera tous les clients importés lors de cette opération.`)) {
                return;
            }

            try {
                showNotification('Annulation de l\'import en cours...', 'info');

                const response = await fetch(`/api/undo-import/${importId}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const result = await response.json();

                if (result.success) {
                    showNotification(result.message || 'Import annulé avec succès', 'success');
                    refreshImportHistory(); // Rafraîchir la liste
                    loadClients(); // Rafraîchir la liste des clients
                } else {
                    showNotification('Erreur: ' + (result.error || 'Impossible d\'annuler l\'import'), 'error');
                }
            } catch (error) {
                console.error('Erreur annulation import:', error);
                showNotification('Erreur lors de l\'annulation de l\'import', 'error');
            }
        }

        function refreshImportHistory() {
            showImportHistory();
        }

        async function showImportDetails(importId) {
            try {
                const response = await fetch(`/api/import-history/${importId}`);
                const result = await response.json();

                if (result.success && result.data) {
                    const details = result.data;
                    let detailsHtml = `
                        <div class="modal fade" id="importDetailsModal" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">
                                            <i class="bi bi-info-circle"></i> Détails de l'import
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <h6>Informations générales</h6>
                                        <ul>
                                            <li><strong>Fichier:</strong> ${details.filename}</li>
                                            <li><strong>Date:</strong> ${new Date(details.import_date).toLocaleString('fr-FR')}</li>
                                            <li><strong>Durée:</strong> ${details.duration || 'N/A'}</li>
                                        </ul>
                    `;

                    if (details.error_details) {
                        detailsHtml += `
                            <h6 class="mt-3">Erreurs détectées</h6>
                            <div class="alert alert-warning">
                                <pre style="white-space: pre-wrap; font-size: 0.9em;">${details.error_details}</pre>
                            </div>
                        `;
                    }

                    detailsHtml += `
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // Supprimer le modal existant s'il y en a un
                    const existingModal = document.getElementById('importDetailsModal');
                    if (existingModal) {
                        existingModal.remove();
                    }

                    document.body.insertAdjacentHTML('beforeend', detailsHtml);
                    new bootstrap.Modal(document.getElementById('importDetailsModal')).show();

                    // Nettoyer après fermeture
                    document.getElementById('importDetailsModal').addEventListener('hidden.bs.modal', function() {
                        this.remove();
                    });
                }
            } catch (error) {
                console.error('Erreur détails import:', error);
                showNotification('Erreur lors du chargement des détails', 'error');
            }
        }

        console.log('👥 BINANCE CRM Clients - Page chargée avec succès');
    </script>
</body>
</html>

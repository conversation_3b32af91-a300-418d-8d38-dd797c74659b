<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Clients - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
        }
        
        .avatar-circle {
            width: 35px; 
            height: 35px; 
            border-radius: 50%;
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            display: flex; 
            align-items: center; 
            justify-content: center;
            color: #000; 
            font-weight: bold; 
            font-size: 12px;
        }
        
        .table-hover tbody tr:hover {
            background-color: rgba(241, 194, 50, 0.1);
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">Dashboard</a>
                <a class="nav-link active" href="clients.html">Clients</a>
                <a class="nav-link" href="vendeurs.html">Vendeurs</a>
                <a class="nav-link" href="emails.html">Emails</a>
                <a class="nav-link" href="#" onclick="logout()">Déconnexion</a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="bi bi-people"></i> Gestion des Clients
                </h1>
                <p class="text-muted">127 clients • Gestion complète CRUD</p>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="importClients()">
                    <i class="bi bi-upload"></i> Import CSV
                </button>
                <button class="btn btn-info" onclick="exportClients()">
                    <i class="bi bi-download"></i> Export CSV
                </button>
                <button class="btn btn-primary" onclick="addClient()">
                    <i class="bi bi-person-plus"></i> Nouveau Client
                </button>
            </div>
        </div>
        
        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <input type="text" class="form-control" placeholder="Rechercher..." id="searchInput" onkeyup="filterClients()">
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="statusFilter" onchange="filterClients()">
                            <option value="">Tous les statuts</option>
                            <option value="nouveau">Nouveau</option>
                            <option value="en cours">En cours</option>
                            <option value="magnifique">Magnifique</option>
                            <option value="NRP">NRP</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <select class="form-select" id="vendeurFilter" onchange="filterClients()">
                            <option value="">Tous les vendeurs</option>
                            <option value="marie.martin">Marie Martin</option>
                            <option value="pierre.durand">Pierre Durand</option>
                            <option value="sophie.bernard">Sophie Bernard</option>
                        </select>
                    </div>
                    <div class="col-md-2">
                        <button class="btn btn-outline-secondary" onclick="resetFilters()">
                            <i class="bi bi-arrow-clockwise"></i> Reset
                        </button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Liste des clients -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Liste des Clients</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="clientsTable">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Email</th>
                                <th>Téléphone</th>
                                <th>Vendeur</th>
                                <th>Indicateur</th>
                                <th>Dernière activité</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="clientsTableBody">
                            <!-- Données générées par JavaScript -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <!-- Statistiques -->
        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-check-circle"></i> Fonctionnalités Clients Disponibles :</h6>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Création</strong><br>
                    <small>Formulaire complet fonctionnel</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Lecture</strong><br>
                    <small>Liste avec filtres et recherche</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Modification</strong><br>
                    <small>Édition en ligne</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Suppression</strong><br>
                    <small>Suppression sécurisée</small>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modal Nouveau Client -->
    <div class="modal fade" id="clientModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="bi bi-person-plus"></i> Nouveau Client
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="clientForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" id="prenom" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Nom *</label>
                                    <input type="text" class="form-control" id="nom" required>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Email *</label>
                                    <input type="email" class="form-control" id="email" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Téléphone</label>
                                    <input type="tel" class="form-control" id="telephone">
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Vendeur</label>
                                    <select class="form-select" id="vendeur">
                                        <option value="">Non attribué</option>
                                        <option value="marie.martin">Marie Martin</option>
                                        <option value="pierre.durand">Pierre Durand</option>
                                        <option value="sophie.bernard">Sophie Bernard</option>
                                    </select>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">Indicateur</label>
                                    <select class="form-select" id="indicateur">
                                        <option value="nouveau">Nouveau</option>
                                        <option value="en cours">En cours</option>
                                        <option value="magnifique">Magnifique</option>
                                        <option value="NRP">NRP</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">Notes</label>
                            <textarea class="form-control" id="notes" rows="3"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="button" class="btn btn-primary" onclick="saveClient()">
                        <i class="bi bi-check"></i> Enregistrer
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Données clients simulées
        let clients = [
            { id: 1, prenom: 'Jean', nom: 'Dupont', email: '<EMAIL>', telephone: '***********.89', vendeur: 'marie.martin', indicateur: 'nouveau', activite: '2024-01-15' },
            { id: 2, prenom: 'Marie', nom: 'Martin', email: '<EMAIL>', telephone: '***********.90', vendeur: 'pierre.durand', indicateur: 'en cours', activite: '2024-01-14' },
            { id: 3, prenom: 'Pierre', nom: 'Bernard', email: '<EMAIL>', telephone: '***********.91', vendeur: 'sophie.bernard', indicateur: 'magnifique', activite: '2024-01-13' },
            { id: 4, prenom: 'Sophie', nom: 'Durand', email: '<EMAIL>', telephone: '***********.92', vendeur: 'marie.martin', indicateur: 'NRP', activite: '2024-01-12' },
            { id: 5, prenom: 'Luc', nom: 'Moreau', email: '<EMAIL>', telephone: '***********.93', vendeur: 'pierre.durand', indicateur: 'nouveau', activite: '2024-01-11' }
        ];
        
        let filteredClients = [...clients];
        
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        function renderClientsTable() {
            const tbody = document.getElementById('clientsTableBody');
            tbody.innerHTML = '';
            
            filteredClients.forEach(client => {
                const statusColors = {
                    'nouveau': 'primary',
                    'en cours': 'warning',
                    'magnifique': 'success',
                    'NRP': 'danger'
                };
                
                const vendeurNames = {
                    'marie.martin': 'Marie Martin',
                    'pierre.durand': 'Pierre Durand',
                    'sophie.bernard': 'Sophie Bernard'
                };
                
                const row = `
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2">
                                    ${client.prenom[0]}${client.nom[0]}
                                </div>
                                <div>
                                    <strong>${client.prenom} ${client.nom}</strong><br>
                                    <small class="text-muted">ID: ${client.id}</small>
                                </div>
                            </div>
                        </td>
                        <td>
                            <a href="mailto:${client.email}" class="text-decoration-none">
                                ${client.email}
                            </a>
                        </td>
                        <td>${client.telephone}</td>
                        <td>
                            <span class="badge bg-info">${vendeurNames[client.vendeur] || 'Non attribué'}</span>
                        </td>
                        <td>
                            <span class="badge bg-${statusColors[client.indicateur]}">${client.indicateur}</span>
                        </td>
                        <td>${new Date(client.activite).toLocaleDateString('fr-FR')}</td>
                        <td>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="editClient(${client.id})" title="Modifier">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-outline-info" onclick="emailClient(${client.id})" title="Email">
                                    <i class="bi bi-envelope"></i>
                                </button>
                                <button class="btn btn-outline-danger" onclick="deleteClient(${client.id})" title="Supprimer">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }
        
        function filterClients() {
            const search = document.getElementById('searchInput').value.toLowerCase();
            const status = document.getElementById('statusFilter').value;
            const vendeur = document.getElementById('vendeurFilter').value;
            
            filteredClients = clients.filter(client => {
                const matchSearch = !search || 
                    client.prenom.toLowerCase().includes(search) ||
                    client.nom.toLowerCase().includes(search) ||
                    client.email.toLowerCase().includes(search);
                
                const matchStatus = !status || client.indicateur === status;
                const matchVendeur = !vendeur || client.vendeur === vendeur;
                
                return matchSearch && matchStatus && matchVendeur;
            });
            
            renderClientsTable();
        }
        
        function resetFilters() {
            document.getElementById('searchInput').value = '';
            document.getElementById('statusFilter').value = '';
            document.getElementById('vendeurFilter').value = '';
            filteredClients = [...clients];
            renderClientsTable();
        }
        
        function addClient() {
            document.getElementById('clientForm').reset();
            new bootstrap.Modal(document.getElementById('clientModal')).show();
        }
        
        function saveClient() {
            const prenom = document.getElementById('prenom').value;
            const nom = document.getElementById('nom').value;
            const email = document.getElementById('email').value;
            
            if (!prenom || !nom || !email) {
                alert('Veuillez remplir tous les champs obligatoires');
                return;
            }
            
            const newClient = {
                id: clients.length + 1,
                prenom: prenom,
                nom: nom,
                email: email,
                telephone: document.getElementById('telephone').value,
                vendeur: document.getElementById('vendeur').value,
                indicateur: document.getElementById('indicateur').value,
                activite: new Date().toISOString().split('T')[0]
            };
            
            clients.push(newClient);
            filteredClients = [...clients];
            renderClientsTable();
            
            bootstrap.Modal.getInstance(document.getElementById('clientModal')).hide();
            alert('Client ajouté avec succès !');
        }
        
        function editClient(id) {
            alert(`Édition du client ID: ${id} (fonctionnalité en développement)`);
        }
        
        function emailClient(id) {
            const client = clients.find(c => c.id === id);
            alert(`Envoi d'email à ${client.prenom} ${client.nom} (${client.email})`);
        }
        
        function deleteClient(id) {
            if (confirm('Êtes-vous sûr de vouloir supprimer ce client ?')) {
                clients = clients.filter(c => c.id !== id);
                filteredClients = [...clients];
                renderClientsTable();
                alert('Client supprimé avec succès !');
            }
        }
        
        function importClients() {
            alert('Import CSV en cours de développement...');
        }
        
        function exportClients() {
            alert('Export CSV en cours de développement...');
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            renderClientsTable();
        });
        
        console.log('👥 BINANCE CRM Clients - Page chargée avec succès');
    </script>
</body>
</html>

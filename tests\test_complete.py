#!/usr/bin/env python3
"""
Tests complets pour l'application CRM
"""

import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import tempfile
import os
from datetime import datetime, timedelta

# Import des modules de l'application
from main import app
from database import get_db, Base
import crud
import schemas
import models

# Configuration de test
SQLALCHEMY_DATABASE_URL = "sqlite:///./test.db"
engine = create_engine(SQLALCHEMY_DATABASE_URL, connect_args={"check_same_thread": False})
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()

app.dependency_overrides[get_db] = override_get_db

@pytest.fixture(scope="module")
def setup_database():
    """Créer la base de données de test"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)

@pytest.fixture
def client():
    """Client de test FastAPI"""
    return TestClient(app)

@pytest.fixture
def db_session():
    """Session de base de données pour les tests"""
    db = TestingSessionLocal()
    try:
        yield db
    finally:
        db.close()

@pytest.fixture
def admin_user(db_session):
    """Créer un utilisateur admin pour les tests"""
    user_data = schemas.UserCreate(
        username="test_admin",
        email="<EMAIL>",
        password="test123",
        role="admin"
    )
    return crud.create_user(db_session, user_data)

@pytest.fixture
def vendeur_user(db_session):
    """Créer un utilisateur vendeur pour les tests"""
    user_data = schemas.UserCreate(
        username="test_vendeur",
        email="<EMAIL>",
        password="test123",
        role="vendeur"
    )
    return crud.create_user(db_session, user_data)

@pytest.fixture
def test_client(db_session, vendeur_user):
    """Créer un client de test"""
    client_data = schemas.ClientCreate(
        nom="Test",
        prenom="Client",
        email="<EMAIL>",
        telephone="0123456789",
        date_naissance=datetime(1990, 1, 1),
        adresse="123 Test Street",
        vendeur_id=vendeur_user.id,
        indicateur="nouveau"
    )
    return crud.create_client(db_session, client_data)

class TestAuthentication:
    """Tests d'authentification"""
    
    def test_login_page(self, client):
        """Test de la page de connexion"""
        response = client.get("/login")
        assert response.status_code == 200
        assert "Connexion" in response.text
    
    def test_login_success(self, client, admin_user):
        """Test de connexion réussie"""
        response = client.post("/login", data={
            "username": "test_admin",
            "password": "test123"
        })
        assert response.status_code == 302  # Redirection
    
    def test_login_failure(self, client):
        """Test de connexion échouée"""
        response = client.post("/login", data={
            "username": "wrong",
            "password": "wrong"
        })
        assert response.status_code == 200
        assert "incorrect" in response.text.lower()
    
    def test_logout(self, client):
        """Test de déconnexion"""
        response = client.get("/logout")
        assert response.status_code == 302  # Redirection vers login

class TestClientManagement:
    """Tests de gestion des clients"""
    
    def test_create_client(self, db_session, vendeur_user):
        """Test de création de client"""
        client_data = schemas.ClientCreate(
            nom="Nouveau",
            prenom="Client",
            email="<EMAIL>",
            telephone="0987654321",
            date_naissance=datetime(1985, 5, 15),
            adresse="456 New Street",
            vendeur_id=vendeur_user.id
        )
        
        client = crud.create_client(db_session, client_data)
        assert client.nom == "Nouveau"
        assert client.prenom == "Client"
        assert client.email == "<EMAIL>"
        assert client.vendeur_id == vendeur_user.id
    
    def test_get_clients(self, db_session, test_client):
        """Test de récupération des clients"""
        clients = crud.get_clients(db_session, limit=10)
        assert len(clients) >= 1
        assert any(c.email == "<EMAIL>" for c in clients)
    
    def test_update_client(self, db_session, test_client):
        """Test de mise à jour de client"""
        update_data = schemas.ClientUpdate(
            indicateur="magnifique",
            note="Client très intéressé"
        )
        
        updated_client = crud.update_client(db_session, test_client.id, update_data)
        assert updated_client.indicateur == "magnifique"
        assert updated_client.note == "Client très intéressé"
    
    def test_delete_client(self, db_session, test_client):
        """Test de suppression de client"""
        client_id = test_client.id
        success = crud.delete_client(db_session, client_id)
        assert success
        
        # Vérifier que le client n'existe plus
        deleted_client = crud.get_client(db_session, client_id)
        assert deleted_client is None

class TestEmailSystem:
    """Tests du système d'emails"""
    
    def test_create_email_template(self, db_session):
        """Test de création de template d'email"""
        template_data = schemas.EmailTemplateCreate(
            nom="Test Template",
            sujet="Test Subject {prenom}",
            contenu="Bonjour {prenom} {nom}, ceci est un test.",
            variables="prenom, nom"
        )
        
        template = crud.create_email_template(db_session, template_data)
        assert template.nom == "Test Template"
        assert "{prenom}" in template.sujet
        assert "{nom}" in template.contenu
    
    def test_get_email_templates(self, db_session):
        """Test de récupération des templates"""
        templates = crud.get_email_templates(db_session)
        assert isinstance(templates, list)
    
    def test_email_log_creation(self, db_session, test_client, vendeur_user):
        """Test de création de log d'email"""
        # Créer d'abord un template
        template_data = schemas.EmailTemplateCreate(
            nom="Log Test",
            sujet="Test",
            contenu="Test content"
        )
        template = crud.create_email_template(db_session, template_data)
        
        # Créer le log
        log_data = schemas.EmailLogCreate(
            client_id=test_client.id,
            template_id=template.id,
            vendeur_id=vendeur_user.id,
            sujet="Test Subject",
            contenu="Test Content",
            statut="envoye"
        )
        
        email_log = crud.create_email_log(db_session, log_data)
        assert email_log.client_id == test_client.id
        assert email_log.statut == "envoye"

class TestAppointmentSystem:
    """Tests du système de rendez-vous"""
    
    def test_create_appointment(self, db_session, test_client, vendeur_user):
        """Test de création de rendez-vous"""
        appointment_data = schemas.AppointmentCreate(
            client_id=test_client.id,
            vendeur_id=vendeur_user.id,
            date_rdv=datetime.now() + timedelta(days=1),
            titre="Test RDV",
            description="Rendez-vous de test",
            statut="planifie"
        )
        
        appointment = crud.create_appointment(db_session, appointment_data)
        assert appointment.client_id == test_client.id
        assert appointment.titre == "Test RDV"
        assert appointment.statut == "planifie"
    
    def test_get_appointments(self, db_session, vendeur_user):
        """Test de récupération des rendez-vous"""
        appointments = crud.get_appointments(db_session, vendeur_id=vendeur_user.id)
        assert isinstance(appointments, list)
    
    def test_update_appointment_status(self, db_session, test_client, vendeur_user):
        """Test de mise à jour du statut d'un rendez-vous"""
        # Créer un rendez-vous
        appointment_data = schemas.AppointmentCreate(
            client_id=test_client.id,
            vendeur_id=vendeur_user.id,
            date_rdv=datetime.now() + timedelta(days=1),
            titre="Test Update",
            statut="planifie"
        )
        appointment = crud.create_appointment(db_session, appointment_data)
        
        # Mettre à jour le statut
        update_data = schemas.AppointmentUpdate(statut="realise")
        updated_appointment = crud.update_appointment(db_session, appointment.id, update_data)
        
        assert updated_appointment.statut == "realise"

class TestUserManagement:
    """Tests de gestion des utilisateurs"""
    
    def test_create_user(self, db_session):
        """Test de création d'utilisateur"""
        user_data = schemas.UserCreate(
            username="new_user",
            email="<EMAIL>",
            password="password123",
            role="vendeur"
        )
        
        user = crud.create_user(db_session, user_data)
        assert user.username == "new_user"
        assert user.email == "<EMAIL>"
        assert user.role == "vendeur"
        assert user.is_active is True
    
    def test_authenticate_user(self, db_session, admin_user):
        """Test d'authentification d'utilisateur"""
        authenticated = crud.authenticate_user(db_session, "test_admin", "test123")
        assert authenticated is not None
        assert authenticated.username == "test_admin"
        
        # Test avec mauvais mot de passe
        not_authenticated = crud.authenticate_user(db_session, "test_admin", "wrong")
        assert not_authenticated is None
    
    def test_get_user_by_username(self, db_session, admin_user):
        """Test de récupération d'utilisateur par nom"""
        user = crud.get_user_by_username(db_session, "test_admin")
        assert user is not None
        assert user.username == "test_admin"

class TestStatistics:
    """Tests des statistiques"""
    
    def test_dashboard_stats(self, db_session, test_client):
        """Test des statistiques du dashboard"""
        stats = crud.get_dashboard_stats(db_session)
        
        assert "total_clients" in stats
        assert "clients_attribues" in stats
        assert "clients_non_attribues" in stats
        assert "emails_envoyes" in stats
        assert isinstance(stats["total_clients"], int)
    
    def test_vendeur_stats(self, db_session, vendeur_user):
        """Test des statistiques par vendeur"""
        stats = crud.get_dashboard_stats(db_session, vendeur_id=vendeur_user.id)
        
        assert isinstance(stats, dict)
        assert "total_clients" in stats

class TestSecurity:
    """Tests de sécurité"""
    
    def test_password_hashing(self):
        """Test du hachage des mots de passe"""
        password = "test123"
        hashed = crud.get_password_hash(password)
        
        assert hashed != password
        assert crud.verify_password(password, hashed)
        assert not crud.verify_password("wrong", hashed)
    
    def test_input_sanitization(self):
        """Test de la sanitisation des entrées"""
        from utils.security import sanitize_input, sanitize_html
        
        # Test sanitisation basique
        dirty_input = "<script>alert('xss')</script>Hello"
        clean_input = sanitize_input(dirty_input)
        assert "<script>" not in clean_input
        assert "Hello" in clean_input
        
        # Test sanitisation HTML
        dirty_html = "<script>alert('xss')</script><p>Valid content</p>"
        clean_html = sanitize_html(dirty_html)
        assert "<script>" not in clean_html
        assert "<p>Valid content</p>" in clean_html

class TestAPIEndpoints:
    """Tests des endpoints API"""
    
    def test_api_clients_endpoint(self, client, admin_user):
        """Test de l'endpoint API clients"""
        # Se connecter d'abord
        client.post("/login", data={
            "username": "test_admin",
            "password": "test123"
        })
        
        # Tester l'endpoint
        response = client.get("/api/clients")
        assert response.status_code == 200
        
        data = response.json()
        assert isinstance(data, list)
    
    def test_api_stats_endpoint(self, client, admin_user):
        """Test de l'endpoint API statistiques"""
        # Se connecter d'abord
        client.post("/login", data={
            "username": "test_admin",
            "password": "test123"
        })
        
        # Tester l'endpoint
        response = client.get("/api/stats")
        assert response.status_code == 200
        
        data = response.json()
        assert "total_clients" in data

def run_all_tests():
    """Exécuter tous les tests"""
    print("🧪 Exécution des tests complets...")
    
    # Configurer pytest
    pytest_args = [
        __file__,
        "-v",
        "--tb=short",
        "--disable-warnings"
    ]
    
    # Exécuter les tests
    result = pytest.main(pytest_args)
    
    if result == 0:
        print("✅ Tous les tests sont passés avec succès !")
    else:
        print("❌ Certains tests ont échoué.")
    
    return result == 0

if __name__ == "__main__":
    # Créer la base de données de test
    Base.metadata.create_all(bind=engine)
    
    try:
        success = run_all_tests()
        exit(0 if success else 1)
    finally:
        # Nettoyer
        if os.path.exists("test.db"):
            os.remove("test.db")

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import Optional, Dict, Any
import re
from datetime import datetime
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class EmailService:
    def __init__(self, smtp_config: Dict[str, Any]):
        """
        Initialise le service email avec la configuration SMTP
        """
        self.host = smtp_config.get('host')
        self.port = smtp_config.get('port', 587)
        self.username = smtp_config.get('username')
        self.password = smtp_config.get('password')
        self.use_tls = smtp_config.get('use_tls', True)
        self.from_email = smtp_config.get('from_email')
        self.from_name = smtp_config.get('from_name', 'CRM System')
    
    def test_connection(self) -> tuple[bool, str]:
        """
        Test la connexion SMTP
        Retourne (success: bool, message: str)
        """
        try:
            if self.use_tls:
                context = ssl.create_default_context()
                with smtplib.SMTP(self.host, self.port) as server:
                    server.starttls(context=context)
                    server.login(self.username, self.password)
            else:
                with smtplib.SMTP(self.host, self.port) as server:
                    server.login(self.username, self.password)
            
            return True, "Connexion SMTP réussie"
        except Exception as e:
            logger.error(f"Erreur de connexion SMTP: {str(e)}")
            return False, f"Erreur de connexion: {str(e)}"
    
    def replace_variables(self, content: str, variables: Dict[str, str]) -> str:
        """
        Remplace les variables dans le contenu du template
        Variables supportées: {prenom}, {nom}, {email}, {date_rdv}, etc.
        """
        for key, value in variables.items():
            placeholder = f"{{{key}}}"
            content = content.replace(placeholder, str(value) if value else "")
        
        return content
    
    def send_email(self, 
                   to_email: str, 
                   subject: str, 
                   content: str, 
                   variables: Optional[Dict[str, str]] = None) -> tuple[bool, str]:
        """
        Envoie un email
        Retourne (success: bool, message: str)
        """
        try:
            # Remplacer les variables si fournies
            if variables:
                subject = self.replace_variables(subject, variables)
                content = self.replace_variables(content, variables)
            
            # Créer le message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email
            
            # Ajouter le contenu (HTML et texte)
            if "<html>" in content.lower() or "<p>" in content.lower():
                # Contenu HTML
                html_part = MIMEText(content, "html", "utf-8")
                message.attach(html_part)
                
                # Créer version texte simple
                text_content = re.sub(r'<[^>]+>', '', content)
                text_part = MIMEText(text_content, "plain", "utf-8")
                message.attach(text_part)
            else:
                # Contenu texte simple
                text_part = MIMEText(content, "plain", "utf-8")
                message.attach(text_part)
            
            # Envoyer l'email
            if self.use_tls:
                context = ssl.create_default_context()
                with smtplib.SMTP(self.host, self.port) as server:
                    server.starttls(context=context)
                    server.login(self.username, self.password)
                    server.sendmail(self.from_email, to_email, message.as_string())
            else:
                with smtplib.SMTP(self.host, self.port) as server:
                    server.login(self.username, self.password)
                    server.sendmail(self.from_email, to_email, message.as_string())
            
            logger.info(f"Email envoyé avec succès à {to_email}")
            return True, "Email envoyé avec succès"
            
        except Exception as e:
            error_msg = f"Erreur lors de l'envoi de l'email: {str(e)}"
            logger.error(error_msg)
            return False, error_msg
    
    def send_bulk_emails(self, 
                        recipients: list, 
                        subject: str, 
                        content: str) -> Dict[str, Any]:
        """
        Envoie des emails en lot
        recipients: liste de dict avec 'email' et optionnellement 'variables'
        Retourne un rapport d'envoi
        """
        results = {
            'success': 0,
            'errors': 0,
            'details': []
        }
        
        for recipient in recipients:
            email = recipient.get('email')
            variables = recipient.get('variables', {})
            
            success, message = self.send_email(email, subject, content, variables)
            
            if success:
                results['success'] += 1
                results['details'].append(f"✓ {email}: {message}")
            else:
                results['errors'] += 1
                results['details'].append(f"✗ {email}: {message}")
        
        return results

def get_default_variables(client_data: Dict[str, Any], 
                         appointment_data: Optional[Dict[str, Any]] = None) -> Dict[str, str]:
    """
    Génère les variables par défaut pour les templates d'email
    """
    variables = {
        'prenom': client_data.get('prenom', ''),
        'nom': client_data.get('nom', ''),
        'email': client_data.get('email', ''),
        'telephone': client_data.get('telephone', ''),
        'adresse': client_data.get('adresse', ''),
        'date_actuelle': datetime.now().strftime('%d/%m/%Y'),
        'heure_actuelle': datetime.now().strftime('%H:%M')
    }
    
    # Ajouter les variables de rendez-vous si disponibles
    if appointment_data:
        if appointment_data.get('date_rdv'):
            rdv_date = appointment_data['date_rdv']
            if isinstance(rdv_date, str):
                variables['date_rdv'] = rdv_date
            else:
                variables['date_rdv'] = rdv_date.strftime('%d/%m/%Y à %H:%M')
        
        variables['titre_rdv'] = appointment_data.get('titre', '')
        variables['description_rdv'] = appointment_data.get('description', '')
    
    return variables

def get_available_variables() -> Dict[str, str]:
    """
    Retourne la liste des variables disponibles avec leur description
    """
    return {
        '{prenom}': 'Prénom du client',
        '{nom}': 'Nom du client',
        '{email}': 'Email du client',
        '{telephone}': 'Téléphone du client',
        '{adresse}': 'Adresse du client',
        '{date_rdv}': 'Date et heure du rendez-vous',
        '{titre_rdv}': 'Titre du rendez-vous',
        '{description_rdv}': 'Description du rendez-vous',
        '{date_actuelle}': 'Date actuelle',
        '{heure_actuelle}': 'Heure actuelle'
    }

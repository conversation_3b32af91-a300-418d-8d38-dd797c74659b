{% extends "base.html" %}

{% block title %}Configuration SMTP - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-gear"></i> Configuration SMTP</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        {% if smtp_config %}
        <button type="button" class="btn btn-outline-info" onclick="testSMTP()">
            <i class="bi bi-check-circle"></i> Tester la Configuration
        </button>
        {% endif %}
    </div>
</div>

<!-- Statut de la configuration -->
<div class="row mb-4">
    <div class="col-md-12">
        {% if smtp_config %}
        <div class="alert alert-success">
            <i class="bi bi-check-circle"></i>
            <strong>Configuration SMTP active</strong><br>
            Serveur: {{ smtp_config.host }}:{{ smtp_config.port }} | 
            Email expéditeur: {{ smtp_config.from_email }}
        </div>
        {% else %}
        <div class="alert alert-warning">
            <i class="bi bi-exclamation-triangle"></i>
            <strong>Aucune configuration SMTP</strong><br>
            Vous devez configurer les paramètres SMTP pour pouvoir envoyer des emails.
        </div>
        {% endif %}
    </div>
</div>

<!-- Formulaire de configuration -->
<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-envelope-gear"></i> Paramètres SMTP</h5>
    </div>
    <div class="card-body">
        <form id="smtpForm">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="host" class="form-label">Serveur SMTP *</label>
                        <input type="text" class="form-control" id="host" name="host" 
                               value="{{ smtp_config.host if smtp_config else '' }}" 
                               placeholder="smtp.gmail.com" required>
                        <div class="form-text">Adresse du serveur SMTP</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="port" class="form-label">Port *</label>
                        <input type="number" class="form-control" id="port" name="port" 
                               value="{{ smtp_config.port if smtp_config else 587 }}" 
                               placeholder="587" required>
                        <div class="form-text">Port du serveur (587 pour TLS, 465 pour SSL)</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="username" class="form-label">Nom d'utilisateur *</label>
                        <input type="text" class="form-control" id="username" name="username" 
                               value="{{ smtp_config.username if smtp_config else '' }}" 
                               placeholder="<EMAIL>" required>
                        <div class="form-text">Nom d'utilisateur pour l'authentification</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe *</label>
                        <input type="password" class="form-control" id="password" name="password" 
                               placeholder="{% if smtp_config %}••••••••{% else %}Mot de passe{% endif %}" 
                               {% if not smtp_config %}required{% endif %}>
                        <div class="form-text">
                            {% if smtp_config %}
                            Laissez vide pour conserver le mot de passe actuel
                            {% else %}
                            Mot de passe ou token d'application
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="from_email" class="form-label">Email expéditeur *</label>
                        <input type="email" class="form-control" id="from_email" name="from_email" 
                               value="{{ smtp_config.from_email if smtp_config else '' }}" 
                               placeholder="<EMAIL>" required>
                        <div class="form-text">Adresse email qui apparaîtra comme expéditeur</div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label for="from_name" class="form-label">Nom expéditeur</label>
                        <input type="text" class="form-control" id="from_name" name="from_name" 
                               value="{{ smtp_config.from_name if smtp_config else 'CRM System' }}" 
                               placeholder="CRM System">
                        <div class="form-text">Nom qui apparaîtra comme expéditeur</div>
                    </div>
                </div>
            </div>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="use_tls" name="use_tls" 
                                   {% if not smtp_config or smtp_config.use_tls %}checked{% endif %}>
                            <label class="form-check-label" for="use_tls">
                                Utiliser TLS/STARTTLS
                            </label>
                            <div class="form-text">Recommandé pour la sécurité (port 587)</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="d-flex gap-2">
                <button type="submit" class="btn btn-primary">
                    <i class="bi bi-save"></i> Sauvegarder
                </button>
                <button type="button" class="btn btn-outline-info" onclick="testSMTPForm()">
                    <i class="bi bi-check-circle"></i> Tester avant de sauvegarder
                </button>
            </div>
        </form>
    </div>
</div>

<!-- Configurations prédéfinies -->
<div class="card mt-4">
    <div class="card-header">
        <h5><i class="bi bi-bookmark"></i> Configurations Prédéfinies</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-4">
                <div class="card border-primary">
                    <div class="card-header bg-primary text-white">
                        <h6 class="mb-0">Gmail</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Serveur:</strong> smtp.gmail.com</p>
                        <p><strong>Port:</strong> 587</p>
                        <p><strong>TLS:</strong> Oui</p>
                        <button class="btn btn-sm btn-outline-primary" onclick="useGmailConfig()">
                            Utiliser cette config
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card border-info">
                    <div class="card-header bg-info text-white">
                        <h6 class="mb-0">Outlook/Hotmail</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Serveur:</strong> smtp-mail.outlook.com</p>
                        <p><strong>Port:</strong> 587</p>
                        <p><strong>TLS:</strong> Oui</p>
                        <button class="btn btn-sm btn-outline-info" onclick="useOutlookConfig()">
                            Utiliser cette config
                        </button>
                    </div>
                </div>
            </div>
            
            <div class="col-md-4">
                <div class="card border-success">
                    <div class="card-header bg-success text-white">
                        <h6 class="mb-0">SendGrid</h6>
                    </div>
                    <div class="card-body">
                        <p><strong>Serveur:</strong> smtp.sendgrid.net</p>
                        <p><strong>Port:</strong> 587</p>
                        <p><strong>TLS:</strong> Oui</p>
                        <button class="btn btn-sm btn-outline-success" onclick="useSendGridConfig()">
                            Utiliser cette config
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Instructions -->
<div class="card mt-4">
    <div class="card-header">
        <h5><i class="bi bi-info-circle"></i> Instructions</h5>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Gmail</h6>
                <ul>
                    <li>Activez l'authentification à 2 facteurs</li>
                    <li>Générez un "mot de passe d'application"</li>
                    <li>Utilisez ce mot de passe au lieu de votre mot de passe habituel</li>
                </ul>
            </div>
            <div class="col-md-6">
                <h6>Outlook/Hotmail</h6>
                <ul>
                    <li>Utilisez votre adresse email complète comme nom d'utilisateur</li>
                    <li>Activez l'authentification moderne si nécessaire</li>
                    <li>Vérifiez que SMTP est activé dans les paramètres</li>
                </ul>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function useGmailConfig() {
    document.getElementById('host').value = 'smtp.gmail.com';
    document.getElementById('port').value = '587';
    document.getElementById('use_tls').checked = true;
}

function useOutlookConfig() {
    document.getElementById('host').value = 'smtp-mail.outlook.com';
    document.getElementById('port').value = '587';
    document.getElementById('use_tls').checked = true;
}

function useSendGridConfig() {
    document.getElementById('host').value = 'smtp.sendgrid.net';
    document.getElementById('port').value = '587';
    document.getElementById('use_tls').checked = true;
    document.getElementById('username').value = 'apikey';
}

function testSMTP() {
    fetch('/api/smtp/test', {
        method: 'POST'
    }).then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('✅ Test réussi: ' + data.message);
        } else {
            alert('❌ Test échoué: ' + data.message);
        }
    }).catch(error => {
        alert('❌ Erreur lors du test: ' + error);
    });
}

function testSMTPForm() {
    const formData = new FormData(document.getElementById('smtpForm'));
    const data = Object.fromEntries(formData.entries());
    data.use_tls = document.getElementById('use_tls').checked;
    
    // Test temporaire avec les données du formulaire
    fetch('/api/smtp/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => response.json())
    .then(result => {
        if (result.message.includes('succès')) {
            alert('✅ Configuration testée et sauvegardée avec succès!');
            location.reload();
        } else {
            alert('❌ Erreur: ' + result.message);
        }
    }).catch(error => {
        alert('❌ Erreur lors du test: ' + error);
    });
}

// Gestion du formulaire
document.getElementById('smtpForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    data.use_tls = document.getElementById('use_tls').checked;
    
    // Si pas de mot de passe et config existante, ne pas l'inclure
    {% if smtp_config %}
    if (!data.password) {
        delete data.password;
    }
    {% endif %}
    
    fetch('/api/smtp/config', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => response.json())
    .then(result => {
        if (result.message.includes('succès')) {
            alert('✅ Configuration sauvegardée avec succès!');
            location.reload();
        } else {
            alert('❌ Erreur: ' + result.message);
        }
    }).catch(error => {
        alert('❌ Erreur lors de la sauvegarde: ' + error);
    });
});
</script>
{% endblock %}

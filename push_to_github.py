#!/usr/bin/env python3
"""
BINANCE CRM - Script de Push vers GitHub
Script automatisé pour préparer et pousser le code vers GitHub
"""

import os
import subprocess
import sys
import json
from datetime import datetime

class GitHubPusher:
    def __init__(self):
        self.project_name = "binance-crm"
        self.commit_message = f"🚀 Binance CRM - Version complète optimisée ({datetime.now().strftime('%Y-%m-%d')})"
        
    def check_git_installed(self):
        """Vérifier que Git est installé"""
        try:
            result = subprocess.run(['git', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Git installé: {result.stdout.strip()}")
                return True
            else:
                print("❌ Git n'est pas installé")
                return False
        except FileNotFoundError:
            print("❌ Git n'est pas installé ou pas dans le PATH")
            return False
    
    def init_git_repo(self):
        """Initialiser le repository Git"""
        print("🔧 Initialisation du repository Git...")
        
        try:
            # Vérifier si .git existe déjà
            if os.path.exists('.git'):
                print("   ✅ Repository Git déjà initialisé")
                return True
            
            # Initialiser Git
            subprocess.run(['git', 'init'], check=True)
            print("   ✅ Repository Git initialisé")
            
            # Configurer Git (optionnel)
            try:
                subprocess.run(['git', 'config', 'user.name', 'Binance CRM Developer'], check=True)
                subprocess.run(['git', 'config', 'user.email', '<EMAIL>'], check=True)
                print("   ✅ Configuration Git définie")
            except:
                print("   ⚠️ Configuration Git non définie (utilisez git config)")
            
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Erreur lors de l'initialisation: {e}")
            return False
    
    def create_gitignore(self):
        """Créer ou vérifier le fichier .gitignore"""
        print("📝 Vérification du fichier .gitignore...")
        
        if os.path.exists('.gitignore'):
            print("   ✅ Fichier .gitignore déjà présent")
            return True
        
        gitignore_content = """# BINANCE CRM - Fichiers à ignorer par Git

# Base de données (optionnel - vous pouvez inclure une DB vide)
# binance_crm.db
*.db-journal
*.db-wal
*.db-shm

# Fichiers Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environnements virtuels
.env
.venv
env/
venv/
ENV/

# IDEs
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Sauvegardes
backups/
*.backup

# Fichiers temporaires
*.tmp
*.temp
temp/

# Fichiers de test
*_test_results.json
*_benchmark.json
*_report.json
"""
        
        try:
            with open('.gitignore', 'w', encoding='utf-8') as f:
                f.write(gitignore_content)
            print("   ✅ Fichier .gitignore créé")
            return True
        except Exception as e:
            print(f"   ❌ Erreur création .gitignore: {e}")
            return False
    
    def check_files_ready(self):
        """Vérifier que tous les fichiers nécessaires sont présents"""
        print("📁 Vérification des fichiers...")
        
        required_files = [
            'start_production.py',
            'requirements.txt',
            'railway.toml',
            'Procfile',
            'database_server.py',
            'email_server.py',
            'pdf_server.py',
            'index.html',
            'clients.html',
            'dashboard.html'
        ]
        
        missing_files = []
        for file in required_files:
            if os.path.exists(file):
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} - MANQUANT")
                missing_files.append(file)
        
        if missing_files:
            print(f"\n⚠️ Fichiers manquants: {', '.join(missing_files)}")
            return False
        else:
            print("   ✅ Tous les fichiers requis sont présents")
            return True
    
    def add_and_commit(self):
        """Ajouter et committer les fichiers"""
        print("📦 Ajout et commit des fichiers...")
        
        try:
            # Ajouter tous les fichiers
            subprocess.run(['git', 'add', '.'], check=True)
            print("   ✅ Fichiers ajoutés au staging")
            
            # Vérifier s'il y a des changements à committer
            result = subprocess.run(['git', 'status', '--porcelain'], capture_output=True, text=True)
            if not result.stdout.strip():
                print("   ⚠️ Aucun changement à committer")
                return True
            
            # Committer
            subprocess.run(['git', 'commit', '-m', self.commit_message], check=True)
            print(f"   ✅ Commit créé: {self.commit_message}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Erreur lors du commit: {e}")
            return False
    
    def setup_remote_origin(self):
        """Configurer l'origine remote"""
        print("🔗 Configuration de l'origine remote...")
        
        # Demander l'URL du repository GitHub
        print("\n📋 CONFIGURATION DU REPOSITORY GITHUB")
        print("Vous devez d'abord créer un repository sur GitHub:")
        print("1. Aller sur https://github.com")
        print("2. Cliquer sur 'New repository'")
        print("3. Nommer le repository 'binance-crm'")
        print("4. Ne pas initialiser avec README (nous en avons déjà un)")
        print("5. Copier l'URL du repository")
        
        github_url = input("\n🔗 Entrez l'URL de votre repository GitHub (ex: https://github.com/username/binance-crm.git): ").strip()
        
        if not github_url:
            print("❌ URL non fournie")
            return False
        
        try:
            # Vérifier si origin existe déjà
            result = subprocess.run(['git', 'remote', 'get-url', 'origin'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"   ⚠️ Origin déjà configuré: {result.stdout.strip()}")
                response = input("   Voulez-vous le remplacer? (y/N): ").lower()
                if response in ['y', 'yes', 'oui']:
                    subprocess.run(['git', 'remote', 'remove', 'origin'], check=True)
                else:
                    return True
            
            # Ajouter l'origin
            subprocess.run(['git', 'remote', 'add', 'origin', github_url], check=True)
            print(f"   ✅ Origin configuré: {github_url}")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Erreur configuration origin: {e}")
            return False
    
    def push_to_github(self):
        """Pousser vers GitHub"""
        print("🚀 Push vers GitHub...")
        
        try:
            # Créer et pousser la branche main
            subprocess.run(['git', 'branch', '-M', 'main'], check=True)
            print("   ✅ Branche main créée")
            
            # Push vers GitHub
            result = subprocess.run(['git', 'push', '-u', 'origin', 'main'], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("   ✅ Code poussé vers GitHub avec succès!")
                print(f"   🔗 Votre repository: {result.stderr}")
                return True
            else:
                print(f"   ❌ Erreur lors du push: {result.stderr}")
                
                # Si c'est un problème d'authentification
                if "Authentication failed" in result.stderr or "Permission denied" in result.stderr:
                    print("\n🔐 PROBLÈME D'AUTHENTIFICATION")
                    print("Solutions possibles:")
                    print("1. Utiliser un Personal Access Token au lieu du mot de passe")
                    print("2. Configurer SSH keys")
                    print("3. Utiliser GitHub CLI: gh auth login")
                
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"   ❌ Erreur lors du push: {e}")
            return False
    
    def create_deployment_info(self):
        """Créer un fichier d'information de déploiement"""
        deployment_info = {
            "project_name": "Binance CRM",
            "version": "2.0",
            "deployment_date": datetime.now().isoformat(),
            "deployment_platforms": {
                "railway": {
                    "url": "https://railway.app/new/template",
                    "auto_deploy": True,
                    "config_file": "railway.toml"
                },
                "render": {
                    "url": "https://render.com",
                    "auto_deploy": True,
                    "config_file": "Procfile"
                },
                "pythonanywhere": {
                    "url": "https://pythonanywhere.com",
                    "auto_deploy": False,
                    "manual_upload": True
                }
            },
            "features": [
                "Gestion complète des clients",
                "Système d'emails intégré",
                "Dashboard avec statistiques",
                "Génération PDF automatique",
                "Import/Export CSV",
                "Optimisations de performance",
                "Cache intelligent",
                "Compression gzip",
                "API REST complète"
            ],
            "performance": {
                "response_time": "< 100ms",
                "concurrent_users": "100+",
                "database_optimizations": "15+ indexes",
                "compression": "60-80% reduction"
            }
        }
        
        try:
            with open('deployment_info.json', 'w', encoding='utf-8') as f:
                json.dump(deployment_info, f, indent=2, ensure_ascii=False)
            print("📄 Fichier deployment_info.json créé")
            return True
        except Exception as e:
            print(f"❌ Erreur création deployment_info.json: {e}")
            return False
    
    def run_push_process(self):
        """Exécuter le processus complet de push"""
        print("🚀 PUSH BINANCE CRM VERS GITHUB")
        print("="*50)
        
        # Vérifications préliminaires
        if not self.check_git_installed():
            print("\n❌ Git doit être installé pour continuer")
            print("Téléchargez Git depuis: https://git-scm.com/")
            return False
        
        if not self.check_files_ready():
            print("\n❌ Fichiers manquants. Assurez-vous que tous les fichiers sont présents")
            return False
        
        # Processus Git
        steps = [
            ("Initialisation Git", self.init_git_repo),
            ("Création .gitignore", self.create_gitignore),
            ("Création info déploiement", self.create_deployment_info),
            ("Ajout et commit", self.add_and_commit),
            ("Configuration remote", self.setup_remote_origin),
            ("Push vers GitHub", self.push_to_github)
        ]
        
        for step_name, step_function in steps:
            print(f"\n{step_name}...")
            if not step_function():
                print(f"\n❌ Échec à l'étape: {step_name}")
                return False
        
        self.print_success_message()
        return True
    
    def print_success_message(self):
        """Afficher le message de succès"""
        print("\n" + "="*50)
        print("🎉 SUCCÈS - CODE POUSSÉ VERS GITHUB!")
        print("="*50)
        
        print("\n📋 ÉTAPES SUIVANTES:")
        print("1. ✅ Votre code est maintenant sur GitHub")
        print("2. 🚀 Déployez sur Railway:")
        print("   - Aller sur https://railway.app")
        print("   - Se connecter avec GitHub")
        print("   - Sélectionner votre repository 'binance-crm'")
        print("   - Déploiement automatique!")
        
        print("\n🌐 ALTERNATIVES DE DÉPLOIEMENT:")
        print("   - Render: https://render.com")
        print("   - PythonAnywhere: https://pythonanywhere.com")
        
        print("\n📚 DOCUMENTATION:")
        print("   - Guide de déploiement: DEPLOYMENT_GUIDE.md")
        print("   - Référence API: DATABASE_ACTIONS_REFERENCE.md")
        print("   - Rapport d'optimisations: OPTIMIZATIONS_REPORT.md")
        
        print("\n🏆 VOTRE CRM EST PRÊT POUR LA PRODUCTION!")

if __name__ == "__main__":
    pusher = GitHubPusher()
    
    print("🔍 PRÉPARATION DU PUSH VERS GITHUB")
    print("Ce script va préparer et pousser votre Binance CRM vers GitHub")
    
    response = input("\nContinuer? (Y/n): ").lower()
    if response in ['', 'y', 'yes', 'oui']:
        pusher.run_push_process()
    else:
        print("❌ Opération annulée")

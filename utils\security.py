"""
Module de sécurité pour l'application CRM
Contient les fonctions de validation, sanitisation et sécurité
"""

import re
import html
import bleach
from typing import Optional, Dict, Any
import logging
from datetime import datetime, timedelta
from functools import wraps
from fastapi import HTTPException, Request
import hashlib
import secrets

logger = logging.getLogger(__name__)

# Configuration de sécurité
ALLOWED_HTML_TAGS = ['p', 'br', 'strong', 'em', 'u', 'ol', 'ul', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6']
ALLOWED_HTML_ATTRIBUTES = {}
MAX_LOGIN_ATTEMPTS = 5
LOCKOUT_DURATION = 900  # 15 minutes en secondes

# Stockage temporaire des tentatives de connexion (en production, utiliser Redis)
login_attempts = {}

def sanitize_html(content: str) -> str:
    """
    Nettoie le contenu HTML pour éviter les attaques XSS
    """
    if not content:
        return ""
    
    # Nettoyer avec bleach
    clean_content = bleach.clean(
        content,
        tags=ALLOWED_HTML_TAGS,
        attributes=ALLOWED_HTML_ATTRIBUTES,
        strip=True
    )
    
    return clean_content

def sanitize_input(input_str: str) -> str:
    """
    Nettoie les entrées utilisateur
    """
    if not input_str:
        return ""
    
    # Échapper les caractères HTML
    sanitized = html.escape(input_str.strip())
    
    # Limiter la longueur
    if len(sanitized) > 10000:  # 10KB max
        sanitized = sanitized[:10000]
    
    return sanitized

def validate_email(email: str) -> bool:
    """
    Valide le format d'un email
    """
    if not email:
        return False
    
    pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    return re.match(pattern, email) is not None

def validate_phone(phone: str) -> bool:
    """
    Valide le format d'un numéro de téléphone
    """
    if not phone:
        return False
    
    # Nettoyer le numéro (enlever espaces, points, tirets)
    clean_phone = re.sub(r'[\s\-\.\(\)]', '', phone)
    
    # Vérifier le format (10 chiffres minimum, 15 maximum)
    pattern = r'^[\+]?[0-9]{8,15}$'
    return re.match(pattern, clean_phone) is not None

def validate_password_strength(password: str) -> tuple[bool, str]:
    """
    Valide la force d'un mot de passe
    Retourne (is_valid, message)
    """
    if not password:
        return False, "Le mot de passe est requis"
    
    if len(password) < 6:
        return False, "Le mot de passe doit contenir au moins 6 caractères"
    
    if len(password) > 128:
        return False, "Le mot de passe est trop long (maximum 128 caractères)"
    
    # Vérifications optionnelles pour un mot de passe fort
    has_upper = any(c.isupper() for c in password)
    has_lower = any(c.islower() for c in password)
    has_digit = any(c.isdigit() for c in password)
    has_special = any(c in "!@#$%^&*()_+-=[]{}|;:,.<>?" for c in password)
    
    strength_score = sum([has_upper, has_lower, has_digit, has_special])
    
    if strength_score < 2:
        return False, "Le mot de passe doit contenir au moins 2 types de caractères (majuscules, minuscules, chiffres, caractères spéciaux)"
    
    return True, "Mot de passe valide"

def check_rate_limit(identifier: str, max_attempts: int = 10, window_minutes: int = 60) -> bool:
    """
    Vérifie si l'utilisateur n'a pas dépassé la limite de requêtes
    """
    now = datetime.now()
    window_start = now - timedelta(minutes=window_minutes)
    
    # Nettoyer les anciennes tentatives
    if identifier in login_attempts:
        login_attempts[identifier] = [
            attempt for attempt in login_attempts[identifier]
            if attempt > window_start
        ]
    
    # Vérifier le nombre de tentatives
    attempts_count = len(login_attempts.get(identifier, []))
    
    if attempts_count >= max_attempts:
        logger.warning(f"Rate limit exceeded for {identifier}: {attempts_count} attempts")
        return False
    
    return True

def record_login_attempt(identifier: str, success: bool = False):
    """
    Enregistre une tentative de connexion
    """
    now = datetime.now()
    
    if identifier not in login_attempts:
        login_attempts[identifier] = []
    
    login_attempts[identifier].append(now)
    
    # Si connexion réussie, nettoyer les tentatives
    if success:
        login_attempts[identifier] = []
    
    logger.info(f"Login attempt recorded for {identifier}: {'success' if success else 'failed'}")

def is_account_locked(identifier: str) -> tuple[bool, Optional[datetime]]:
    """
    Vérifie si un compte est verrouillé
    Retourne (is_locked, unlock_time)
    """
    if identifier not in login_attempts:
        return False, None
    
    now = datetime.now()
    recent_attempts = [
        attempt for attempt in login_attempts[identifier]
        if attempt > now - timedelta(seconds=LOCKOUT_DURATION)
    ]
    
    if len(recent_attempts) >= MAX_LOGIN_ATTEMPTS:
        # Compte verrouillé, calculer le temps de déverrouillage
        last_attempt = max(recent_attempts)
        unlock_time = last_attempt + timedelta(seconds=LOCKOUT_DURATION)
        
        if now < unlock_time:
            return True, unlock_time
        else:
            # Le verrouillage a expiré, nettoyer
            login_attempts[identifier] = []
            return False, None
    
    return False, None

def generate_csrf_token() -> str:
    """
    Génère un token CSRF sécurisé
    """
    return secrets.token_urlsafe(32)

def validate_csrf_token(token: str, session_token: str) -> bool:
    """
    Valide un token CSRF
    """
    if not token or not session_token:
        return False
    
    return secrets.compare_digest(token, session_token)

def hash_sensitive_data(data: str) -> str:
    """
    Hash des données sensibles pour les logs
    """
    return hashlib.sha256(data.encode()).hexdigest()[:8]

def validate_file_upload(filename: str, content: bytes, max_size: int = 10 * 1024 * 1024) -> tuple[bool, str]:
    """
    Valide un fichier uploadé
    """
    if not filename:
        return False, "Nom de fichier manquant"
    
    # Vérifier l'extension
    allowed_extensions = {'.csv', '.xlsx', '.xls'}
    file_ext = '.' + filename.split('.')[-1].lower() if '.' in filename else ''
    
    if file_ext not in allowed_extensions:
        return False, f"Extension non autorisée. Extensions autorisées: {', '.join(allowed_extensions)}"
    
    # Vérifier la taille
    if len(content) > max_size:
        return False, f"Fichier trop volumineux. Taille maximum: {max_size // (1024*1024)}MB"
    
    # Vérifier le contenu (basique)
    if file_ext == '.csv':
        try:
            content.decode('utf-8')
        except UnicodeDecodeError:
            try:
                content.decode('latin-1')
            except UnicodeDecodeError:
                return False, "Encodage de fichier non supporté"
    
    return True, "Fichier valide"

def log_security_event(event_type: str, details: Dict[str, Any], request: Optional[Request] = None):
    """
    Log un événement de sécurité
    """
    log_data = {
        'event_type': event_type,
        'timestamp': datetime.now().isoformat(),
        'details': details
    }
    
    if request:
        log_data['ip'] = request.client.host if request.client else 'unknown'
        log_data['user_agent'] = request.headers.get('user-agent', 'unknown')
    
    logger.warning(f"Security event: {log_data}")

def require_https(func):
    """
    Décorateur pour forcer HTTPS en production
    """
    @wraps(func)
    async def wrapper(request: Request, *args, **kwargs):
        if request.url.scheme != 'https' and not request.url.hostname in ['localhost', '127.0.0.1']:
            # En production, rediriger vers HTTPS
            https_url = request.url.replace(scheme='https')
            raise HTTPException(status_code=301, headers={'Location': str(https_url)})
        
        return await func(request, *args, **kwargs)
    
    return wrapper

class SecurityHeaders:
    """
    Middleware pour ajouter des headers de sécurité
    """
    
    @staticmethod
    def get_security_headers() -> Dict[str, str]:
        return {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Strict-Transport-Security': 'max-age=31536000; includeSubDomains',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; img-src 'self' data:; font-src 'self' https://cdn.jsdelivr.net;",
            'Referrer-Policy': 'strict-origin-when-cross-origin'
        }

def clean_filename(filename: str) -> str:
    """
    Nettoie un nom de fichier pour éviter les attaques de traversée de répertoire
    """
    if not filename:
        return "file"
    
    # Enlever les caractères dangereux
    clean_name = re.sub(r'[^\w\-_\.]', '_', filename)
    
    # Enlever les points multiples
    clean_name = re.sub(r'\.+', '.', clean_name)
    
    # Limiter la longueur
    if len(clean_name) > 255:
        name, ext = clean_name.rsplit('.', 1) if '.' in clean_name else (clean_name, '')
        clean_name = name[:250] + ('.' + ext if ext else '')
    
    return clean_name

def validate_date_range(date_debut: Optional[datetime], date_fin: Optional[datetime]) -> tuple[bool, str]:
    """
    Valide une plage de dates
    """
    if not date_debut and not date_fin:
        return True, "OK"
    
    if date_debut and date_fin:
        if date_debut > date_fin:
            return False, "La date de début doit être antérieure à la date de fin"
        
        # Vérifier que la plage n'est pas trop large (max 2 ans)
        if (date_fin - date_debut).days > 730:
            return False, "La plage de dates ne peut pas dépasser 2 ans"
    
    # Vérifier que les dates ne sont pas trop dans le futur
    now = datetime.now()
    max_future = now + timedelta(days=365)
    
    if date_debut and date_debut > max_future:
        return False, "La date de début ne peut pas être si loin dans le futur"
    
    if date_fin and date_fin > max_future:
        return False, "La date de fin ne peut pas être si loin dans le futur"
    
    return True, "OK"

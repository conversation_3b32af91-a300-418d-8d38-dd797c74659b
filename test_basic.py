#!/usr/bin/env python3
"""
Test serveur HTTP basique
"""

from http.server import HTTPServer, BaseHTTPRequestHandler
import webbrowser
import threading
import time

class TestHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        html = '''<!DOCTYPE html>
<html>
<head><title>Test</title></head>
<body>
<h1>SERVEUR FONCTIONNE !</h1>
<p>Si vous voyez cette page, le serveur HTTP fonctionne.</p>
</body>
</html>'''
        
        self.send_response(200)
        self.send_header('Content-Type', 'text/html')
        self.end_headers()
        self.wfile.write(html.encode())

def run_server():
    server = HTTPServer(('localhost', 8000), TestHandler)
    print("Serveur demarre sur http://localhost:8000")
    
    # Ouvrir navigateur
    def open_browser():
        time.sleep(1)
        webbrowser.open('http://localhost:8000')
    
    threading.Thread(target=open_browser, daemon=True).start()
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("Serveur arrete")
        server.shutdown()

if __name__ == '__main__':
    run_server()

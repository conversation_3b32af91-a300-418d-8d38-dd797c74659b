<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapports - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }

        body {
            background-color: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }

        .navbar {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .navbar-brand, .navbar-nav .nav-link {
            color: #000 !important;
            font-weight: 600;
        }

        .card {
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border: none;
            margin-bottom: 20px;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            border: none;
            color: #000;
            font-weight: 600;
        }

        .stat-card {
            border-left: 4px solid var(--binance-yellow);
            transition: transform 0.2s;
        }

        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .chart-container {
            position: relative;
            height: 400px;
            margin: 20px 0;
        }

        .export-btn {
            background: var(--binance-yellow);
            border: none;
            color: #000;
            font-weight: bold;
        }

        .export-btn:hover {
            background: var(--binance-gold);
            color: #000;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            background: #fff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dee2e6;
        }
        
        .progress-custom {
            height: 25px;
            border-radius: 12px;
        }
        
        .kpi-card {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            color: #000;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .report-item {
            border-left: 4px solid var(--binance-yellow);
            padding-left: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>

            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="bi bi-house"></i> Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="clients.html">
                            <i class="bi bi-people"></i> Clients
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="vendeurs.html">
                            <i class="bi bi-person-badge"></i> Vendeurs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="emails.html">
                            <i class="bi bi-envelope"></i> Emails
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="admin_config.html">
                            <i class="bi bi-gear"></i> Configuration
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="reports.html">
                            <i class="bi bi-graph-up"></i> Rapports
                        </a>
                    </li>
                </ul>
                <div class="navbar-nav">
                    <a class="nav-link" href="#" onclick="logout()">
                        <i class="bi bi-box-arrow-right"></i> Déconnexion
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <h1><i class="bi bi-graph-up"></i> Rapports et Analyses</h1>
                <p class="text-muted">Tableau de bord analytique et génération de rapports</p>
            </div>
        </div>

        <!-- Filtres -->
        <div class="card mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <label class="form-label">Période</label>
                        <select class="form-select" id="periodFilter" onchange="updateReports()">
                            <option value="7">7 derniers jours</option>
                            <option value="30" selected>30 derniers jours</option>
                            <option value="90">3 derniers mois</option>
                            <option value="365">12 derniers mois</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Vendeur</label>
                        <select class="form-select" id="vendeurFilter" onchange="updateReports()">
                            <option value="">Tous les vendeurs</option>
                            <option value="1">Marie Martin</option>
                            <option value="2">Pierre Durand</option>
                            <option value="3">Sophie Bernard</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <label class="form-label">Statut</label>
                        <select class="form-select" id="statusFilter" onchange="updateReports()">
                            <option value="">Tous les statuts</option>
                            <option value="prospect">Prospects</option>
                            <option value="client">Clients</option>
                            <option value="actif">Actifs</option>
                            <option value="inactif">Inactifs</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <div class="d-flex gap-2 mt-4">
                            <button class="btn export-btn" onclick="exportReport('pdf')">
                                <i class="bi bi-file-pdf"></i> PDF
                            </button>
                            <button class="btn export-btn" onclick="exportReport('excel')">
                                <i class="bi bi-file-excel"></i> Excel
                            </button>
                            <button class="btn btn-outline-secondary" onclick="resetFilters()">
                                <i class="bi bi-arrow-clockwise"></i> Reset
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Statistiques principales -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-people-fill" style="font-size: 2rem; color: var(--binance-yellow);"></i>
                        <h3 class="mt-2" id="totalClients">0</h3>
                        <p class="text-muted mb-0">Total Clients</p>
                        <small class="text-success" id="clientsGrowth">+0%</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-person-plus-fill" style="font-size: 2rem; color: #28a745;"></i>
                        <h3 class="mt-2" id="newClients">0</h3>
                        <p class="text-muted mb-0">Nouveaux Clients</p>
                        <small class="text-info" id="newClientsGrowth">Cette période</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-graph-up-arrow" style="font-size: 2rem; color: #17a2b8;"></i>
                        <h3 class="mt-2" id="conversionRate">0%</h3>
                        <p class="text-muted mb-0">Taux Conversion</p>
                        <small class="text-primary" id="conversionTrend">Prospect → Client</small>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stat-card">
                    <div class="card-body text-center">
                        <i class="bi bi-envelope-check" style="font-size: 2rem; color: #6f42c1;"></i>
                        <h3 class="mt-2" id="emailsSent">0</h3>
                        <p class="text-muted mb-0">Emails Envoyés</p>
                        <small class="text-warning" id="emailsGrowth">Cette période</small>
                    </div>
                </div>
            </div>
        </div>

        <!-- Graphiques -->
        <div class="row">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); color: #000;">
                        <h5><i class="bi bi-graph-up"></i> Évolution des Clients</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="clientsChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-4">
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); color: #000;">
                        <h5><i class="bi bi-pie-chart"></i> Répartition par Statut</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="statusChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-4">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); color: #000;">
                        <h5><i class="bi bi-person-badge"></i> Performance Vendeurs</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="vendeurChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header" style="background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); color: #000;">
                        <h5><i class="bi bi-geo-alt"></i> Répartition Géographique</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container">
                            <canvas id="geoChart"></canvas>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Tableau détaillé -->
        <div class="card mt-4">
            <div class="card-header d-flex justify-content-between align-items-center" style="background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); color: #000;">
                <h5><i class="bi bi-table"></i> Rapport Détaillé</h5>
                <div>
                    <button class="btn btn-sm btn-dark" onclick="exportTable()">
                        <i class="bi bi-download"></i> Exporter
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Statut</th>
                                <th>Vendeur</th>
                                <th>Ville</th>
                                <th>Date Création</th>
                                <th>Dernière Activité</th>
                            </tr>
                        </thead>
                        <tbody id="detailedReportTable">
                            <!-- Données chargées dynamiquement -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <!-- Notification Container -->
    <div id="notificationContainer" style="position: fixed; top: 20px; right: 20px; z-index: 1050;"></div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Variables globales
        let clientsData = [];
        let chartsInitialized = false;
        let charts = {};

        // Initialisation
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadReportsData();
        });

        function checkAuth() {
            const user = sessionStorage.getItem('user');
            if (!user) {
                window.location.href = 'login.html';
                return;
            }
        }

        function logout() {
            sessionStorage.removeItem('user');
            window.location.href = 'login.html';
        }

        async function loadReportsData() {
            try {
                // Charger les données clients
                const response = await fetch('http://localhost:8000/api/clients');
                if (response.ok) {
                    const result = await response.json();
                    clientsData = result.data || [];
                } else {
                    // Fallback avec données localStorage
                    clientsData = JSON.parse(localStorage.getItem('binance_crm_clients') || '[]');
                }

                updateStatistics();
                initializeCharts();
                updateDetailedTable();

            } catch (error) {
                console.error('Erreur lors du chargement des données:', error);
                // Utiliser les données localStorage en fallback
                clientsData = JSON.parse(localStorage.getItem('binance_crm_clients') || '[]');
                updateStatistics();
                initializeCharts();
                updateDetailedTable();
            }
        }

        function updateStatistics() {
            const period = parseInt(document.getElementById('periodFilter').value);
            const vendeurFilter = document.getElementById('vendeurFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            // Filtrer les données
            let filteredData = clientsData.filter(client => {
                let include = true;

                if (vendeurFilter && client.vendeur !== getVendeurName(vendeurFilter)) {
                    include = false;
                }

                if (statusFilter && client.statut !== statusFilter) {
                    include = false;
                }

                return include;
            });

            // Calculer les statistiques
            const totalClients = filteredData.length;
            const newClients = filteredData.filter(client => {
                if (!client.dateCreation) return false;
                const creationDate = new Date(client.dateCreation);
                const cutoffDate = new Date();
                cutoffDate.setDate(cutoffDate.getDate() - period);
                return creationDate >= cutoffDate;
            }).length;

            const prospects = filteredData.filter(c => c.statut === 'prospect').length;
            const clients = filteredData.filter(c => c.statut === 'client').length;
            const conversionRate = prospects > 0 ? Math.round((clients / (prospects + clients)) * 100) : 0;

            // Mettre à jour l'affichage
            document.getElementById('totalClients').textContent = totalClients;
            document.getElementById('newClients').textContent = newClients;
            document.getElementById('conversionRate').textContent = conversionRate + '%';
            document.getElementById('emailsSent').textContent = Math.floor(totalClients * 2.3); // Simulation

            // Calculer les tendances (simulation)
            const growth = Math.floor(Math.random() * 20) - 5; // -5% à +15%
            document.getElementById('clientsGrowth').textContent = (growth >= 0 ? '+' : '') + growth + '%';
            document.getElementById('clientsGrowth').className = growth >= 0 ? 'text-success' : 'text-danger';
        }

        function getVendeurName(id) {
            const vendeurs = {
                '1': 'Marie Martin',
                '2': 'Pierre Durand',
                '3': 'Sophie Bernard'
            };
            return vendeurs[id] || '';
        }

        function initializeCharts() {
            if (chartsInitialized) {
                updateCharts();
                return;
            }

            // Graphique évolution clients
            const clientsCtx = document.getElementById('clientsChart').getContext('2d');
            charts.clients = new Chart(clientsCtx, {
                type: 'line',
                data: {
                    labels: getLast30Days(),
                    datasets: [{
                        label: 'Nouveaux Clients',
                        data: generateClientEvolutionData(),
                        borderColor: '#f1c232',
                        backgroundColor: 'rgba(241, 194, 50, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });

            // Graphique répartition statuts
            const statusCtx = document.getElementById('statusChart').getContext('2d');
            const statusData = getStatusDistribution();
            charts.status = new Chart(statusCtx, {
                type: 'doughnut',
                data: {
                    labels: ['Prospects', 'Clients', 'Actifs', 'Inactifs'],
                    datasets: [{
                        data: [statusData.prospect, statusData.client, statusData.actif, statusData.inactif],
                        backgroundColor: ['#ffc107', '#28a745', '#17a2b8', '#6c757d']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });

            // Graphique performance vendeurs
            const vendeurCtx = document.getElementById('vendeurChart').getContext('2d');
            const vendeurData = getVendeurPerformance();
            charts.vendeur = new Chart(vendeurCtx, {
                type: 'bar',
                data: {
                    labels: vendeurData.labels,
                    datasets: [{
                        label: 'Clients',
                        data: vendeurData.data,
                        backgroundColor: '#f1c232'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            // Graphique répartition géographique
            const geoCtx = document.getElementById('geoChart').getContext('2d');
            const geoData = getGeographicDistribution();
            charts.geo = new Chart(geoCtx, {
                type: 'bar',
                data: {
                    labels: geoData.labels,
                    datasets: [{
                        label: 'Clients par ville',
                        data: geoData.data,
                        backgroundColor: ['#f1c232', '#fcd535', '#28a745', '#17a2b8', '#6f42c1']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });

            chartsInitialized = true;
        }

        function updateCharts() {
            if (charts.status) {
                const statusData = getStatusDistribution();
                charts.status.data.datasets[0].data = [statusData.prospect, statusData.client, statusData.actif, statusData.inactif];
                charts.status.update();
            }

            if (charts.vendeur) {
                const vendeurData = getVendeurPerformance();
                charts.vendeur.data.labels = vendeurData.labels;
                charts.vendeur.data.datasets[0].data = vendeurData.data;
                charts.vendeur.update();
            }

            if (charts.geo) {
                const geoData = getGeographicDistribution();
                charts.geo.data.labels = geoData.labels;
                charts.geo.data.datasets[0].data = geoData.data;
                charts.geo.update();
            }
        }

        function getLast30Days() {
            const days = [];
            for (let i = 29; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                days.push(date.toLocaleDateString('fr-FR', { day: '2-digit', month: '2-digit' }));
            }
            return days;
        }

        function generateClientEvolutionData() {
            const data = [];
            for (let i = 0; i < 30; i++) {
                data.push(Math.floor(Math.random() * 5) + 1);
            }
            return data;
        }

        function getStatusDistribution() {
            const distribution = {
                prospect: 0,
                client: 0,
                actif: 0,
                inactif: 0
            };

            clientsData.forEach(client => {
                const status = client.statut || 'prospect';
                if (distribution.hasOwnProperty(status)) {
                    distribution[status]++;
                }
            });

            return distribution;
        }

        function getVendeurPerformance() {
            const performance = {};

            clientsData.forEach(client => {
                const vendeur = client.vendeur || 'Non attribué';
                performance[vendeur] = (performance[vendeur] || 0) + 1;
            });

            return {
                labels: Object.keys(performance),
                data: Object.values(performance)
            };
        }

        function getGeographicDistribution() {
            const distribution = {};

            clientsData.forEach(client => {
                const ville = client.ville || 'Non renseigné';
                distribution[ville] = (distribution[ville] || 0) + 1;
            });

            const sorted = Object.entries(distribution)
                .sort(([,a], [,b]) => b - a)
                .slice(0, 5);

            return {
                labels: sorted.map(([ville]) => ville),
                data: sorted.map(([, count]) => count)
            };
        }

        function updateDetailedTable() {
            const tbody = document.getElementById('detailedReportTable');
            tbody.innerHTML = '';

            const vendeurFilter = document.getElementById('vendeurFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filteredData = clientsData.filter(client => {
                let include = true;

                if (vendeurFilter && client.vendeur !== getVendeurName(vendeurFilter)) {
                    include = false;
                }

                if (statusFilter && client.statut !== statusFilter) {
                    include = false;
                }

                return include;
            });

            filteredData.slice(0, 50).forEach(client => {
                const row = `
                    <tr>
                        <td>
                            <strong>${(client.prenom || '') + ' ' + (client.nom || '')}</strong><br>
                            <small class="text-muted">${client.email || 'N/A'}</small>
                        </td>
                        <td>
                            <span class="badge bg-${getStatusColor(client.statut)}">${client.statut || 'prospect'}</span>
                        </td>
                        <td>${client.vendeur || 'Non attribué'}</td>
                        <td>${client.ville || 'N/A'}</td>
                        <td>${client.dateCreation ? new Date(client.dateCreation).toLocaleDateString('fr-FR') : 'N/A'}</td>
                        <td>${client.dateModification ? new Date(client.dateModification).toLocaleDateString('fr-FR') : client.dateCreation ? new Date(client.dateCreation).toLocaleDateString('fr-FR') : 'N/A'}</td>
                    </tr>
                `;
                tbody.innerHTML += row;
            });
        }

        function getStatusColor(status) {
            const colors = {
                'prospect': 'warning',
                'client': 'success',
                'actif': 'primary',
                'inactif': 'secondary'
            };
            return colors[status] || 'secondary';
        }

        function updateReports() {
            updateStatistics();
            updateCharts();
            updateDetailedTable();
        }

        function resetFilters() {
            document.getElementById('periodFilter').value = '30';
            document.getElementById('vendeurFilter').value = '';
            document.getElementById('statusFilter').value = '';
            updateReports();
        }

        function exportReport(format) {
            if (format === 'pdf') {
                showNotification('Export PDF en cours de développement', 'info');
            } else if (format === 'excel') {
                exportToCSV();
            }
        }

        function exportTable() {
            exportToCSV();
        }

        function exportToCSV() {
            const vendeurFilter = document.getElementById('vendeurFilter').value;
            const statusFilter = document.getElementById('statusFilter').value;

            let filteredData = clientsData.filter(client => {
                let include = true;

                if (vendeurFilter && client.vendeur !== getVendeurName(vendeurFilter)) {
                    include = false;
                }

                if (statusFilter && client.statut !== statusFilter) {
                    include = false;
                }

                return include;
            });

            let csv = 'Nom;Prénom;Email;Téléphone;Ville;Statut;Vendeur;Date Création\n';
            filteredData.forEach(client => {
                csv += `"${client.nom || ''}";`;
                csv += `"${client.prenom || ''}";`;
                csv += `"${client.email || ''}";`;
                csv += `"${client.telephone || ''}";`;
                csv += `"${client.ville || ''}";`;
                csv += `"${client.statut || 'prospect'}";`;
                csv += `"${client.vendeur || 'Non attribué'}";`;
                csv += `"${client.dateCreation || ''}"\n`;
            });

            const BOM = '\uFEFF';
            const blob = new Blob([BOM + csv], { type: 'text/csv;charset=utf-8;' });
            const link = document.createElement('a');
            const url = URL.createObjectURL(blob);
            link.setAttribute('href', url);
            link.setAttribute('download', `rapport_clients_${new Date().toISOString().split('T')[0]}.csv`);
            link.style.visibility = 'hidden';
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);

            showNotification('Rapport exporté avec succès', 'success');
        }

        function showNotification(message, type) {
            const container = document.getElementById('notificationContainer');
            const alertClass = type === 'success' ? 'alert-success' :
                              type === 'error' ? 'alert-danger' :
                              type === 'info' ? 'alert-info' : 'alert-warning';

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass} alert-dismissible fade show`;
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            container.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        console.log('📊 BINANCE CRM Reports - Page chargée avec succès');
    </script>
</body>
</html>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Marie Martin</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-success" style="width: 35%">35%</div>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Pierre Durand</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-info" style="width: 30%">30%</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Sophie Bernard</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-warning" style="width: 25%">25%</div>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Autres</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-secondary" style="width: 10%">10%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistiques détaillées -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-speedometer2"></i> Métriques Clés</h5>
                    </div>
                    <div class="card-body">
                        <div class="stat-card card mb-3">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-2 text-primary"></i>
                                <h4 class="fw-bold">127</h4>
                                <p class="text-muted mb-0">Clients Actifs</p>
                            </div>
                        </div>
                        
                        <div class="stat-card card mb-3">
                            <div class="card-body text-center">
                                <i class="bi bi-envelope-check fs-2 text-success"></i>
                                <h4 class="fw-bold">254</h4>
                                <p class="text-muted mb-0">Emails Envoyés</p>
                            </div>
                        </div>
                        
                        <div class="stat-card card mb-3">
                            <div class="card-body text-center">
                                <i class="bi bi-calendar-check fs-2 text-warning"></i>
                                <h4 class="fw-bold">42</h4>
                                <p class="text-muted mb-0">RDV Planifiés</p>
                            </div>
                        </div>
                        
                        <div class="stat-card card">
                            <div class="card-body text-center">
                                <i class="bi bi-trophy fs-2 text-info"></i>
                                <h4 class="fw-bold">89%</h4>
                                <p class="text-muted mb-0">Taux de Réussite</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-clock-history"></i> Activité Récente</h5>
                    </div>
                    <div class="card-body">
                        <div class="report-item">
                            <h6>Nouveau client ajouté</h6>
                            <small class="text-muted">Jean Dupont - Il y a 5 min</small>
                        </div>
                        <div class="report-item">
                            <h6>Email campagne envoyé</h6>
                            <small class="text-muted">Newsletter - Il y a 1h</small>
                        </div>
                        <div class="report-item">
                            <h6>RDV planifié</h6>
                            <small class="text-muted">Marie Martin - Il y a 2h</small>
                        </div>
                        <div class="report-item">
                            <h6>Rapport généré</h6>
                            <small class="text-muted">Ventes mensuel - Il y a 3h</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Rapports disponibles -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-file-earmark-text"></i> Rapports Disponibles</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-graph-up fs-1 text-success mb-3"></i>
                                <h6>Rapport de Ventes</h6>
                                <p class="text-muted small">Analyse détaillée des performances commerciales</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('sales')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-1 text-info mb-3"></i>
                                <h6>Rapport Clients</h6>
                                <p class="text-muted small">Statistiques et segmentation clientèle</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('clients')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-person-badge fs-1 text-warning mb-3"></i>
                                <h6>Rapport Vendeurs</h6>
                                <p class="text-muted small">Performance individuelle des vendeurs</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('vendeurs')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-envelope fs-1 text-primary mb-3"></i>
                                <h6>Rapport Emails</h6>
                                <p class="text-muted small">Analytics des campagnes email</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('emails')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-calendar-event fs-1 text-secondary mb-3"></i>
                                <h6>Rapport RDV</h6>
                                <p class="text-muted small">Suivi des rendez-vous et conversions</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('rdv')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-file-earmark-bar-graph fs-1 text-danger mb-3"></i>
                                <h6>Rapport Complet</h6>
                                <p class="text-muted small">Synthèse globale de l'activité</p>
                                <button class="btn btn-primary btn-sm" onclick="generateReport('complet')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statut système -->
        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-check-circle"></i> Système de Rapports Binance Opérationnel :</h6>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Analytics</strong><br>
                    <small>Tableaux de bord temps réel</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ KPIs</strong><br>
                    <small>Indicateurs de performance</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Exports</strong><br>
                    <small>PDF, Excel, CSV</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Programmation</strong><br>
                    <small>Rapports automatiques</small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        function showChart(period) {
            // Retirer la classe active des boutons
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Ajouter la classe active au bouton cliqué
            event.target.classList.add('active');
            
            // Simuler le changement de graphique
            const chartContainer = document.getElementById('salesChart');
            let content = '';
            
            switch(period) {
                case 'week':
                    content = `
                        <div class="text-center">
                            <i class="bi bi-bar-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                            <h5 class="mt-3">Ventes - 7 derniers jours</h5>
                            <p class="text-muted">Total: €335K (+12%)</p>
                            <div class="mt-3">
                                <span class="badge bg-success me-2">Lun: €45K</span>
                                <span class="badge bg-success me-2">Mar: €52K</span>
                                <span class="badge bg-warning me-2">Mer: €38K</span>
                                <span class="badge bg-success me-2">Jeu: €61K</span>
                                <span class="badge bg-success me-2">Ven: €58K</span>
                                <span class="badge bg-info me-2">Sam: €42K</span>
                                <span class="badge bg-info">Dim: €39K</span>
                            </div>
                        </div>
                    `;
                    break;
                case 'month':
                    content = `
                        <div class="text-center">
                            <i class="bi bi-bar-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                            <h5 class="mt-3">Ventes - 30 derniers jours</h5>
                            <p class="text-muted">Total: €2.4M (+15%)</p>
                            <div class="mt-3">
                                <span class="badge bg-success me-2">S1: €580K</span>
                                <span class="badge bg-success me-2">S2: €620K</span>
                                <span class="badge bg-warning me-2">S3: €540K</span>
                                <span class="badge bg-success">S4: €660K</span>
                            </div>
                        </div>
                    `;
                    break;
                case 'year':
                    content = `
                        <div class="text-center">
                            <i class="bi bi-bar-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                            <h5 class="mt-3">Ventes - 12 derniers mois</h5>
                            <p class="text-muted">Total: €28.8M (+22%)</p>
                            <div class="mt-3">
                                <span class="badge bg-success me-1">Jan: €2.1M</span>
                                <span class="badge bg-success me-1">Fév: €2.3M</span>
                                <span class="badge bg-warning me-1">Mar: €1.9M</span>
                                <span class="badge bg-success me-1">Avr: €2.5M</span>
                                <span class="badge bg-success me-1">Mai: €2.7M</span>
                                <span class="badge bg-success">...</span>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            chartContainer.innerHTML = content;
        }
        
        function generateReport(type) {
            const reportNames = {
                'sales': 'Rapport de Ventes',
                'clients': 'Rapport Clients',
                'vendeurs': 'Rapport Vendeurs',
                'emails': 'Rapport Emails',
                'rdv': 'Rapport RDV',
                'complet': 'Rapport Complet'
            };

            showNotification(`Génération du ${reportNames[type]} en cours...`, 'info');

            // Simuler la génération avec un délai
            setTimeout(() => {
                const reportData = generateReportData(type);
                displayReportModal(reportNames[type], reportData);
                showNotification(`${reportNames[type]} généré avec succès !`, 'success');
            }, 2000);
        }

        function generateReportData(type) {
            const baseData = {
                date: new Date().toLocaleDateString('fr-FR'),
                period: 'Janvier 2024',
                generated_by: JSON.parse(sessionStorage.getItem('user') || '{}').name || 'Utilisateur'
            };

            switch(type) {
                case 'sales':
                    return {
                        ...baseData,
                        title: 'Rapport de Ventes',
                        data: {
                            ca_total: '€2.4M',
                            ca_mensuel: '€335K',
                            nb_ventes: 127,
                            ticket_moyen: '€18,897',
                            croissance: '+15%',
                            top_vendeurs: [
                                { nom: 'Marie Martin', ca: '€840K', ventes: 45 },
                                { nom: 'Sophie Bernard', ca: '€792K', ventes: 44 },
                                { nom: 'Pierre Durand', ca: '€684K', ventes: 38 }
                            ]
                        }
                    };

                case 'clients':
                    return {
                        ...baseData,
                        title: 'Rapport Clients',
                        data: {
                            total_clients: 127,
                            nouveaux_clients: 38,
                            clients_actifs: 89,
                            taux_retention: '92%',
                            satisfaction: '4.8/5',
                            segments: [
                                { nom: 'Premium', count: 45, ca: '€1.2M' },
                                { nom: 'Standard', count: 67, ca: '€980K' },
                                { nom: 'Basic', count: 15, ca: '€220K' }
                            ]
                        }
                    };

                case 'vendeurs':
                    return {
                        ...baseData,
                        title: 'Rapport Vendeurs',
                        data: {
                            total_vendeurs: 4,
                            vendeurs_actifs: 3,
                            performance_moyenne: '89%',
                            ca_par_vendeur: '€600K',
                            details: [
                                { nom: 'Marie Martin', clients: 45, ca: '€840K', performance: 'Excellent' },
                                { nom: 'Sophie Bernard', clients: 44, ca: '€792K', performance: 'Excellent' },
                                { nom: 'Pierre Durand', clients: 38, ca: '€684K', performance: 'Bon' }
                            ]
                        }
                    };

                case 'emails':
                    return {
                        ...baseData,
                        title: 'Rapport Emails',
                        data: {
                            emails_envoyes: 254,
                            emails_ouverts: 216,
                            taux_ouverture: '85%',
                            clics: 89,
                            taux_clic: '35%',
                            campagnes: [
                                { nom: 'Newsletter', envoyes: 127, ouverture: '85%' },
                                { nom: 'Promotion', envoyes: 89, ouverture: '78%' },
                                { nom: 'Suivi', envoyes: 38, ouverture: '92%' }
                            ]
                        }
                    };

                case 'rdv':
                    return {
                        ...baseData,
                        title: 'Rapport RDV',
                        data: {
                            rdv_planifies: 42,
                            rdv_realises: 38,
                            taux_realisation: '90%',
                            duree_moyenne: '35 min',
                            conversion: '78%',
                            planning: [
                                { vendeur: 'Marie Martin', rdv: 18, realises: 16 },
                                { vendeur: 'Sophie Bernard', rdv: 15, realises: 14 },
                                { vendeur: 'Pierre Durand', rdv: 9, realises: 8 }
                            ]
                        }
                    };

                case 'complet':
                    return {
                        ...baseData,
                        title: 'Rapport Complet',
                        data: {
                            resume: {
                                ca: '€2.4M',
                                clients: 127,
                                vendeurs: 4,
                                emails: 254,
                                rdv: 42
                            },
                            kpis: {
                                croissance_ca: '+15%',
                                satisfaction: '4.8/5',
                                retention: '92%',
                                conversion: '89%'
                            }
                        }
                    };

                default:
                    return baseData;
            }
        }
        
        function displayReportModal(title, reportData) {
            let contentHtml = `
                <div class="report-header mb-4">
                    <div class="row">
                        <div class="col-md-8">
                            <h4>${reportData.title}</h4>
                            <p class="text-muted">Période: ${reportData.period} | Généré le: ${reportData.date}</p>
                        </div>
                        <div class="col-md-4 text-end">
                            <button class="btn btn-outline-primary btn-sm" onclick="downloadReportPDF('${title}')">
                                <i class="bi bi-download"></i> PDF
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="downloadReportCSV('${title}')">
                                <i class="bi bi-file-earmark-spreadsheet"></i> CSV
                            </button>
                        </div>
                    </div>
                </div>
            `;

            // Générer le contenu spécifique selon le type de rapport
            if (reportData.data.resume) {
                // Rapport complet
                contentHtml += `
                    <div class="row mb-4">
                        <div class="col-md-2">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-primary">${reportData.data.resume.ca}</h5>
                                    <small>Chiffre d'Affaires</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-success">${reportData.data.resume.clients}</h5>
                                    <small>Clients</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-2">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-info">${reportData.data.resume.vendeurs}</h5>
                                    <small>Vendeurs</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-warning">${reportData.data.resume.emails}</h5>
                                    <small>Emails</small>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card text-center">
                                <div class="card-body">
                                    <h5 class="text-secondary">${reportData.data.resume.rdv}</h5>
                                    <small>RDV</small>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            } else {
                // Autres rapports
                const data = reportData.data;
                contentHtml += '<div class="row mb-4">';

                Object.keys(data).forEach((key, index) => {
                    if (typeof data[key] === 'string' || typeof data[key] === 'number') {
                        contentHtml += `
                            <div class="col-md-3 mb-2">
                                <div class="card text-center">
                                    <div class="card-body">
                                        <h6 class="text-primary">${data[key]}</h6>
                                        <small>${key.replace('_', ' ').toUpperCase()}</small>
                                    </div>
                                </div>
                            </div>
                        `;
                    }
                });

                contentHtml += '</div>';

                // Ajouter les tableaux de détails
                Object.keys(data).forEach(key => {
                    if (Array.isArray(data[key])) {
                        contentHtml += `
                            <div class="mb-4">
                                <h6>${key.replace('_', ' ').toUpperCase()}</h6>
                                <div class="table-responsive">
                                    <table class="table table-sm">
                                        <tbody>
                        `;

                        data[key].forEach(item => {
                            contentHtml += '<tr>';
                            Object.values(item).forEach(value => {
                                contentHtml += `<td>${value}</td>`;
                            });
                            contentHtml += '</tr>';
                        });

                        contentHtml += `
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        `;
                    }
                });
            }

            contentHtml += `
                <div class="text-center mt-4">
                    <small class="text-muted">Rapport généré par ${reportData.generated_by} - BINANCE CRM</small>
                </div>
            `;

            showModal(title, contentHtml);
        }

        function downloadReportPDF(title) {
            showNotification(`Génération PDF du ${title} en cours...`, 'info');

            // Créer le contenu PDF
            const reportContent = document.querySelector('#genericModal .modal-body');
            if (!reportContent) {
                showNotification('Erreur: Aucun rapport ouvert pour générer le PDF', 'error');
                return;
            }

            // Utiliser html2canvas pour capturer le contenu
            html2canvas(reportContent, {
                scale: 2,
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff'
            }).then(canvas => {
                const { jsPDF } = window.jspdf;
                const pdf = new jsPDF('p', 'mm', 'a4');

                // Ajouter l'en-tête
                pdf.setFontSize(20);
                pdf.setTextColor(241, 194, 50); // Couleur Binance
                pdf.text('BINANCE CRM', 20, 20);

                pdf.setFontSize(16);
                pdf.setTextColor(0, 0, 0);
                pdf.text(title, 20, 35);

                pdf.setFontSize(10);
                pdf.setTextColor(128, 128, 128);
                pdf.text(`Généré le: ${new Date().toLocaleDateString('fr-FR')} à ${new Date().toLocaleTimeString('fr-FR')}`, 20, 45);

                // Ajouter le contenu capturé
                const imgData = canvas.toDataURL('image/png');
                const imgWidth = 170; // Largeur en mm
                const imgHeight = (canvas.height * imgWidth) / canvas.width;

                // Vérifier si le contenu tient sur une page
                if (imgHeight > 200) {
                    // Diviser en plusieurs pages si nécessaire
                    let yPosition = 55;
                    const pageHeight = 250;
                    let remainingHeight = imgHeight;

                    while (remainingHeight > 0) {
                        const currentHeight = Math.min(pageHeight - yPosition, remainingHeight);

                        pdf.addImage(imgData, 'PNG', 20, yPosition, imgWidth, currentHeight);

                        remainingHeight -= currentHeight;

                        if (remainingHeight > 0) {
                            pdf.addPage();
                            yPosition = 20;
                        }
                    }
                } else {
                    pdf.addImage(imgData, 'PNG', 20, 55, imgWidth, imgHeight);
                }

                // Ajouter le pied de page
                const pageCount = pdf.internal.getNumberOfPages();
                for (let i = 1; i <= pageCount; i++) {
                    pdf.setPage(i);
                    pdf.setFontSize(8);
                    pdf.setTextColor(128, 128, 128);
                    pdf.text(`Page ${i} sur ${pageCount}`, 20, 285);
                    pdf.text('BINANCE CRM - Rapport confidentiel', 150, 285);
                }

                // Télécharger le PDF
                const fileName = `${title.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;
                pdf.save(fileName);

                showNotification(`PDF "${fileName}" téléchargé avec succès !`, 'success');

            }).catch(error => {
                console.error('Erreur génération PDF:', error);
                showNotification('Erreur lors de la génération du PDF', 'error');
            });
        }

        function downloadReportCSV(title) {
            showNotification(`Export CSV du ${title} généré avec succès`, 'success');
        }

        function exportReport() {
            showNotification('Génération du rapport global PDF en cours...', 'info');

            // Générer un rapport complet
            const reportData = generateReportData('complet');
            displayReportModal('Rapport Global BINANCE CRM', reportData);

            // Attendre que le modal soit affiché puis générer le PDF
            setTimeout(() => {
                downloadReportPDF('Rapport_Global_BINANCE_CRM');
            }, 1000);
        }

        function scheduleReport() {
            const modalHtml = `
                <div class="modal fade" id="scheduleModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-calendar-plus"></i> Programmer un Rapport
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">Type de rapport</label>
                                    <select class="form-select" id="scheduleReportType">
                                        <option value="complet">Rapport Complet</option>
                                        <option value="sales">Rapport de Ventes</option>
                                        <option value="clients">Rapport Clients</option>
                                        <option value="vendeurs">Rapport Vendeurs</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Fréquence</label>
                                    <select class="form-select" id="scheduleFrequency">
                                        <option value="daily">Quotidien</option>
                                        <option value="weekly">Hebdomadaire</option>
                                        <option value="monthly">Mensuel</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Email de destination</label>
                                    <input type="email" class="form-control" id="scheduleEmail" placeholder="<EMAIL>">
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" onclick="confirmScheduleReport()">
                                    <i class="bi bi-check"></i> Programmer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            showModalFromHtml(modalHtml, 'scheduleModal');
        }

        function confirmScheduleReport() {
            const type = document.getElementById('scheduleReportType').value;
            const frequency = document.getElementById('scheduleFrequency').value;
            const email = document.getElementById('scheduleEmail').value;

            if (!email) {
                showNotification('Veuillez saisir un email de destination', 'error');
                return;
            }

            // Sauvegarder la programmation
            const scheduledReports = JSON.parse(localStorage.getItem('binance_crm_scheduled_reports') || '[]');
            scheduledReports.push({
                id: Date.now(),
                type: type,
                frequency: frequency,
                email: email,
                created: new Date().toISOString(),
                active: true
            });
            localStorage.setItem('binance_crm_scheduled_reports', JSON.stringify(scheduledReports));

            bootstrap.Modal.getInstance(document.getElementById('scheduleModal')).hide();
            showNotification(`Rapport ${type} programmé en ${frequency} vers ${email}`, 'success');
        }

        function customReport() {
            const modalHtml = `
                <div class="modal fade" id="customReportModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">
                                    <i class="bi bi-gear"></i> Rapport Personnalisé
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">Nom du rapport</label>
                                    <input type="text" class="form-control" id="customReportName" placeholder="Mon rapport personnalisé">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Période</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <input type="date" class="form-control" id="customDateFrom">
                                        </div>
                                        <div class="col-md-6">
                                            <input type="date" class="form-control" id="customDateTo">
                                        </div>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Sections à inclure</label>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includeSales" checked>
                                                <label class="form-check-label" for="includeSales">Ventes</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includeClients" checked>
                                                <label class="form-check-label" for="includeClients">Clients</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includeEmails" checked>
                                                <label class="form-check-label" for="includeEmails">Emails</label>
                                            </div>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="includeRdv" checked>
                                                <label class="form-check-label" for="includeRdv">RDV</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                                <button type="button" class="btn btn-primary" onclick="generateCustomReport()">
                                    <i class="bi bi-file-earmark-text"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            showModalFromHtml(modalHtml, 'customReportModal');
        }

        function generateCustomReport() {
            const name = document.getElementById('customReportName').value || 'Rapport Personnalisé';

            bootstrap.Modal.getInstance(document.getElementById('customReportModal')).hide();
            showNotification(`Génération du rapport "${name}" en cours...`, 'info');

            setTimeout(() => {
                const customData = {
                    title: name,
                    date: new Date().toLocaleDateString('fr-FR'),
                    period: 'Période personnalisée',
                    generated_by: JSON.parse(sessionStorage.getItem('user') || '{}').name || 'Utilisateur',
                    data: {
                        ca_total: '€2.4M',
                        clients_total: 127,
                        emails_envoyes: 254,
                        rdv_planifies: 42,
                        performance: '89%'
                    }
                };

                displayReportModal(name, customData);
                showNotification(`Rapport "${name}" généré avec succès !`, 'success');
            }, 2000);
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        // Fonctions utilitaires
        function showModal(title, content) {
            const modalHtml = `
                <div class="modal fade" id="genericModal" tabindex="-1">
                    <div class="modal-dialog modal-xl">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">${title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                ${content}
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            showModalFromHtml(modalHtml, 'genericModal');
        }

        function showModalFromHtml(modalHtml, modalId) {
            const existingModal = document.getElementById(modalId);
            if (existingModal) {
                existingModal.remove();
            }

            document.body.insertAdjacentHTML('beforeend', modalHtml);
            new bootstrap.Modal(document.getElementById(modalId)).show();
        }

        function showNotification(message, type = 'info') {
            const alertClass = {
                'success': 'alert-success',
                'error': 'alert-danger',
                'info': 'alert-info',
                'warning': 'alert-warning'
            };

            const notification = document.createElement('div');
            notification.className = `alert ${alertClass[type]} alert-dismissible fade show position-fixed`;
            notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(notification);

            setTimeout(() => {
                if (notification.parentNode) {
                    notification.remove();
                }
            }, 5000);
        }

        // Charger les données depuis localStorage
        function loadReportsData() {
            const scheduledReports = JSON.parse(localStorage.getItem('binance_crm_scheduled_reports') || '[]');
            if (scheduledReports.length > 0) {
                console.log(`${scheduledReports.length} rapports programmés chargés`);
            }
        }

        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
            loadReportsData();
        });
        
        console.log('📊 BINANCE CRM Rapports - Page chargée avec succès');
    </script>
</body>
</html>

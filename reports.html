<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rapports & Analytics - BINANCE CRM</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background-color: #f8f9fa; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .navbar { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%) !important; 
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .navbar-brand, .navbar-nav .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
        }
        
        .stat-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .stat-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .chart-container {
            position: relative;
            height: 300px;
            background: #fff;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid #dee2e6;
        }
        
        .progress-custom {
            height: 25px;
            border-radius: 12px;
        }
        
        .kpi-card {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            color: #000;
            border-radius: 15px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
        }
        
        .report-item {
            border-left: 4px solid var(--binance-yellow);
            padding-left: 15px;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark sticky-top">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="dashboard.html">
                <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
            </a>
            
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="dashboard.html">Dashboard</a>
                <a class="nav-link" href="clients.html">Clients</a>
                <a class="nav-link" href="vendeurs.html">Vendeurs</a>
                <a class="nav-link" href="emails.html">Emails</a>
                <a class="nav-link active" href="reports.html">Rapports</a>
                <a class="nav-link" href="#" onclick="logout()">Déconnexion</a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h1 class="h3 mb-1">
                    <i class="bi bi-graph-up"></i> Rapports & Analytics
                </h1>
                <p class="text-muted">Tableaux de bord • KPIs • Analyses de performance</p>
            </div>
            <div class="btn-group">
                <button class="btn btn-success" onclick="exportReport()">
                    <i class="bi bi-download"></i> Export PDF
                </button>
                <button class="btn btn-info" onclick="scheduleReport()">
                    <i class="bi bi-calendar-plus"></i> Programmer
                </button>
                <button class="btn btn-primary" onclick="customReport()">
                    <i class="bi bi-gear"></i> Rapport Personnalisé
                </button>
            </div>
        </div>
        
        <!-- KPIs principaux -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="kpi-card">
                    <h2 class="fw-bold mb-1">€2.4M</h2>
                    <p class="mb-0">Chiffre d'Affaires</p>
                    <small>+15% vs mois dernier</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-card">
                    <h2 class="fw-bold mb-1">127</h2>
                    <p class="mb-0">Nouveaux Clients</p>
                    <small>+8% vs mois dernier</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-card">
                    <h2 class="fw-bold mb-1">89%</h2>
                    <p class="mb-0">Taux Conversion</p>
                    <small>+3% vs mois dernier</small>
                </div>
            </div>
            <div class="col-md-3">
                <div class="kpi-card">
                    <h2 class="fw-bold mb-1">4.8/5</h2>
                    <p class="mb-0">Satisfaction Client</p>
                    <small>Stable</small>
                </div>
            </div>
        </div>
        
        <div class="row">
            <!-- Graphiques -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="bi bi-bar-chart"></i> Évolution des Ventes</h5>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-secondary active" onclick="showChart('week')">7J</button>
                            <button class="btn btn-outline-secondary" onclick="showChart('month')">30J</button>
                            <button class="btn btn-outline-secondary" onclick="showChart('year')">1A</button>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="salesChart">
                            <div class="text-center">
                                <i class="bi bi-bar-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                                <h5 class="mt-3">Graphique des Ventes</h5>
                                <p class="text-muted">Évolution du chiffre d'affaires sur 7 jours</p>
                                <div class="mt-3">
                                    <span class="badge bg-success me-2">Lun: €45K</span>
                                    <span class="badge bg-success me-2">Mar: €52K</span>
                                    <span class="badge bg-warning me-2">Mer: €38K</span>
                                    <span class="badge bg-success me-2">Jeu: €61K</span>
                                    <span class="badge bg-success me-2">Ven: €58K</span>
                                    <span class="badge bg-info me-2">Sam: €42K</span>
                                    <span class="badge bg-info">Dim: €39K</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-pie-chart"></i> Répartition par Vendeur</h5>
                    </div>
                    <div class="card-body">
                        <div class="chart-container" id="vendeurChart">
                            <div class="text-center">
                                <i class="bi bi-pie-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                                <h5 class="mt-3">Performance par Vendeur</h5>
                                <div class="row mt-3">
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Marie Martin</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-success" style="width: 35%">35%</div>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Pierre Durand</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-info" style="width: 30%">30%</div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="mb-2">
                                            <strong>Sophie Bernard</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-warning" style="width: 25%">25%</div>
                                            </div>
                                        </div>
                                        <div class="mb-2">
                                            <strong>Autres</strong>
                                            <div class="progress progress-custom">
                                                <div class="progress-bar bg-secondary" style="width: 10%">10%</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Statistiques détaillées -->
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-speedometer2"></i> Métriques Clés</h5>
                    </div>
                    <div class="card-body">
                        <div class="stat-card card mb-3">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-2 text-primary"></i>
                                <h4 class="fw-bold">127</h4>
                                <p class="text-muted mb-0">Clients Actifs</p>
                            </div>
                        </div>
                        
                        <div class="stat-card card mb-3">
                            <div class="card-body text-center">
                                <i class="bi bi-envelope-check fs-2 text-success"></i>
                                <h4 class="fw-bold">254</h4>
                                <p class="text-muted mb-0">Emails Envoyés</p>
                            </div>
                        </div>
                        
                        <div class="stat-card card mb-3">
                            <div class="card-body text-center">
                                <i class="bi bi-calendar-check fs-2 text-warning"></i>
                                <h4 class="fw-bold">42</h4>
                                <p class="text-muted mb-0">RDV Planifiés</p>
                            </div>
                        </div>
                        
                        <div class="stat-card card">
                            <div class="card-body text-center">
                                <i class="bi bi-trophy fs-2 text-info"></i>
                                <h4 class="fw-bold">89%</h4>
                                <p class="text-muted mb-0">Taux de Réussite</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-clock-history"></i> Activité Récente</h5>
                    </div>
                    <div class="card-body">
                        <div class="report-item">
                            <h6>Nouveau client ajouté</h6>
                            <small class="text-muted">Jean Dupont - Il y a 5 min</small>
                        </div>
                        <div class="report-item">
                            <h6>Email campagne envoyé</h6>
                            <small class="text-muted">Newsletter - Il y a 1h</small>
                        </div>
                        <div class="report-item">
                            <h6>RDV planifié</h6>
                            <small class="text-muted">Marie Martin - Il y a 2h</small>
                        </div>
                        <div class="report-item">
                            <h6>Rapport généré</h6>
                            <small class="text-muted">Ventes mensuel - Il y a 3h</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Rapports disponibles -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-file-earmark-text"></i> Rapports Disponibles</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-graph-up fs-1 text-success mb-3"></i>
                                <h6>Rapport de Ventes</h6>
                                <p class="text-muted small">Analyse détaillée des performances commerciales</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('sales')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-people fs-1 text-info mb-3"></i>
                                <h6>Rapport Clients</h6>
                                <p class="text-muted small">Statistiques et segmentation clientèle</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('clients')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-person-badge fs-1 text-warning mb-3"></i>
                                <h6>Rapport Vendeurs</h6>
                                <p class="text-muted small">Performance individuelle des vendeurs</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('vendeurs')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-envelope fs-1 text-primary mb-3"></i>
                                <h6>Rapport Emails</h6>
                                <p class="text-muted small">Analytics des campagnes email</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('emails')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-calendar-event fs-1 text-secondary mb-3"></i>
                                <h6>Rapport RDV</h6>
                                <p class="text-muted small">Suivi des rendez-vous et conversions</p>
                                <button class="btn btn-outline-primary btn-sm" onclick="generateReport('rdv')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4 mb-3">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-file-earmark-bar-graph fs-1 text-danger mb-3"></i>
                                <h6>Rapport Complet</h6>
                                <p class="text-muted small">Synthèse globale de l'activité</p>
                                <button class="btn btn-primary btn-sm" onclick="generateReport('complet')">
                                    <i class="bi bi-download"></i> Générer
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Statut système -->
        <div class="alert alert-success mt-4">
            <h6><i class="bi bi-check-circle"></i> Système de Rapports Binance Opérationnel :</h6>
            <div class="row">
                <div class="col-md-3">
                    <strong>✅ Analytics</strong><br>
                    <small>Tableaux de bord temps réel</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ KPIs</strong><br>
                    <small>Indicateurs de performance</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Exports</strong><br>
                    <small>PDF, Excel, CSV</small>
                </div>
                <div class="col-md-3">
                    <strong>✅ Programmation</strong><br>
                    <small>Rapports automatiques</small>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Vérifier l'authentification
        function checkAuth() {
            const user = JSON.parse(sessionStorage.getItem('user') || '{}');
            if (!user.username) {
                window.location.href = 'login.html';
                return null;
            }
            return user;
        }
        
        function showChart(period) {
            // Retirer la classe active des boutons
            document.querySelectorAll('.btn-group .btn').forEach(btn => {
                btn.classList.remove('active');
            });
            
            // Ajouter la classe active au bouton cliqué
            event.target.classList.add('active');
            
            // Simuler le changement de graphique
            const chartContainer = document.getElementById('salesChart');
            let content = '';
            
            switch(period) {
                case 'week':
                    content = `
                        <div class="text-center">
                            <i class="bi bi-bar-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                            <h5 class="mt-3">Ventes - 7 derniers jours</h5>
                            <p class="text-muted">Total: €335K (+12%)</p>
                            <div class="mt-3">
                                <span class="badge bg-success me-2">Lun: €45K</span>
                                <span class="badge bg-success me-2">Mar: €52K</span>
                                <span class="badge bg-warning me-2">Mer: €38K</span>
                                <span class="badge bg-success me-2">Jeu: €61K</span>
                                <span class="badge bg-success me-2">Ven: €58K</span>
                                <span class="badge bg-info me-2">Sam: €42K</span>
                                <span class="badge bg-info">Dim: €39K</span>
                            </div>
                        </div>
                    `;
                    break;
                case 'month':
                    content = `
                        <div class="text-center">
                            <i class="bi bi-bar-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                            <h5 class="mt-3">Ventes - 30 derniers jours</h5>
                            <p class="text-muted">Total: €2.4M (+15%)</p>
                            <div class="mt-3">
                                <span class="badge bg-success me-2">S1: €580K</span>
                                <span class="badge bg-success me-2">S2: €620K</span>
                                <span class="badge bg-warning me-2">S3: €540K</span>
                                <span class="badge bg-success">S4: €660K</span>
                            </div>
                        </div>
                    `;
                    break;
                case 'year':
                    content = `
                        <div class="text-center">
                            <i class="bi bi-bar-chart" style="font-size: 4rem; color: var(--binance-yellow);"></i>
                            <h5 class="mt-3">Ventes - 12 derniers mois</h5>
                            <p class="text-muted">Total: €28.8M (+22%)</p>
                            <div class="mt-3">
                                <span class="badge bg-success me-1">Jan: €2.1M</span>
                                <span class="badge bg-success me-1">Fév: €2.3M</span>
                                <span class="badge bg-warning me-1">Mar: €1.9M</span>
                                <span class="badge bg-success me-1">Avr: €2.5M</span>
                                <span class="badge bg-success me-1">Mai: €2.7M</span>
                                <span class="badge bg-success">...</span>
                            </div>
                        </div>
                    `;
                    break;
            }
            
            chartContainer.innerHTML = content;
        }
        
        function generateReport(type) {
            const reportNames = {
                'sales': 'Rapport de Ventes',
                'clients': 'Rapport Clients',
                'vendeurs': 'Rapport Vendeurs',
                'emails': 'Rapport Emails',
                'rdv': 'Rapport RDV',
                'complet': 'Rapport Complet'
            };
            
            alert(`Génération du ${reportNames[type]} en cours...\n\nLe rapport sera disponible dans quelques instants.`);
        }
        
        function exportReport() {
            alert('Export PDF du rapport en cours...');
        }
        
        function scheduleReport() {
            alert('Programmation de rapport automatique (fonctionnalité en développement)');
        }
        
        function customReport() {
            alert('Création de rapport personnalisé (fonctionnalité en développement)');
        }
        
        function logout() {
            if (confirm('Êtes-vous sûr de vouloir vous déconnecter ?')) {
                sessionStorage.removeItem('user');
                window.location.href = 'login.html';
            }
        }
        
        // Initialiser la page
        document.addEventListener('DOMContentLoaded', function() {
            checkAuth();
        });
        
        console.log('📊 BINANCE CRM Rapports - Page chargée avec succès');
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}CRM System{% endblock %}</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="/static/style.css" rel="stylesheet">
    <style>
        .sidebar {
            min-height: 100vh;
            background-color: #f8f9fa;
        }
        .navbar-brand {
            font-weight: bold;
        }
        .card-stats {
            border-left: 4px solid #007bff;
        }
        .table-actions {
            white-space: nowrap;
        }
        .btn-sm {
            padding: 0.25rem 0.5rem;
            font-size: 0.875rem;
        }
        .alert-dismissible {
            padding-right: 3rem;
        }
    </style>
    {% block extra_css %}{% endblock %}
</head>
<body>
    {% if is_authenticated %}
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="bi bi-building"></i> CRM System
            </a>
            
            <div class="navbar-nav ms-auto">
                <div class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle text-white" href="#" role="button" data-bs-toggle="dropdown">
                        <i class="bi bi-person-circle"></i> {{ username }}
                        <span class="badge bg-secondary">{{ user_role }}</span>
                    </a>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="/logout">
                            <i class="bi bi-box-arrow-right"></i> Déconnexion
                        </a></li>
                    </ul>
                </div>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                <div class="position-sticky pt-3">
                    <ul class="nav flex-column">
                        {% if user_role == "admin" %}
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/dashboard">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/clients">
                                <i class="bi bi-people"></i> Clients
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/vendeurs">
                                <i class="bi bi-person-badge"></i> Vendeurs
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/templates">
                                <i class="bi bi-envelope"></i> Templates Email
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/admin/smtp">
                                <i class="bi bi-gear"></i> Configuration SMTP
                            </a>
                        </li>
                        {% else %}
                        <li class="nav-item">
                            <a class="nav-link" href="/vendeur/dashboard">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/vendeur/clients">
                                <i class="bi bi-people"></i> Mes Clients
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/vendeur/agenda">
                                <i class="bi bi-calendar"></i> Agenda
                            </a>
                        </li>
                        {% endif %}
                    </ul>
                </div>
            </nav>

            <!-- Main content -->
            <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                <div class="pt-3">
                    {% block content %}{% endblock %}
                </div>
            </main>
        </div>
    </div>
    {% else %}
    <!-- Page sans sidebar pour les non-connectés -->
    <div class="container-fluid">
        {% block content %}{% endblock %}
    </div>
    {% endif %}

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script>
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            var alerts = document.querySelectorAll('.alert-dismissible');
            alerts.forEach(function(alert) {
                var bsAlert = new bootstrap.Alert(alert);
                bsAlert.close();
            });
        }, 5000);

        // Confirmation for delete actions
        function confirmDelete(message) {
            return confirm(message || 'Êtes-vous sûr de vouloir supprimer cet élément ?');
        }

        // Select all checkboxes
        function toggleSelectAll(source) {
            var checkboxes = document.querySelectorAll('input[name="client_ids"]');
            checkboxes.forEach(function(checkbox) {
                checkbox.checked = source.checked;
            });
        }

        // Get selected client IDs
        function getSelectedClientIds() {
            var selected = [];
            var checkboxes = document.querySelectorAll('input[name="client_ids"]:checked');
            checkboxes.forEach(function(checkbox) {
                selected.push(checkbox.value);
            });
            return selected;
        }
    </script>
    
    {% block extra_js %}{% endblock %}
</body>
</html>

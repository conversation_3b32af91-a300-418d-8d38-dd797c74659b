from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from database import Base
from datetime import datetime

class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True, nullable=False)
    email = Column(String(100), unique=True, index=True, nullable=False)
    password_hash = Column(String(255), nullable=False)
    role = Column(String(20), nullable=False, default="vendeur")  # admin ou vendeur
    created_at = Column(DateTime, default=datetime.utcnow)
    is_active = Column(Boolean, default=True)
    
    # Relations
    clients = relationship("Client", back_populates="vendeur")
    appointments = relationship("Appointment", back_populates="vendeur")
    email_logs = relationship("EmailLog", back_populates="vendeur")

class Client(Base):
    __tablename__ = "clients"
    
    id = Column(Integer, primary_key=True, index=True)
    nom = Column(String(100), nullable=False)
    prenom = Column(String(100), nullable=False)
    email = Column(String(100), nullable=False, index=True)
    telephone = Column(String(20), nullable=False)
    date_naissance = Column(DateTime, nullable=False)
    adresse = Column(Text)
    
    # Attribution et suivi
    vendeur_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    indicateur = Column(String(100), default="nouveau")  # client mort, NRP, magnifique, etc.
    note = Column(Text)  # Note libre du vendeur
    
    # Suivi emails
    email_envoye = Column(Boolean, default=False)
    template_utilise = Column(String(100))  # Nom du template utilisé
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    vendeur = relationship("User", back_populates="clients")
    appointments = relationship("Appointment", back_populates="client")
    email_logs = relationship("EmailLog", back_populates="client")

class EmailTemplate(Base):
    __tablename__ = "email_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    nom = Column(String(100), nullable=False, unique=True)
    sujet = Column(String(200), nullable=False)
    contenu = Column(Text, nullable=False)
    variables = Column(Text)  # JSON des variables disponibles
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    email_logs = relationship("EmailLog", back_populates="template")

class Appointment(Base):
    __tablename__ = "appointments"
    
    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)
    vendeur_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    date_rdv = Column(DateTime, nullable=False)
    titre = Column(String(200), nullable=False)
    description = Column(Text)
    statut = Column(String(20), default="planifie")  # planifie, realise, annule
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relations
    client = relationship("Client", back_populates="appointments")
    vendeur = relationship("User", back_populates="appointments")

class EmailLog(Base):
    __tablename__ = "email_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    client_id = Column(Integer, ForeignKey("clients.id"), nullable=False)
    template_id = Column(Integer, ForeignKey("email_templates.id"), nullable=True)
    vendeur_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    sujet = Column(String(200), nullable=False)
    contenu = Column(Text, nullable=False)
    statut = Column(String(20), default="envoye")  # envoye, erreur
    erreur_message = Column(Text)
    
    date_envoi = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    client = relationship("Client", back_populates="email_logs")
    template = relationship("EmailTemplate", back_populates="email_logs")
    vendeur = relationship("User", back_populates="email_logs")

class SMTPConfig(Base):
    __tablename__ = "smtp_config"
    
    id = Column(Integer, primary_key=True, index=True)
    host = Column(String(100), nullable=False)
    port = Column(Integer, nullable=False, default=587)
    username = Column(String(100), nullable=False)
    password = Column(String(255), nullable=False)
    use_tls = Column(Boolean, default=True)
    from_email = Column(String(100), nullable=False)
    from_name = Column(String(100), default="CRM System")
    
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

class ActionLog(Base):
    __tablename__ = "action_logs"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    action = Column(String(100), nullable=False)  # import, modification, envoi_email, etc.
    details = Column(Text)  # JSON avec détails de l'action
    timestamp = Column(DateTime, default=datetime.utcnow)
    
    # Relations
    user = relationship("User")

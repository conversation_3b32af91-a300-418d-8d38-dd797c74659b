# 📖 Guide Utilisateur - CRM System

Ce guide détaille l'utilisation complète du système CRM pour les administrateurs et les vendeurs.

## 🚀 Première Connexion

### Accès à l'Application
1. Ouvrez votre navigateur web
2. Allez à l'adresse : `http://localhost:8000` (ou l'adresse de votre serveur)
3. Vous arrivez sur la page de connexion

### Connexion
- **Administrateur par défaut** :
  - Nom d'utilisateur : `admin`
  - Mot de passe : `admin123`
- **Vendeurs de démonstration** :
  - `marie.martin` / `vendeur123`
  - `pierre.durand` / `vendeur123`
  - `sophie.bernard` / `vendeur123`

⚠️ **Important** : Changez ces mots de passe après la première connexion !

## 👑 Guide Administrateur

### Dashboard Administrateur

Le dashboard vous donne une vue d'ensemble complète :

#### Statistiques Principales
- **Total Clients** : Nombre total de prospects dans la base
- **Clients Attribués** : Prospects assignés à des vendeurs
- **Non Attribués** : Prospects en attente d'attribution
- **Emails Envoyés** : Nombre total d'emails envoyés

#### Actions Rapides
- **Gérer les Clients** : Accès direct à la liste des clients
- **Gérer les Vendeurs** : Administration des comptes vendeurs
- **Templates Email** : Création et modification des modèles d'emails
- **Configuration SMTP** : Paramétrage de l'envoi d'emails

### Gestion des Clients

#### Consulter la Liste
1. Allez dans **Clients** depuis le menu
2. Utilisez les filtres pour affiner votre recherche :
   - **Vendeur** : Filtrer par vendeur attribué
   - **Indicateur** : Filtrer par statut (nouveau, magnifique, etc.)

#### Ajouter un Client
1. Cliquez sur **Nouveau Client**
2. Remplissez les informations obligatoires :
   - Nom et prénom
   - Email et téléphone
   - Date de naissance
3. Informations optionnelles :
   - Adresse complète
   - Vendeur à attribuer
   - Indicateur de statut
   - Note personnalisée

#### Attribution en Lot
1. Sélectionnez les clients avec les cases à cocher
2. Choisissez le vendeur dans la liste déroulante
3. Cliquez sur **Attribuer**
4. Confirmez l'opération

#### Import de Clients (CSV)
1. Cliquez sur **Importer CSV**
2. Sélectionnez votre fichier CSV
3. **Format requis** :
   ```csv
   nom,prenom,email,telephone,date_naissance,adresse,vendeur_id,indicateur
   Dupont,Jean,<EMAIL>,0123456789,1980-05-15,"123 Rue de la Paix",1,nouveau
   ```
4. **Colonnes obligatoires** : nom, prenom, email, telephone, date_naissance
5. **Colonnes optionnelles** : adresse, vendeur_id, indicateur

#### Export de Clients
1. Utilisez les filtres pour sélectionner les clients à exporter
2. Cliquez sur **Exporter CSV**
3. Le fichier se télécharge automatiquement

### Gestion des Vendeurs

#### Créer un Vendeur
1. Allez dans **Vendeurs**
2. Cliquez sur **Nouveau Vendeur**
3. Remplissez :
   - Nom d'utilisateur (unique)
   - Email professionnel
   - Mot de passe (minimum 6 caractères)
   - Confirmation du mot de passe
4. Cochez **Compte actif** si nécessaire

#### Modifier un Vendeur
1. Cliquez sur l'icône **crayon** dans la liste
2. Modifiez les informations
3. Activez/désactivez le compte si nécessaire

#### Réinitialiser un Mot de Passe
1. Cliquez sur l'icône **clé** dans la liste
2. Saisissez le nouveau mot de passe
3. Confirmez l'opération

#### Consulter les Statistiques
1. Cliquez sur l'icône **graphique** dans la liste
2. Consultez les performances du vendeur :
   - Nombre de clients attribués
   - Emails envoyés
   - Rendez-vous planifiés et réalisés

### Templates d'Email

#### Variables Disponibles
Utilisez ces variables dans vos templates :
- `{prenom}` : Prénom du client
- `{nom}` : Nom du client
- `{email}` : Email du client
- `{telephone}` : Téléphone du client
- `{date_rdv}` : Date du rendez-vous
- `{date_actuelle}` : Date du jour

#### Créer un Template
1. Allez dans **Templates Email**
2. Cliquez sur **Nouveau Template**
3. Remplissez :
   - **Nom** : Identifiant unique du template
   - **Sujet** : Objet de l'email (peut contenir des variables)
   - **Contenu** : Corps de l'email (HTML autorisé)
   - **Variables** : Liste des variables utilisées (documentation)

#### Exemple de Template
```
Nom: Premier Contact
Sujet: Bonjour {prenom}, découvrez nos services
Contenu:
<p>Bonjour {prenom} {nom},</p>
<p>J'espère que vous allez bien. Je me permets de vous contacter concernant nos services.</p>
<p>Cordialement,<br>L'équipe commerciale</p>
```

#### Prévisualiser un Template
1. Cliquez sur l'icône **œil** dans la liste
2. Le template s'affiche avec des données d'exemple
3. Vérifiez le rendu avant utilisation

### Configuration SMTP

#### Paramètres Gmail
1. **Serveur** : `smtp.gmail.com`
2. **Port** : `587`
3. **TLS** : Activé
4. **Nom d'utilisateur** : Votre email Gmail
5. **Mot de passe** : Mot de passe d'application (pas votre mot de passe habituel)

#### Paramètres Outlook
1. **Serveur** : `smtp-mail.outlook.com`
2. **Port** : `587`
3. **TLS** : Activé
4. **Nom d'utilisateur** : Votre email Outlook complet
5. **Mot de passe** : Votre mot de passe Outlook

#### Test de Configuration
1. Après avoir saisi les paramètres, cliquez sur **Tester**
2. Un message confirme si la configuration fonctionne
3. Sauvegardez seulement si le test réussit

### Statistiques Avancées

#### Filtres Disponibles
- **Période** : Choisissez une plage de dates
- **Vendeur** : Statistiques d'un vendeur spécifique
- **Périodes prédéfinies** : 7 jours, 30 jours, 3 mois, 1 an

#### Graphiques Disponibles
- **Évolution temporelle** : Nouveaux clients et emails dans le temps
- **Répartition par indicateur** : Camembert des statuts clients
- **Performance des vendeurs** : Comparaison des vendeurs
- **Funnel de conversion** : Entonnoir de vente

## 👤 Guide Vendeur

### Dashboard Vendeur

Votre dashboard personnel affiche :
- **Mes Clients** : Nombre de clients qui vous sont attribués
- **Emails Envoyés** : Nombre d'emails que vous avez envoyés
- **RDV Planifiés** : Rendez-vous à venir
- **RDV Réalisés** : Rendez-vous terminés

### Gestion de Mes Clients

#### Consulter Mes Clients
1. Allez dans **Mes Clients**
2. Vous ne voyez que les clients qui vous sont attribués
3. Utilisez le filtre par indicateur pour affiner

#### Mettre à Jour un Client
1. **Indicateur** : Changez directement dans la liste déroulante
   - **Nouveau** : Prospect récent
   - **En cours** : Contact en cours
   - **Magnifique** : Prospect très intéressé
   - **NRP** : Ne répond pas
   - **Client mort** : Prospect perdu
2. **Note** : Ajoutez des commentaires dans la zone de texte

#### Envoyer un Email
1. Cliquez sur l'icône **enveloppe** dans la liste
2. Choisissez un template dans la liste
3. Prévisualisez le contenu avec les données du client
4. Cliquez sur **Envoyer**
5. L'email est marqué comme envoyé automatiquement

#### Planifier un Rendez-vous
1. Cliquez sur l'icône **calendrier** dans la liste
2. Remplissez :
   - **Date et heure** du rendez-vous
   - **Titre** descriptif
   - **Description** détaillée (optionnel)
3. Le rendez-vous apparaît dans votre agenda

### Mon Agenda

#### Vue d'Ensemble
- **Aujourd'hui** : Rendez-vous du jour
- **Prochains RDV** : Rendez-vous à venir
- **Planning** : Vue calendrier (en développement)

#### Créer un Rendez-vous
1. Cliquez sur **Nouveau RDV**
2. Sélectionnez le client
3. Définissez la date et l'heure
4. Ajoutez un titre et une description
5. Choisissez le statut initial

#### Gérer les Rendez-vous
1. **Modifier** : Cliquez sur l'icône crayon
2. **Changer le statut** : Utilisez la liste déroulante
   - **Planifié** : RDV à venir
   - **Réalisé** : RDV terminé
   - **Annulé** : RDV annulé
3. **Envoyer un rappel** : Cliquez sur l'icône enveloppe
4. **Supprimer** : Cliquez sur l'icône poubelle

#### Envoi de Rappels
1. Cliquez sur l'icône **enveloppe** d'un rendez-vous
2. Un email de rappel est envoyé automatiquement au client
3. Le template "Confirmation RDV" est utilisé

## 🔧 Fonctionnalités Avancées

### Recherche Globale
- Utilisez la barre de recherche pour trouver rapidement un client
- La recherche porte sur : nom, prénom, email, téléphone, adresse

### Historique des Actions
- Toutes vos actions sont enregistrées
- Les administrateurs peuvent consulter l'historique complet
- Utile pour le suivi et l'audit

### Export Personnalisé
- Filtrez vos données selon vos critères
- Exportez au format CSV ou Excel
- Incluez ou excluez certaines colonnes

### Notifications
- Recevez des alertes pour les rendez-vous à venir
- Notifications d'emails en échec
- Alertes de sécurité (tentatives de connexion)

## 🚨 Résolution de Problèmes

### Problèmes de Connexion
- **Mot de passe oublié** : Contactez votre administrateur
- **Compte verrouillé** : Attendez 15 minutes ou contactez l'admin
- **Erreur de connexion** : Vérifiez votre nom d'utilisateur

### Problèmes d'Email
- **Email non envoyé** : Vérifiez la configuration SMTP
- **Template non trouvé** : Assurez-vous que le template existe
- **Variables non remplacées** : Vérifiez la syntaxe des variables

### Problèmes d'Import
- **Erreur de format** : Vérifiez que votre CSV respecte le format
- **Caractères spéciaux** : Utilisez l'encodage UTF-8
- **Doublons** : Les emails en doublon sont rejetés

### Performance Lente
- **Trop de données** : Utilisez les filtres pour limiter l'affichage
- **Connexion lente** : Vérifiez votre connexion internet
- **Navigateur** : Utilisez Chrome, Firefox ou Edge récents

## 📞 Support

### Contacts
- **Support technique** : <EMAIL>
- **Formation** : <EMAIL>
- **Urgences** : +33 X XX XX XX XX

### Ressources
- **Documentation API** : `/docs` (si activé)
- **Logs d'erreur** : Consultables par l'administrateur
- **Mises à jour** : Notifications automatiques

---

**💡 Conseil** : Prenez le temps d'explorer l'interface et n'hésitez pas à tester les fonctionnalités avec des données de démonstration avant d'utiliser vos vraies données !

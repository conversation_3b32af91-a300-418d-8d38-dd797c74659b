#!/usr/bin/env python3
"""
BINANCE CRM - Script d'Installation Automatique
Installation complète de toutes les dépendances et configuration
"""

import subprocess
import sys
import os
import json
from datetime import datetime

def print_banner():
    """Afficher la bannière d'installation"""
    print("=" * 80)
    print("🚀 BINANCE CRM - INSTALLATION AUTOMATIQUE")
    print("=" * 80)
    print(f"📅 Installation le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}")
    print("🔧 Installation de toutes les dépendances...")
    print()

def install_dependencies():
    """Installer toutes les dépendances Python"""
    print("📦 Installation des dépendances Python...")
    
    dependencies = [
        'reportlab',
        'pillow',  # Pour les images dans les PDFs
        'requests'  # Pour les requêtes HTTP
    ]
    
    for dep in dependencies:
        try:
            print(f"   📥 Installation de {dep}...")
            subprocess.check_call([
                sys.executable, "-m", "pip", "install", dep, "--quiet"
            ])
            print(f"   ✅ {dep} installé avec succès")
        except subprocess.CalledProcessError:
            print(f"   ❌ Erreur lors de l'installation de {dep}")
            return False
    
    print("✅ Toutes les dépendances Python installées\n")
    return True

def create_directories():
    """Créer tous les dossiers nécessaires"""
    print("📁 Création des dossiers...")
    
    directories = [
        'reports',
        'exports', 
        'uploads',
        'templates',
        'static',
        'logs'
    ]
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"   📁 Dossier créé: {directory}")
        else:
            print(f"   ✅ Dossier existe: {directory}")
    
    print("✅ Tous les dossiers créés\n")

def setup_smtp_config():
    """Configurer SMTP"""
    print("📧 Configuration SMTP...")
    
    if os.path.exists('smtp_config.json'):
        print("   ✅ Configuration SMTP existe déjà")
        return True
    
    print("   ⚙️  Création du fichier de configuration SMTP...")
    
    config = {
        "smtp_server": "smtp.gmail.com",
        "smtp_port": 587,
        "username": "<EMAIL>",
        "password": "votre-mot-de-passe-application",
        "use_tls": True,
        "from_name": "BINANCE CRM"
    }
    
    try:
        with open('smtp_config.json', 'w', encoding='utf-8') as f:
            json.dump(config, f, indent=4, ensure_ascii=False)
        print("   ✅ Configuration SMTP créée")
        print("   ⚠️  N'oubliez pas de modifier smtp_config.json avec vos vraies informations")
    except Exception as e:
        print(f"   ❌ Erreur création config SMTP: {e}")
        return False
    
    print("✅ Configuration SMTP prête\n")
    return True

def test_installation():
    """Tester l'installation"""
    print("🧪 Test de l'installation...")
    
    # Test des imports
    test_modules = [
        ('reportlab', 'Génération PDF'),
        ('sqlite3', 'Base de données'),
        ('json', 'Configuration'),
        ('http.server', 'Serveur web')
    ]
    
    for module, description in test_modules:
        try:
            __import__(module)
            print(f"   ✅ {description} - OK")
        except ImportError:
            print(f"   ❌ {description} - ERREUR")
            return False
    
    # Test des fichiers
    required_files = [
        'email_server.py',
        'pdf_server.py', 
        'database_server.py',
        'start_all_servers.py',
        'login.html',
        'dashboard.html',
        'clients.html',
        'vendeurs.html',
        'emails.html',
        'reports.html'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"   ✅ {file} - OK")
        else:
            print(f"   ❌ {file} - MANQUANT")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n   ⚠️  Fichiers manquants: {', '.join(missing_files)}")
        return False
    
    print("✅ Installation testée avec succès\n")
    return True

def create_start_script():
    """Créer un script de démarrage rapide"""
    print("🚀 Création du script de démarrage...")
    
    if os.name == 'nt':  # Windows
        script_content = '''@echo off
echo ========================================
echo BINANCE CRM - DEMARRAGE RAPIDE
echo ========================================
echo.
echo Demarrage de tous les serveurs...
echo.
python start_all_servers.py
pause
'''
        with open('start.bat', 'w') as f:
            f.write(script_content)
        print("   ✅ Script start.bat créé pour Windows")
    
    else:  # Linux/Mac
        script_content = '''#!/bin/bash
echo "========================================"
echo "BINANCE CRM - DEMARRAGE RAPIDE"
echo "========================================"
echo
echo "Demarrage de tous les serveurs..."
echo
python3 start_all_servers.py
'''
        with open('start.sh', 'w') as f:
            f.write(script_content)
        os.chmod('start.sh', 0o755)
        print("   ✅ Script start.sh créé pour Linux/Mac")
    
    print("✅ Script de démarrage créé\n")

def show_final_instructions():
    """Afficher les instructions finales"""
    print("=" * 80)
    print("🎉 INSTALLATION TERMINÉE AVEC SUCCÈS !")
    print("=" * 80)
    print()
    print("📋 PROCHAINES ÉTAPES:")
    print()
    print("1. 📧 CONFIGURER SMTP (OPTIONNEL):")
    print("   • Éditez le fichier 'smtp_config.json'")
    print("   • Ajoutez vos vraies informations SMTP")
    print("   • Pour Gmail: utilisez un mot de passe d'application")
    print()
    print("2. 🚀 DÉMARRER LE SYSTÈME:")
    if os.name == 'nt':
        print("   • Double-cliquez sur 'start.bat'")
        print("   • OU exécutez: python start_all_servers.py")
    else:
        print("   • Exécutez: ./start.sh")
        print("   • OU exécutez: python3 start_all_servers.py")
    print()
    print("3. 🌐 ACCÉDER AU CRM:")
    print("   • Ouvrez votre navigateur")
    print("   • Allez sur: http://localhost:8000/login.html")
    print("   • Connectez-vous avec: admin / admin123")
    print()
    print("✨ FONCTIONNALITÉS 100% RÉELLES:")
    print("   ✅ Base de données SQLite persistante")
    print("   ✅ Envoi réel d'emails via SMTP")
    print("   ✅ Génération PDF réelle avec ReportLab")
    print("   ✅ API REST complète")
    print("   ✅ Interface web responsive")
    print("   ✅ Authentification sécurisée")
    print("   ✅ CRUD complet clients/vendeurs")
    print("   ✅ Import/Export CSV et JSON")
    print()
    print("📞 SUPPORT:")
    print("   • Consultez les logs en cas de problème")
    print("   • Vérifiez que tous les ports sont libres (8000-8003)")
    print("   • Pour SMTP: vérifiez votre configuration email")
    print()
    print("🎊 BINANCE CRM EST PRÊT À ÊTRE UTILISÉ !")
    print("=" * 80)

def main():
    """Fonction principale d'installation"""
    print_banner()
    
    # Étapes d'installation
    steps = [
        ("Installation des dépendances", install_dependencies),
        ("Création des dossiers", create_directories),
        ("Configuration SMTP", setup_smtp_config),
        ("Test de l'installation", test_installation),
        ("Création du script de démarrage", create_start_script)
    ]
    
    for step_name, step_function in steps:
        print(f"🔄 {step_name}...")
        if not step_function():
            print(f"❌ Échec de l'étape: {step_name}")
            print("🛑 Installation interrompue")
            return False
    
    show_final_instructions()
    return True

if __name__ == '__main__':
    try:
        success = main()
        if success:
            print("\n✅ Installation réussie !")
        else:
            print("\n❌ Installation échouée !")
            sys.exit(1)
    except KeyboardInterrupt:
        print("\n🛑 Installation interrompue par l'utilisateur")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Erreur inattendue: {e}")
        sys.exit(1)

#!/usr/bin/env python3
"""
BINANCE CRM - Serveur Email SMTP Réel
Serveur Python pour envoi réel d'emails via SMTP
"""

import smtplib
import json
import os
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON><PERSON><PERSON>
from email.mime.base import MI<PERSON>B<PERSON>
from email import encoders
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import threading
import time
from datetime import datetime
import sqlite3

class EmailServer:
    def __init__(self):
        self.smtp_config = {
            'smtp_server': 'smtp.gmail.com',  # Configurable
            'smtp_port': 587,
            'username': '',  # À configurer
            'password': '',  # À configurer
            'use_tls': True
        }
        self.init_database()
    
    def init_database(self):
        """Initialiser la base de données pour l'historique des emails"""
        self.conn = sqlite3.connect('binance_crm_emails.db', check_same_thread=False)
        cursor = self.conn.cursor()
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template TEXT,
                subject TEXT,
                recipients TEXT,
                recipient_count INTEGER,
                sender TEXT,
                content TEXT,
                status TEXT,
                sent_date TIMESTAMP,
                delivered INTEGER DEFAULT 0,
                opened INTEGER DEFAULT 0,
                clicked INTEGER DEFAULT 0,
                bounced INTEGER DEFAULT 0
            )
        ''')
        
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE,
                subject TEXT,
                content TEXT,
                created_date TIMESTAMP,
                modified_date TIMESTAMP
            )
        ''')
        
        self.conn.commit()
    
    def configure_smtp(self, smtp_server, smtp_port, username, password, use_tls=True):
        """Configurer les paramètres SMTP"""
        self.smtp_config = {
            'smtp_server': smtp_server,
            'smtp_port': smtp_port,
            'username': username,
            'password': password,
            'use_tls': use_tls
        }
        return True
    
    def send_email(self, to_emails, subject, html_content, template_name='custom'):
        """Envoyer un email réel via SMTP"""
        try:
            # Créer le message
            msg = MIMEMultipart('alternative')
            msg['From'] = self.smtp_config['username']
            msg['Subject'] = subject
            
            # Ajouter le contenu HTML
            html_part = MIMEText(html_content, 'html', 'utf-8')
            msg.attach(html_part)
            
            # Se connecter au serveur SMTP
            server = smtplib.SMTP(self.smtp_config['smtp_server'], self.smtp_config['smtp_port'])
            
            if self.smtp_config['use_tls']:
                server.starttls()
            
            server.login(self.smtp_config['username'], self.smtp_config['password'])
            
            # Envoyer l'email
            sent_count = 0
            failed_count = 0
            
            for email in to_emails:
                try:
                    msg['To'] = email
                    server.send_message(msg)
                    sent_count += 1
                    del msg['To']
                except Exception as e:
                    print(f"Erreur envoi vers {email}: {e}")
                    failed_count += 1
            
            server.quit()
            
            # Enregistrer dans la base de données
            self.save_email_history(template_name, subject, to_emails, html_content, 'sent', sent_count, failed_count)
            
            return {
                'success': True,
                'sent': sent_count,
                'failed': failed_count,
                'total': len(to_emails)
            }
            
        except Exception as e:
            print(f"Erreur SMTP: {e}")
            self.save_email_history(template_name, subject, to_emails, html_content, 'failed', 0, len(to_emails))
            return {
                'success': False,
                'error': str(e),
                'sent': 0,
                'failed': len(to_emails),
                'total': len(to_emails)
            }
    
    def save_email_history(self, template, subject, recipients, content, status, sent, failed):
        """Sauvegarder l'historique des emails"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT INTO email_history 
            (template, subject, recipients, recipient_count, content, status, sent_date, delivered, bounced)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            template, subject, json.dumps(recipients), len(recipients), 
            content, status, datetime.now(), sent, failed
        ))
        self.conn.commit()
    
    def get_email_history(self):
        """Récupérer l'historique des emails"""
        cursor = self.conn.cursor()
        cursor.execute('''
            SELECT * FROM email_history ORDER BY sent_date DESC LIMIT 50
        ''')
        return cursor.fetchall()
    
    def save_template(self, name, subject, content):
        """Sauvegarder un template d'email"""
        cursor = self.conn.cursor()
        cursor.execute('''
            INSERT OR REPLACE INTO email_templates 
            (name, subject, content, created_date, modified_date)
            VALUES (?, ?, ?, ?, ?)
        ''', (name, subject, content, datetime.now(), datetime.now()))
        self.conn.commit()
        return True
    
    def get_templates(self):
        """Récupérer tous les templates"""
        cursor = self.conn.cursor()
        cursor.execute('SELECT * FROM email_templates ORDER BY name')
        return cursor.fetchall()

class EmailAPIHandler(BaseHTTPRequestHandler):
    email_server = EmailServer()
    
    def do_OPTIONS(self):
        """Gérer les requêtes OPTIONS pour CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def do_POST(self):
        """Gérer les requêtes POST"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            path = urlparse(self.path).path
            
            if path == '/api/send-email':
                result = self.handle_send_email(data)
            elif path == '/api/configure-smtp':
                result = self.handle_configure_smtp(data)
            elif path == '/api/save-template':
                result = self.handle_save_template(data)
            elif path == '/api/get-history':
                result = self.handle_get_history()
            elif path == '/api/get-templates':
                result = self.handle_get_templates()
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def handle_send_email(self, data):
        """Gérer l'envoi d'email"""
        to_emails = data.get('to_emails', [])
        subject = data.get('subject', '')
        html_content = data.get('html_content', '')
        template_name = data.get('template', 'custom')
        
        if not to_emails or not subject or not html_content:
            return {'success': False, 'error': 'Données manquantes'}
        
        return self.email_server.send_email(to_emails, subject, html_content, template_name)
    
    def handle_configure_smtp(self, data):
        """Configurer SMTP"""
        smtp_server = data.get('smtp_server', '')
        smtp_port = data.get('smtp_port', 587)
        username = data.get('username', '')
        password = data.get('password', '')
        use_tls = data.get('use_tls', True)
        
        if not smtp_server or not username or not password:
            return {'success': False, 'error': 'Configuration SMTP incomplète'}
        
        self.email_server.configure_smtp(smtp_server, smtp_port, username, password, use_tls)
        return {'success': True, 'message': 'Configuration SMTP mise à jour'}
    
    def handle_save_template(self, data):
        """Sauvegarder un template"""
        name = data.get('name', '')
        subject = data.get('subject', '')
        content = data.get('content', '')
        
        if not name or not subject or not content:
            return {'success': False, 'error': 'Données template manquantes'}
        
        self.email_server.save_template(name, subject, content)
        return {'success': True, 'message': 'Template sauvegardé'}
    
    def handle_get_history(self):
        """Récupérer l'historique"""
        history = self.email_server.get_email_history()
        return {'success': True, 'history': history}
    
    def handle_get_templates(self):
        """Récupérer les templates"""
        templates = self.email_server.get_templates()
        return {'success': True, 'templates': templates}

def start_email_server(port=8001):
    """Démarrer le serveur email"""
    server = HTTPServer(('localhost', port), EmailAPIHandler)
    print(f"🚀 Serveur Email BINANCE CRM démarré sur http://localhost:{port}")
    print("📧 Endpoints disponibles:")
    print("   POST /api/send-email - Envoyer un email")
    print("   POST /api/configure-smtp - Configurer SMTP")
    print("   POST /api/save-template - Sauvegarder template")
    print("   POST /api/get-history - Historique emails")
    print("   POST /api/get-templates - Liste templates")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur email")
        server.shutdown()

if __name__ == '__main__':
    # Configuration par défaut (à modifier selon vos besoins)
    print("⚙️  Configuration SMTP requise:")
    print("   - Serveur SMTP (ex: smtp.gmail.com)")
    print("   - Port (ex: 587)")
    print("   - Username/Email")
    print("   - Mot de passe d'application")
    print()
    
    start_email_server()

{% extends "base.html" %}

{% block title %}Dashboard Admin - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-speedometer2"></i> Dashboard Administrateur</h1>
</div>

<!-- Statistiques -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card card-stats text-white bg-primary">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Total Clients</h5>
                        <h2 class="mb-0">{{ stats.total_clients }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats text-white bg-success">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Clients Attribués</h5>
                        <h2 class="mb-0">{{ stats.clients_attribues }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats text-white bg-warning">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Non Attribués</h5>
                        <h2 class="mb-0">{{ stats.clients_non_attribues }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-person-x fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card card-stats text-white bg-info">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">Emails Envoyés</h5>
                        <h2 class="mb-0">{{ stats.emails_envoyes }}</h2>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-envelope-check fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Statistiques par indicateur -->
{% if stats.indicateurs %}
<div class="row mb-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-bar-chart"></i> Répartition par Indicateur</h5>
            </div>
            <div class="card-body">
                {% for indicateur, count in stats.indicateurs.items() %}
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="badge bg-secondary">{{ indicateur }}</span>
                    <span class="fw-bold">{{ count }}</span>
                </div>
                {% endfor %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-calendar-check"></i> Rendez-vous</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Planifiés</span>
                    <span class="badge bg-primary">{{ stats.rdv_planifies }}</span>
                </div>
                <div class="d-flex justify-content-between align-items-center">
                    <span>Réalisés</span>
                    <span class="badge bg-success">{{ stats.rdv_realises }}</span>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Actions rapides -->
<div class="row mb-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-lightning"></i> Actions Rapides</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3">
                        <a href="/admin/clients" class="btn btn-outline-primary w-100 mb-2">
                            <i class="bi bi-people"></i><br>Gérer les Clients
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/vendeurs" class="btn btn-outline-success w-100 mb-2">
                            <i class="bi bi-person-badge"></i><br>Gérer les Vendeurs
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/templates" class="btn btn-outline-info w-100 mb-2">
                            <i class="bi bi-envelope"></i><br>Templates Email
                        </a>
                    </div>
                    <div class="col-md-3">
                        <a href="/admin/smtp" class="btn btn-outline-warning w-100 mb-2">
                            <i class="bi bi-gear"></i><br>Config SMTP
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Clients récents et logs -->
<div class="row">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-clock-history"></i> Clients Récents</h5>
            </div>
            <div class="card-body">
                {% if recent_clients %}
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Email</th>
                                <th>Vendeur</th>
                                <th>Indicateur</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for client in recent_clients %}
                            <tr>
                                <td>{{ client.prenom }} {{ client.nom }}</td>
                                <td>{{ client.email }}</td>
                                <td>
                                    {% if client.vendeur %}
                                        <span class="badge bg-success">{{ client.vendeur.username }}</span>
                                    {% else %}
                                        <span class="badge bg-warning">Non attribué</span>
                                    {% endif %}
                                </td>
                                <td><span class="badge bg-secondary">{{ client.indicateur }}</span></td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <p class="text-muted">Aucun client trouvé.</p>
                {% endif %}
            </div>
        </div>
    </div>
    
    <div class="col-md-6">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-activity"></i> Activité Récente</h5>
            </div>
            <div class="card-body">
                {% if recent_logs %}
                <div class="list-group list-group-flush">
                    {% for log in recent_logs %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h6 class="mb-1">{{ log.action }}</h6>
                            <small>{{ log.timestamp.strftime('%d/%m/%Y %H:%M') }}</small>
                        </div>
                        <p class="mb-1">{{ log.user.username }}</p>
                        {% if log.details %}
                        <small class="text-muted">{{ log.details[:100] }}...</small>
                        {% endif %}
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <p class="text-muted">Aucune activité récente.</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

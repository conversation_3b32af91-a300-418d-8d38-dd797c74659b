#!/usr/bin/env python3
"""
Script de mise à jour pour l'application CRM
"""

import os
import sys
import subprocess
import shutil
import json
from datetime import datetime
from pathlib import Path
import argparse

from version import __version__, get_version_info
from database import create_tables, get_db
import crud

class CRMUpdater:
    """Gestionnaire de mise à jour du CRM"""
    
    def __init__(self):
        self.current_version = __version__
        self.backup_dir = Path("backups")
        self.backup_dir.mkdir(exist_ok=True)
        
    def create_backup(self):
        """Créer une sauvegarde avant mise à jour"""
        print("📦 Création de la sauvegarde...")
        
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_name = f"backup_{self.current_version}_{timestamp}"
        backup_path = self.backup_dir / backup_name
        backup_path.mkdir(exist_ok=True)
        
        # Sauvegarder la base de données
        if os.path.exists("crm.db"):
            shutil.copy2("crm.db", backup_path / "crm.db")
            print(f"  ✅ Base de données sauvegardée")
        
        # Sauvegarder les fichiers uploadés
        if os.path.exists("static/uploads"):
            shutil.copytree("static/uploads", backup_path / "uploads", dirs_exist_ok=True)
            print(f"  ✅ Fichiers uploadés sauvegardés")
        
        # Sauvegarder la configuration
        if os.path.exists(".env"):
            shutil.copy2(".env", backup_path / ".env")
            print(f"  ✅ Configuration sauvegardée")
        
        # Sauvegarder les logs
        if os.path.exists("logs"):
            shutil.copytree("logs", backup_path / "logs", dirs_exist_ok=True)
            print(f"  ✅ Logs sauvegardés")
        
        # Créer un fichier d'information
        info = {
            "version": self.current_version,
            "date": datetime.now().isoformat(),
            "backup_type": "pre_update"
        }
        
        with open(backup_path / "backup_info.json", "w") as f:
            json.dump(info, f, indent=2)
        
        print(f"✅ Sauvegarde créée dans : {backup_path}")
        return backup_path
    
    def update_dependencies(self):
        """Mettre à jour les dépendances"""
        print("📚 Mise à jour des dépendances...")
        
        try:
            # Mettre à jour pip
            subprocess.run([sys.executable, "-m", "pip", "install", "--upgrade", "pip"], 
                         check=True, capture_output=True)
            print("  ✅ pip mis à jour")
            
            # Installer/mettre à jour les dépendances
            subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt", "--upgrade"], 
                         check=True, capture_output=True)
            print("  ✅ Dépendances mises à jour")
            
        except subprocess.CalledProcessError as e:
            print(f"  ❌ Erreur lors de la mise à jour des dépendances: {e}")
            return False
        
        return True
    
    def update_database(self):
        """Mettre à jour la structure de la base de données"""
        print("🗄️ Mise à jour de la base de données...")
        
        try:
            # Créer les nouvelles tables si nécessaire
            create_tables()
            print("  ✅ Structure de base de données mise à jour")
            
            # Exécuter les migrations si nécessaire
            db = next(get_db())
            
            # Vérifier et créer les données par défaut manquantes
            admin = crud.get_user_by_username(db, "admin")
            if not admin:
                print("  ⚠️ Utilisateur admin manquant, création...")
                from schemas import UserCreate
                admin_user = UserCreate(
                    username="admin",
                    email="<EMAIL>",
                    password="admin123",
                    role="admin"
                )
                crud.create_user(db, admin_user)
                print("  ✅ Utilisateur admin créé")
            
            return True
            
        except Exception as e:
            print(f"  ❌ Erreur lors de la mise à jour de la base de données: {e}")
            return False
    
    def update_static_files(self):
        """Mettre à jour les fichiers statiques"""
        print("🎨 Mise à jour des fichiers statiques...")
        
        # Créer les dossiers nécessaires
        os.makedirs("static/uploads", exist_ok=True)
        os.makedirs("logs", exist_ok=True)
        
        print("  ✅ Dossiers statiques vérifiés")
        return True
    
    def run_tests(self):
        """Exécuter les tests après mise à jour"""
        print("🧪 Exécution des tests...")
        
        try:
            result = subprocess.run([sys.executable, "test_app.py"], 
                                  capture_output=True, text=True)
            
            if result.returncode == 0:
                print("  ✅ Tous les tests sont passés")
                return True
            else:
                print("  ⚠️ Certains tests ont échoué:")
                print(result.stdout)
                return False
                
        except Exception as e:
            print(f"  ❌ Erreur lors de l'exécution des tests: {e}")
            return False
    
    def cleanup_old_files(self):
        """Nettoyer les anciens fichiers"""
        print("🧹 Nettoyage des anciens fichiers...")
        
        # Nettoyer les anciens logs (garder 30 jours)
        if os.path.exists("logs"):
            import time
            cutoff = time.time() - (30 * 24 * 60 * 60)  # 30 jours
            
            for file in Path("logs").glob("*.log*"):
                if file.stat().st_mtime < cutoff:
                    file.unlink()
                    print(f"  🗑️ Supprimé: {file}")
        
        # Nettoyer les anciennes sauvegardes (garder 10)
        backups = sorted(self.backup_dir.glob("backup_*"), key=lambda x: x.stat().st_mtime, reverse=True)
        for old_backup in backups[10:]:  # Garder les 10 plus récentes
            shutil.rmtree(old_backup)
            print(f"  🗑️ Ancienne sauvegarde supprimée: {old_backup.name}")
        
        print("  ✅ Nettoyage terminé")
    
    def update(self, skip_backup=False, skip_tests=False):
        """Effectuer la mise à jour complète"""
        print(f"🚀 Mise à jour du CRM System v{self.current_version}")
        print("=" * 50)
        
        # Créer une sauvegarde
        if not skip_backup:
            backup_path = self.create_backup()
        else:
            print("⚠️ Sauvegarde ignorée (--skip-backup)")
            backup_path = None
        
        success = True
        
        # Étapes de mise à jour
        steps = [
            ("Dépendances", self.update_dependencies),
            ("Base de données", self.update_database),
            ("Fichiers statiques", self.update_static_files),
        ]
        
        if not skip_tests:
            steps.append(("Tests", self.run_tests))
        
        steps.append(("Nettoyage", self.cleanup_old_files))
        
        for step_name, step_func in steps:
            print(f"\n📋 Étape: {step_name}")
            if not step_func():
                print(f"❌ Échec de l'étape: {step_name}")
                success = False
                break
        
        print("\n" + "=" * 50)
        
        if success:
            print("🎉 Mise à jour terminée avec succès !")
            print(f"📊 Version: {self.current_version}")
            print("🔗 Accès: http://localhost:8000")
            print("👤 Admin: admin / admin123")
        else:
            print("❌ Mise à jour échouée !")
            if backup_path:
                print(f"💾 Sauvegarde disponible dans: {backup_path}")
                print("🔄 Vous pouvez restaurer avec: python update.py --restore")
        
        return success
    
    def restore_backup(self, backup_name=None):
        """Restaurer une sauvegarde"""
        print("🔄 Restauration de sauvegarde...")
        
        if backup_name:
            backup_path = self.backup_dir / backup_name
        else:
            # Prendre la sauvegarde la plus récente
            backups = sorted(self.backup_dir.glob("backup_*"), 
                           key=lambda x: x.stat().st_mtime, reverse=True)
            if not backups:
                print("❌ Aucune sauvegarde trouvée")
                return False
            backup_path = backups[0]
        
        if not backup_path.exists():
            print(f"❌ Sauvegarde non trouvée: {backup_path}")
            return False
        
        print(f"📦 Restauration depuis: {backup_path}")
        
        # Restaurer la base de données
        if (backup_path / "crm.db").exists():
            shutil.copy2(backup_path / "crm.db", "crm.db")
            print("  ✅ Base de données restaurée")
        
        # Restaurer les fichiers uploadés
        if (backup_path / "uploads").exists():
            if os.path.exists("static/uploads"):
                shutil.rmtree("static/uploads")
            shutil.copytree(backup_path / "uploads", "static/uploads")
            print("  ✅ Fichiers uploadés restaurés")
        
        # Restaurer la configuration
        if (backup_path / ".env").exists():
            shutil.copy2(backup_path / ".env", ".env")
            print("  ✅ Configuration restaurée")
        
        print("✅ Restauration terminée")
        return True
    
    def list_backups(self):
        """Lister les sauvegardes disponibles"""
        print("📦 Sauvegardes disponibles:")
        
        backups = sorted(self.backup_dir.glob("backup_*"), 
                        key=lambda x: x.stat().st_mtime, reverse=True)
        
        if not backups:
            print("  Aucune sauvegarde trouvée")
            return
        
        for backup in backups:
            info_file = backup / "backup_info.json"
            if info_file.exists():
                with open(info_file) as f:
                    info = json.load(f)
                date = datetime.fromisoformat(info['date']).strftime('%d/%m/%Y %H:%M')
                print(f"  📁 {backup.name} - v{info['version']} - {date}")
            else:
                date = datetime.fromtimestamp(backup.stat().st_mtime).strftime('%d/%m/%Y %H:%M')
                print(f"  📁 {backup.name} - {date}")

def main():
    parser = argparse.ArgumentParser(description="Outil de mise à jour du CRM System")
    parser.add_argument("--skip-backup", action="store_true", 
                       help="Ignorer la création de sauvegarde")
    parser.add_argument("--skip-tests", action="store_true", 
                       help="Ignorer l'exécution des tests")
    parser.add_argument("--restore", type=str, nargs='?', const='latest',
                       help="Restaurer une sauvegarde (latest pour la plus récente)")
    parser.add_argument("--list-backups", action="store_true",
                       help="Lister les sauvegardes disponibles")
    parser.add_argument("--version", action="store_true",
                       help="Afficher les informations de version")
    
    args = parser.parse_args()
    
    updater = CRMUpdater()
    
    if args.version:
        info = get_version_info()
        print(f"CRM System v{info['version']}")
        print(f"Build: {info['build']}")
        print(f"Author: {info['author']}")
        print(f"Description: {info['description']}")
        return
    
    if args.list_backups:
        updater.list_backups()
        return
    
    if args.restore:
        backup_name = None if args.restore == 'latest' else args.restore
        success = updater.restore_backup(backup_name)
        sys.exit(0 if success else 1)
    
    # Mise à jour normale
    success = updater.update(
        skip_backup=args.skip_backup,
        skip_tests=args.skip_tests
    )
    
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()

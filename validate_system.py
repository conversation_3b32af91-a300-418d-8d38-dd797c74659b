#!/usr/bin/env python3
"""
BINANCE CRM - Validation Système Complète
Script de validation de toutes les fonctionnalités et performances
"""

import requests
import time
import json
import sqlite3
from concurrent.futures import ThreadPoolExecutor
import threading

class SystemValidator:
    def __init__(self, base_url='http://localhost:8000'):
        self.base_url = base_url
        self.results = {}
        
    def test_server_connectivity(self):
        """Tester la connectivité du serveur"""
        print("🔗 Test de connectivité du serveur...")
        
        try:
            response = requests.get(f"{self.base_url}/api/dashboard-stats", timeout=5)
            if response.status_code == 200:
                print("  ✅ Serveur accessible")
                return True
            else:
                print(f"  ❌ Serveur répond avec code {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"  ❌ Erreur de connexion: {e}")
            return False
    
    def test_database_indexes(self):
        """Vérifier que les index sont créés"""
        print("📊 Vérification des index de base de données...")
        
        try:
            conn = sqlite3.connect('binance_crm.db')
            cursor = conn.cursor()
            
            # Lister tous les index
            cursor.execute("SELECT name FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
            indexes = [row[0] for row in cursor.fetchall()]
            
            expected_indexes = [
                'idx_clients_email',
                'idx_clients_status', 
                'idx_clients_assigned_to',
                'idx_users_email',
                'idx_users_role',
                'idx_clients_status_assigned'
            ]
            
            missing_indexes = []
            for expected in expected_indexes:
                if expected in indexes:
                    print(f"  ✅ {expected}")
                else:
                    print(f"  ❌ {expected} - MANQUANT")
                    missing_indexes.append(expected)
            
            conn.close()
            
            if missing_indexes:
                print(f"  ⚠️  {len(missing_indexes)} index manquants")
                return False
            else:
                print(f"  ✅ Tous les index sont présents ({len(indexes)} total)")
                return True
                
        except Exception as e:
            print(f"  ❌ Erreur lors de la vérification: {e}")
            return False
    
    def test_api_endpoints(self):
        """Tester tous les endpoints API"""
        print("🌐 Test des endpoints API...")
        
        endpoints = [
            ('/api/clients', 'GET'),
            ('/api/vendeurs', 'GET'),
            ('/api/users', 'GET'),
            ('/api/email-templates', 'GET'),
            ('/api/import-template', 'GET'),
            ('/api/import-history', 'GET'),
            ('/api/dashboard-stats', 'GET')
        ]
        
        success_count = 0
        total_time = 0
        
        for endpoint, method in endpoints:
            start_time = time.time()
            try:
                if method == 'GET':
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                
                duration = time.time() - start_time
                total_time += duration
                
                if response.status_code == 200:
                    print(f"  ✅ {endpoint}: {duration:.3f}s")
                    success_count += 1
                else:
                    print(f"  ❌ {endpoint}: Code {response.status_code}")
                    
            except Exception as e:
                duration = time.time() - start_time
                print(f"  ❌ {endpoint}: Erreur - {e}")
        
        avg_time = total_time / len(endpoints) if endpoints else 0
        success_rate = (success_count / len(endpoints) * 100) if endpoints else 0
        
        print(f"  📊 Taux de succès: {success_rate:.1f}%")
        print(f"  ⏱️  Temps de réponse moyen: {avg_time:.3f}s")
        
        return success_rate >= 90 and avg_time < 0.5
    
    def test_concurrent_performance(self):
        """Tester les performances en concurrence"""
        print("⚡ Test de performance concurrente...")
        
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}/api/clients", timeout=10)
                duration = time.time() - start_time
                return {
                    'success': response.status_code == 200,
                    'duration': duration
                }
            except:
                return {'success': False, 'duration': 10}
        
        # Test avec 20 requêtes simultanées
        num_requests = 20
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_requests) as executor:
            futures = [executor.submit(make_request) for _ in range(num_requests)]
            results = [future.result() for future in futures]
        
        total_time = time.time() - start_time
        
        success_count = sum(1 for r in results if r['success'])
        success_rate = success_count / num_requests * 100
        avg_duration = sum(r['duration'] for r in results if r['success']) / success_count if success_count > 0 else 0
        requests_per_second = num_requests / total_time
        
        print(f"  📊 {num_requests} requêtes simultanées:")
        print(f"    Taux de succès: {success_rate:.1f}%")
        print(f"    Temps de réponse moyen: {avg_duration:.3f}s")
        print(f"    Requêtes/seconde: {requests_per_second:.1f}")
        print(f"    Temps total: {total_time:.3f}s")
        
        # Critères de performance
        performance_ok = (
            success_rate >= 95 and 
            avg_duration < 1.0 and 
            requests_per_second > 15
        )
        
        if performance_ok:
            print("  ✅ Performance concurrente excellente")
        else:
            print("  ⚠️  Performance concurrente à améliorer")
        
        return performance_ok
    
    def test_csv_import_functionality(self):
        """Tester la fonctionnalité d'import CSV"""
        print("📊 Test de l'import CSV...")
        
        # Données de test
        test_data = {
            'csv_data': [
                {
                    'nom': 'TEST_VALIDATION',
                    'prenom': 'User',
                    'email': f'validation.test.{int(time.time())}@example.com',
                    'telephone': '0123456789',
                    'statut': 'prospect'
                }
            ],
            'filename': 'validation_test.csv',
            'user_id': 1,
            'skip_errors': True
        }
        
        try:
            start_time = time.time()
            response = requests.post(
                f"{self.base_url}/api/import-clients",
                json=test_data,
                timeout=30
            )
            duration = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    print(f"  ✅ Import CSV réussi en {duration:.3f}s")
                    print(f"    Clients importés: {result.get('successful_rows', 0)}")
                    return True
                else:
                    print(f"  ❌ Import CSV échoué: {result.get('error', 'Erreur inconnue')}")
                    return False
            else:
                print(f"  ❌ Import CSV - Code de réponse: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"  ❌ Erreur lors de l'import CSV: {e}")
            return False
    
    def test_pages_accessibility(self):
        """Tester l'accessibilité des pages principales"""
        print("🌐 Test d'accessibilité des pages...")
        
        pages = [
            '/dashboard.html',
            '/clients.html', 
            '/vendeurs.html',
            '/emails.html',
            '/reports.html',
            '/admin_config.html'
        ]
        
        success_count = 0
        
        for page in pages:
            try:
                response = requests.get(f"{self.base_url}{page}", timeout=10)
                if response.status_code == 200:
                    print(f"  ✅ {page}")
                    success_count += 1
                else:
                    print(f"  ❌ {page}: Code {response.status_code}")
            except Exception as e:
                print(f"  ❌ {page}: Erreur - {e}")
        
        success_rate = (success_count / len(pages) * 100) if pages else 0
        print(f"  📊 Pages accessibles: {success_rate:.1f}%")
        
        return success_rate == 100
    
    def test_database_performance(self):
        """Tester les performances de la base de données"""
        print("🗄️  Test de performance base de données...")
        
        try:
            conn = sqlite3.connect('binance_crm.db')
            cursor = conn.cursor()
            
            # Test de requêtes avec mesure de temps
            queries = [
                ("SELECT COUNT(*) FROM clients", "Comptage clients"),
                ("SELECT * FROM clients WHERE email LIKE '%@%' LIMIT 10", "Recherche email"),
                ("SELECT c.*, u.first_name FROM clients c LEFT JOIN users u ON c.assigned_to = u.id LIMIT 10", "Jointure clients-vendeurs"),
                ("SELECT status, COUNT(*) FROM clients GROUP BY status", "Agrégation par statut")
            ]
            
            all_fast = True
            
            for query, description in queries:
                start_time = time.time()
                cursor.execute(query)
                cursor.fetchall()
                duration = time.time() - start_time
                
                if duration < 0.1:
                    print(f"  ✅ {description}: {duration:.4f}s")
                elif duration < 0.5:
                    print(f"  ⚡ {description}: {duration:.4f}s")
                else:
                    print(f"  ⚠️  {description}: {duration:.4f}s (lent)")
                    all_fast = False
            
            conn.close()
            
            if all_fast:
                print("  ✅ Toutes les requêtes sont rapides")
            else:
                print("  ⚠️  Certaines requêtes sont lentes")
            
            return all_fast
            
        except Exception as e:
            print(f"  ❌ Erreur lors du test: {e}")
            return False
    
    def run_complete_validation(self):
        """Exécuter la validation complète du système"""
        print("🚀 VALIDATION COMPLÈTE DU SYSTÈME BINANCE CRM")
        print("=" * 60)
        
        tests = [
            ("Connectivité serveur", self.test_server_connectivity),
            ("Index base de données", self.test_database_indexes),
            ("Endpoints API", self.test_api_endpoints),
            ("Performance concurrente", self.test_concurrent_performance),
            ("Import CSV", self.test_csv_import_functionality),
            ("Accessibilité pages", self.test_pages_accessibility),
            ("Performance base de données", self.test_database_performance)
        ]
        
        results = {}
        passed_tests = 0
        
        for test_name, test_func in tests:
            print(f"\n🧪 {test_name.upper()}")
            print("-" * 40)
            
            try:
                result = test_func()
                results[test_name] = result
                if result:
                    passed_tests += 1
            except Exception as e:
                print(f"  ❌ Erreur inattendue: {e}")
                results[test_name] = False
        
        # Rapport final
        print(f"\n📊 RAPPORT FINAL DE VALIDATION")
        print("=" * 40)
        
        for test_name, result in results.items():
            status = "✅ PASSÉ" if result else "❌ ÉCHOUÉ"
            print(f"  {test_name}: {status}")
        
        success_rate = (passed_tests / len(tests) * 100) if tests else 0
        print(f"\n🎯 TAUX DE RÉUSSITE GLOBAL: {success_rate:.1f}%")
        
        if success_rate >= 90:
            print("🎉 SYSTÈME VALIDÉ - PRÊT POUR PRODUCTION")
        elif success_rate >= 70:
            print("⚠️  SYSTÈME PARTIELLEMENT VALIDÉ - CORRECTIONS MINEURES NÉCESSAIRES")
        else:
            print("❌ SYSTÈME NON VALIDÉ - CORRECTIONS MAJEURES REQUISES")
        
        return success_rate >= 90

if __name__ == "__main__":
    validator = SystemValidator()
    validator.run_complete_validation()

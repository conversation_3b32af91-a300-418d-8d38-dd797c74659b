<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BINANCE CRM - Connexion Sécurisée</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .login-header {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            color: #000;
            padding: 30px;
            text-align: center;
        }
        
        .binance-logo { 
            font-size: 3rem; 
            font-weight: bold; 
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }
        
        .form-control:focus {
            border-color: var(--binance-yellow);
            box-shadow: 0 0 0 0.2rem rgba(241, 194, 50, 0.25);
        }
        
        .btn-login {
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            border: none;
            color: #000;
            font-weight: 600;
            padding: 12px;
            border-radius: 8px;
            transition: all 0.3s ease;
        }
        
        .btn-login:hover {
            background: linear-gradient(135deg, #e6b800 0%, var(--binance-yellow) 100%);
            color: #000;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(241, 194, 50, 0.4);
        }
        
        .demo-account {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
            cursor: pointer;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }
        
        .demo-account:hover {
            background: #bbdefb;
            transform: translateY(-2px);
            border-color: var(--binance-yellow);
        }
        
        .avatar-circle {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: #000;
            font-weight: bold;
            font-size: 14px;
        }
        
        .loading-spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid var(--binance-yellow);
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
            display: inline-block;
            margin-right: 10px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .alert-custom {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="login-container">
                    <div class="row g-0">
                        <!-- Section gauche - Formulaire -->
                        <div class="col-md-6">
                            <div class="login-header">
                                <div class="binance-logo mb-3">
                                    <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
                                </div>
                                <h4>Connexion Sécurisée</h4>
                                <p class="mb-0">Accédez à votre espace de gestion</p>
                            </div>
                            
                            <div class="p-4">
                                <!-- Messages d'alerte -->
                                <div id="alertContainer"></div>
                                
                                <form id="loginForm" onsubmit="handleLogin(event)">
                                    <div class="mb-3">
                                        <label for="username" class="form-label fw-bold">
                                            <i class="bi bi-person-circle"></i> Nom d'utilisateur
                                        </label>
                                        <input type="text" class="form-control" id="username" name="username" 
                                               required autocomplete="username" placeholder="Entrez votre nom d'utilisateur">
                                    </div>
                                    
                                    <div class="mb-3">
                                        <label for="password" class="form-label fw-bold">
                                            <i class="bi bi-shield-lock"></i> Mot de passe
                                        </label>
                                        <input type="password" class="form-control" id="password" name="password" 
                                               required autocomplete="current-password" placeholder="Entrez votre mot de passe">
                                    </div>
                                    
                                    <div class="mb-3 form-check">
                                        <input type="checkbox" class="form-check-input" id="rememberMe">
                                        <label class="form-check-label" for="rememberMe">
                                            Se souvenir de moi
                                        </label>
                                    </div>
                                    
                                    <button type="submit" class="btn btn-login w-100 mb-3" id="loginBtn">
                                        <i class="bi bi-box-arrow-in-right"></i> Se connecter
                                    </button>
                                </form>
                                
                                <div class="text-center">
                                    <a href="#" class="text-decoration-none" onclick="showForgotPassword()">
                                        Mot de passe oublié ?
                                    </a>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Section droite - Comptes de test -->
                        <div class="col-md-6 bg-light">
                            <div class="p-4">
                                <h5 class="text-center mb-4">
                                    <i class="bi bi-info-circle text-primary"></i> Comptes de Test
                                </h5>
                                
                                <div class="demo-account" onclick="fillCredentials('admin', 'admin123')">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            <i class="bi bi-crown"></i>
                                        </div>
                                        <div>
                                            <h6 class="mb-1">👑 Administrateur</h6>
                                            <small class="text-muted">Accès complet au système</small>
                                            <div class="mt-1">
                                                <code>admin</code> / <code>admin123</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="demo-account" onclick="fillCredentials('marie.martin', 'vendeur123')">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            MM
                                        </div>
                                        <div>
                                            <h6 class="mb-1">👤 Marie Martin</h6>
                                            <small class="text-muted">Vendeur senior</small>
                                            <div class="mt-1">
                                                <code>marie.martin</code> / <code>vendeur123</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="demo-account" onclick="fillCredentials('pierre.durand', 'vendeur123')">
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-3">
                                            PD
                                        </div>
                                        <div>
                                            <h6 class="mb-1">👤 Pierre Durand</h6>
                                            <small class="text-muted">Vendeur junior</small>
                                            <div class="mt-1">
                                                <code>pierre.durand</code> / <code>vendeur123</code>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="alert alert-success alert-custom mt-4">
                                    <h6><i class="bi bi-check-circle"></i> Fonctionnalités</h6>
                                    <ul class="mb-0 small">
                                        <li>✅ Authentification simulée</li>
                                        <li>✅ Sessions utilisateur</li>
                                        <li>✅ Redirection automatique</li>
                                        <li>✅ Validation des données</li>
                                    </ul>
                                </div>
                                
                                <div class="text-center mt-3">
                                    <small class="text-muted">
                                        Cliquez sur un compte pour remplir automatiquement
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Base de données des utilisateurs (simulation)
        const users = {
            'admin': { password: 'admin123', role: 'admin', name: 'Administrateur' },
            'marie.martin': { password: 'vendeur123', role: 'vendeur', name: 'Marie Martin' },
            'pierre.durand': { password: 'vendeur123', role: 'vendeur', name: 'Pierre Durand' },
            'sophie.bernard': { password: 'vendeur123', role: 'vendeur', name: 'Sophie Bernard' }
        };
        
        function fillCredentials(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            
            // Animation de remplissage
            const usernameField = document.getElementById('username');
            const passwordField = document.getElementById('password');
            
            usernameField.style.background = 'rgba(241, 194, 50, 0.1)';
            passwordField.style.background = 'rgba(241, 194, 50, 0.1)';
            
            setTimeout(() => {
                usernameField.style.background = '';
                passwordField.style.background = '';
            }, 1000);
        }
        
        function showAlert(type, message) {
            const alertContainer = document.getElementById('alertContainer');
            const alertHtml = `
                <div class="alert alert-${type} alert-custom alert-dismissible fade show" role="alert">
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHtml;
            
            // Auto-dismiss après 5 secondes
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }
        
        function handleLogin(event) {
            event.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            
            // Validation côté client
            if (!username || !password) {
                showAlert('danger', '<i class="bi bi-exclamation-triangle"></i> Veuillez remplir tous les champs');
                return;
            }
            
            // Animation de chargement
            loginBtn.innerHTML = '<div class="loading-spinner"></div>Connexion en cours...';
            loginBtn.disabled = true;
            
            // Simulation d'authentification avec délai
            setTimeout(() => {
                if (users[username] && users[username].password === password) {
                    // Connexion réussie
                    const user = users[username];
                    
                    // Stocker les informations de session
                    sessionStorage.setItem('user', JSON.stringify({
                        username: username,
                        role: user.role,
                        name: user.name,
                        loginTime: new Date().toISOString()
                    }));
                    
                    showAlert('success', `<i class="bi bi-check-circle"></i> Connexion réussie ! Bienvenue ${user.name}`);
                    
                    // Redirection après 2 secondes
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 2000);
                    
                } else {
                    // Connexion échouée
                    showAlert('danger', '<i class="bi bi-x-circle"></i> Nom d\'utilisateur ou mot de passe incorrect');
                    
                    // Réinitialiser le bouton
                    loginBtn.innerHTML = '<i class="bi bi-box-arrow-in-right"></i> Se connecter';
                    loginBtn.disabled = false;
                    
                    // Vider le mot de passe
                    document.getElementById('password').value = '';
                    document.getElementById('password').focus();
                }
            }, 1500); // Délai de 1.5 secondes pour simuler l'authentification
        }
        
        function showForgotPassword() {
            showAlert('info', '<i class="bi bi-info-circle"></i> Contactez l\'administrateur pour réinitialiser votre mot de passe');
        }
        
        // Auto-focus sur le champ username
        document.getElementById('username').focus();
        
        // Gestion de la touche Entrée
        document.addEventListener('keypress', function(event) {
            if (event.key === 'Enter') {
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        });
        
        // Log de démarrage
        console.log('🔐 BINANCE CRM - Page de connexion chargée');
        console.log('👥 Comptes disponibles:', Object.keys(users));
    </script>
</body>
</html>

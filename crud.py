from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc
from passlib.context import Crypt<PERSON>ontext
from datetime import datetime, timedelta
from typing import Optional, List, Dict, Any
import json

import models
import schemas

# Configuration pour le hachage des mots de passe
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# ============ FONCTIONS UTILITAIRES ============

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Vérifie un mot de passe"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Hash un mot de passe"""
    return pwd_context.hash(password)

# ============ CRUD USERS ============

def get_user(db: Session, user_id: int) -> Optional[models.User]:
    """Récupère un utilisateur par ID"""
    return db.query(models.User).filter(models.User.id == user_id).first()

def get_user_by_username(db: Session, username: str) -> Optional[models.User]:
    """Récupère un utilisateur par nom d'utilisateur"""
    return db.query(models.User).filter(models.User.username == username).first()

def get_user_by_email(db: Session, email: str) -> Optional[models.User]:
    """Récupère un utilisateur par email"""
    return db.query(models.User).filter(models.User.email == email).first()

def get_users(db: Session, skip: int = 0, limit: int = 100) -> List[models.User]:
    """Récupère tous les utilisateurs"""
    return db.query(models.User).offset(skip).limit(limit).all()

def get_vendeurs(db: Session) -> List[models.User]:
    """Récupère tous les vendeurs"""
    return db.query(models.User).filter(models.User.role == "vendeur").all()

def create_user(db: Session, user: schemas.UserCreate) -> models.User:
    """Crée un nouvel utilisateur"""
    hashed_password = get_password_hash(user.password)
    db_user = models.User(
        username=user.username,
        email=user.email,
        password_hash=hashed_password,
        role=user.role
    )
    db.add(db_user)
    db.commit()
    db.refresh(db_user)
    return db_user

def update_user(db: Session, user_id: int, user_update: schemas.UserUpdate) -> Optional[models.User]:
    """Met à jour un utilisateur"""
    db_user = get_user(db, user_id)
    if not db_user:
        return None
    
    update_data = user_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_user, field, value)
    
    db.commit()
    db.refresh(db_user)
    return db_user

def authenticate_user(db: Session, username: str, password: str) -> Optional[models.User]:
    """Authentifie un utilisateur"""
    user = get_user_by_username(db, username)
    if not user or not verify_password(password, user.password_hash):
        return None
    return user

# ============ CRUD CLIENTS ============

def get_client(db: Session, client_id: int) -> Optional[models.Client]:
    """Récupère un client par ID"""
    return db.query(models.Client).filter(models.Client.id == client_id).first()

def get_clients(db: Session, skip: int = 0, limit: int = 100, vendeur_id: Optional[int] = None) -> List[models.Client]:
    """Récupère les clients avec filtrage optionnel par vendeur"""
    query = db.query(models.Client)
    if vendeur_id:
        query = query.filter(models.Client.vendeur_id == vendeur_id)
    return query.offset(skip).limit(limit).all()

def get_clients_by_filters(db: Session, 
                          vendeur_id: Optional[int] = None,
                          indicateur: Optional[str] = None,
                          email_envoye: Optional[bool] = None,
                          date_debut: Optional[datetime] = None,
                          date_fin: Optional[datetime] = None) -> List[models.Client]:
    """Récupère les clients avec filtres avancés"""
    query = db.query(models.Client)
    
    if vendeur_id:
        query = query.filter(models.Client.vendeur_id == vendeur_id)
    if indicateur:
        query = query.filter(models.Client.indicateur == indicateur)
    if email_envoye is not None:
        query = query.filter(models.Client.email_envoye == email_envoye)
    if date_debut:
        query = query.filter(models.Client.created_at >= date_debut)
    if date_fin:
        query = query.filter(models.Client.created_at <= date_fin)
    
    return query.all()

def get_clients_non_attribues(db: Session) -> List[models.Client]:
    """Récupère les clients non attribués"""
    return db.query(models.Client).filter(models.Client.vendeur_id.is_(None)).all()

def create_client(db: Session, client: schemas.ClientCreate) -> models.Client:
    """Crée un nouveau client"""
    db_client = models.Client(**client.dict())
    db.add(db_client)
    db.commit()
    db.refresh(db_client)
    return db_client

def update_client(db: Session, client_id: int, client_update: schemas.ClientUpdate) -> Optional[models.Client]:
    """Met à jour un client"""
    db_client = get_client(db, client_id)
    if not db_client:
        return None
    
    update_data = client_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_client, field, value)
    
    db_client.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_client)
    return db_client

def delete_client(db: Session, client_id: int) -> bool:
    """Supprime un client"""
    db_client = get_client(db, client_id)
    if not db_client:
        return False
    
    db.delete(db_client)
    db.commit()
    return True

def assign_clients_to_vendeur(db: Session, client_ids: List[int], vendeur_id: int) -> int:
    """Attribue plusieurs clients à un vendeur"""
    updated = db.query(models.Client).filter(
        models.Client.id.in_(client_ids)
    ).update(
        {models.Client.vendeur_id: vendeur_id, models.Client.updated_at: datetime.utcnow()},
        synchronize_session=False
    )
    db.commit()
    return updated

# ============ CRUD EMAIL TEMPLATES ============

def get_email_template(db: Session, template_id: int) -> Optional[models.EmailTemplate]:
    """Récupère un template par ID"""
    return db.query(models.EmailTemplate).filter(models.EmailTemplate.id == template_id).first()

def get_email_template_by_name(db: Session, nom: str) -> Optional[models.EmailTemplate]:
    """Récupère un template par nom"""
    return db.query(models.EmailTemplate).filter(models.EmailTemplate.nom == nom).first()

def get_email_templates(db: Session) -> List[models.EmailTemplate]:
    """Récupère tous les templates"""
    return db.query(models.EmailTemplate).all()

def create_email_template(db: Session, template: schemas.EmailTemplateCreate) -> models.EmailTemplate:
    """Crée un nouveau template"""
    db_template = models.EmailTemplate(**template.dict())
    db.add(db_template)
    db.commit()
    db.refresh(db_template)
    return db_template

def update_email_template(db: Session, template_id: int, template_update: schemas.EmailTemplateUpdate) -> Optional[models.EmailTemplate]:
    """Met à jour un template"""
    db_template = get_email_template(db, template_id)
    if not db_template:
        return None
    
    update_data = template_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_template, field, value)
    
    db_template.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_template)
    return db_template

def delete_email_template(db: Session, template_id: int) -> bool:
    """Supprime un template"""
    db_template = get_email_template(db, template_id)
    if not db_template:
        return False

    db.delete(db_template)
    db.commit()
    return True

# ============ CRUD APPOINTMENTS ============

def get_appointment(db: Session, appointment_id: int) -> Optional[models.Appointment]:
    """Récupère un rendez-vous par ID"""
    return db.query(models.Appointment).filter(models.Appointment.id == appointment_id).first()

def get_appointments(db: Session, vendeur_id: Optional[int] = None,
                    date_debut: Optional[datetime] = None,
                    date_fin: Optional[datetime] = None) -> List[models.Appointment]:
    """Récupère les rendez-vous avec filtres"""
    query = db.query(models.Appointment)

    if vendeur_id:
        query = query.filter(models.Appointment.vendeur_id == vendeur_id)
    if date_debut:
        query = query.filter(models.Appointment.date_rdv >= date_debut)
    if date_fin:
        query = query.filter(models.Appointment.date_rdv <= date_fin)

    return query.order_by(models.Appointment.date_rdv).all()

def get_appointments_by_client(db: Session, client_id: int) -> List[models.Appointment]:
    """Récupère les rendez-vous d'un client"""
    return db.query(models.Appointment).filter(
        models.Appointment.client_id == client_id
    ).order_by(models.Appointment.date_rdv).all()

def create_appointment(db: Session, appointment: schemas.AppointmentCreate) -> models.Appointment:
    """Crée un nouveau rendez-vous"""
    db_appointment = models.Appointment(**appointment.dict())
    db.add(db_appointment)
    db.commit()
    db.refresh(db_appointment)
    return db_appointment

def update_appointment(db: Session, appointment_id: int, appointment_update: schemas.AppointmentUpdate) -> Optional[models.Appointment]:
    """Met à jour un rendez-vous"""
    db_appointment = get_appointment(db, appointment_id)
    if not db_appointment:
        return None

    update_data = appointment_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        setattr(db_appointment, field, value)

    db_appointment.updated_at = datetime.utcnow()
    db.commit()
    db.refresh(db_appointment)
    return db_appointment

def delete_appointment(db: Session, appointment_id: int) -> bool:
    """Supprime un rendez-vous"""
    db_appointment = get_appointment(db, appointment_id)
    if not db_appointment:
        return False

    db.delete(db_appointment)
    db.commit()
    return True

# ============ CRUD EMAIL LOGS ============

def create_email_log(db: Session, email_log: schemas.EmailLogCreate) -> models.EmailLog:
    """Crée un log d'email"""
    db_log = models.EmailLog(**email_log.dict())
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log

def get_email_logs(db: Session, vendeur_id: Optional[int] = None,
                  client_id: Optional[int] = None,
                  limit: int = 100) -> List[models.EmailLog]:
    """Récupère les logs d'emails"""
    query = db.query(models.EmailLog)

    if vendeur_id:
        query = query.filter(models.EmailLog.vendeur_id == vendeur_id)
    if client_id:
        query = query.filter(models.EmailLog.client_id == client_id)

    return query.order_by(desc(models.EmailLog.date_envoi)).limit(limit).all()

# ============ CRUD SMTP CONFIG ============

def get_smtp_config(db: Session) -> Optional[models.SMTPConfig]:
    """Récupère la configuration SMTP active"""
    return db.query(models.SMTPConfig).first()

def create_or_update_smtp_config(db: Session, smtp_config: schemas.SMTPConfigCreate) -> models.SMTPConfig:
    """Crée ou met à jour la configuration SMTP"""
    db_config = get_smtp_config(db)

    if db_config:
        # Mise à jour
        update_data = smtp_config.dict()
        for field, value in update_data.items():
            setattr(db_config, field, value)
        db_config.updated_at = datetime.utcnow()
    else:
        # Création
        db_config = models.SMTPConfig(**smtp_config.dict())
        db.add(db_config)

    db.commit()
    db.refresh(db_config)
    return db_config

# ============ CRUD ACTION LOGS ============

def create_action_log(db: Session, user_id: int, action: str, details: Dict[str, Any]) -> models.ActionLog:
    """Crée un log d'action"""
    db_log = models.ActionLog(
        user_id=user_id,
        action=action,
        details=json.dumps(details, default=str)
    )
    db.add(db_log)
    db.commit()
    db.refresh(db_log)
    return db_log

def get_action_logs(db: Session, user_id: Optional[int] = None, limit: int = 100) -> List[models.ActionLog]:
    """Récupère les logs d'actions"""
    query = db.query(models.ActionLog)

    if user_id:
        query = query.filter(models.ActionLog.user_id == user_id)

    return query.order_by(desc(models.ActionLog.timestamp)).limit(limit).all()

# ============ STATISTIQUES ============

def get_dashboard_stats(db: Session, vendeur_id: Optional[int] = None) -> Dict[str, Any]:
    """Récupère les statistiques pour le dashboard"""
    stats = {}

    # Requête de base pour les clients
    client_query = db.query(models.Client)
    if vendeur_id:
        client_query = client_query.filter(models.Client.vendeur_id == vendeur_id)

    stats['total_clients'] = client_query.count()
    stats['clients_attribues'] = client_query.filter(models.Client.vendeur_id.isnot(None)).count()
    stats['clients_non_attribues'] = client_query.filter(models.Client.vendeur_id.is_(None)).count()
    stats['emails_envoyes'] = client_query.filter(models.Client.email_envoye == True).count()

    # Statistiques des rendez-vous
    rdv_query = db.query(models.Appointment)
    if vendeur_id:
        rdv_query = rdv_query.filter(models.Appointment.vendeur_id == vendeur_id)

    stats['rdv_planifies'] = rdv_query.filter(models.Appointment.statut == "planifie").count()
    stats['rdv_realises'] = rdv_query.filter(models.Appointment.statut == "realise").count()

    # Statistiques par indicateur
    indicateur_stats = db.query(
        models.Client.indicateur,
        func.count(models.Client.id).label('count')
    )
    if vendeur_id:
        indicateur_stats = indicateur_stats.filter(models.Client.vendeur_id == vendeur_id)

    stats['indicateurs'] = {
        row.indicateur: row.count
        for row in indicateur_stats.group_by(models.Client.indicateur).all()
    }

    return stats

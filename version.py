"""
Informations de version pour l'application CRM
"""

__version__ = "1.0.0"
__build__ = "20231220"
__author__ = "CRM Development Team"
__email__ = "<EMAIL>"
__description__ = "Système CRM complet avec gestion des leads, emails et rendez-vous"

# Historique des versions
VERSION_HISTORY = {
    "1.0.0": {
        "date": "2023-12-20",
        "description": "Version initiale complète",
        "features": [
            "Authentification avec rôles (admin/vendeur)",
            "Gestion complète des clients/leads",
            "Système d'emails avec templates",
            "Agenda et gestion des rendez-vous",
            "Import/export CSV avancé",
            "Statistiques et rapports détaillés",
            "Interface responsive Bootstrap 5",
            "API REST complète",
            "Sécurité renforcée",
            "Logs et monitoring",
            "Documentation complète"
        ],
        "technical": [
            "FastAPI 0.104+",
            "SQLAlchemy 2.0",
            "PostgreSQL/SQLite support",
            "Jinja2 templates",
            "Bootstrap 5 + Chart.js",
            "Docker support",
            "Middleware de sécurité",
            "Tests automatisés",
            "Déploiement multi-environnement"
        ]
    }
}

# Configuration des fonctionnalités
FEATURES = {
    "authentication": True,
    "client_management": True,
    "email_system": True,
    "appointment_system": True,
    "import_export": True,
    "statistics": True,
    "api": True,
    "security": True,
    "logging": True,
    "multi_language": False,  # Future feature
    "mobile_app": False,      # Future feature
    "integrations": False     # Future feature
}

# Limites de l'application
LIMITS = {
    "max_clients_per_vendeur": 1000,
    "max_email_per_hour": 100,
    "max_file_upload_size": 50 * 1024 * 1024,  # 50MB
    "max_template_size": 100 * 1024,  # 100KB
    "session_timeout": 3600,  # 1 heure
    "max_login_attempts": 5,
    "lockout_duration": 900   # 15 minutes
}

# Configuration par défaut
DEFAULT_CONFIG = {
    "items_per_page": 50,
    "timezone": "Europe/Paris",
    "date_format": "%d/%m/%Y",
    "datetime_format": "%d/%m/%Y %H:%M",
    "language": "fr",
    "theme": "default"
}

def get_version_info():
    """Retourne les informations de version"""
    return {
        "version": __version__,
        "build": __build__,
        "author": __author__,
        "description": __description__,
        "features": FEATURES,
        "limits": LIMITS
    }

def check_compatibility(required_version: str) -> bool:
    """Vérifie la compatibilité avec une version requise"""
    current = tuple(map(int, __version__.split('.')))
    required = tuple(map(int, required_version.split('.')))
    return current >= required

def get_changelog():
    """Retourne l'historique des versions"""
    return VERSION_HISTORY

#!/usr/bin/env python3
"""
BINANCE CRM - Serveur Base de Données Réel
API REST complète avec base de données SQLite
"""

import json
import sqlite3
import hashlib
import uuid
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import threading
import time
import queue
from contextlib import contextmanager
import gzip
import io

class ConnectionPool:
    """Pool de connexions optimisé pour SQLite avec cache"""
    def __init__(self, db_path, max_connections=20):  # Augmenté de 10 à 20
        self.db_path = db_path
        self.max_connections = max_connections
        self.pool = queue.Queue(maxsize=max_connections)
        self.lock = threading.Lock()
        self.cache = {}  # Cache en mémoire pour données statiques
        self.cache_ttl = {}  # TTL pour le cache
        self.cache_duration = 300  # 5 minutes par défaut
        self._initialize_pool()

    def _initialize_pool(self):
        """Initialiser le pool avec des connexions optimisées"""
        for _ in range(self.max_connections):
            conn = sqlite3.connect(self.db_path, check_same_thread=False)
            conn.row_factory = sqlite3.Row

            # Optimisations SQLite avancées
            conn.execute("PRAGMA journal_mode = WAL")
            conn.execute("PRAGMA synchronous = NORMAL")
            conn.execute("PRAGMA cache_size = 5000")  # Augmenté de 2000 à 5000
            conn.execute("PRAGMA temp_store = MEMORY")
            conn.execute("PRAGMA mmap_size = 536870912")  # Augmenté à 512MB
            conn.execute("PRAGMA busy_timeout = 5000")  # Timeout de 5 secondes
            conn.execute("PRAGMA foreign_keys = ON")  # Assurer l'intégrité

            self.pool.put(conn)

    @contextmanager
    def get_connection(self):
        """Context manager pour obtenir une connexion"""
        conn = self.pool.get()
        try:
            yield conn
        finally:
            self.pool.put(conn)

    def get_cached_data(self, cache_key):
        """Récupérer des données du cache"""
        with self.lock:
            if cache_key in self.cache and time.time() < self.cache_ttl.get(cache_key, 0):
                return self.cache[cache_key]
        return None

    def set_cached_data(self, cache_key, data, ttl=None):
        """Mettre en cache des données"""
        with self.lock:
            self.cache[cache_key] = data
            self.cache_ttl[cache_key] = time.time() + (ttl or self.cache_duration)

    def invalidate_cache(self, cache_key=None):
        """Invalider le cache"""
        with self.lock:
            if cache_key:
                if cache_key in self.cache:
                    del self.cache[cache_key]
                    del self.cache_ttl[cache_key]
            else:
                self.cache.clear()
                self.cache_ttl.clear()

    def close_all(self):
        """Fermer toutes les connexions"""
        while not self.pool.empty():
            conn = self.pool.get()
            conn.close()

class DatabaseManager:
    def __init__(self, db_path='binance_crm.db'):
        self.db_path = db_path
        self.connection_pool = ConnectionPool(db_path, max_connections=10)
        self.init_database()
        self.init_default_data()

    def get_connection(self):
        """Obtenir une connexion à la base de données (legacy)"""
        conn = sqlite3.connect(self.db_path, check_same_thread=False)
        conn.row_factory = sqlite3.Row
        return conn
    
    def init_database(self):
        """Initialiser la structure de la base de données"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Table utilisateurs (étendue pour les vendeurs)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                first_name TEXT,
                last_name TEXT,
                phone TEXT,
                role TEXT DEFAULT 'vendeur',
                status TEXT DEFAULT 'actif',
                territory TEXT,
                hire_date TEXT,
                monthly_target INTEGER DEFAULT 0,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # Table clients (adaptée au format CSV)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT,
                last_name TEXT,
                email TEXT UNIQUE,
                phone TEXT,
                birth_date TEXT,
                address TEXT,
                postal_code TEXT,
                city TEXT,
                status TEXT DEFAULT 'prospect',
                assigned_to INTEGER,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (assigned_to) REFERENCES users (id)
            )
        ''')
        
        # Table templates emails
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                subject TEXT NOT NULL,
                content TEXT NOT NULL,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Table historique emails
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_campaigns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_id INTEGER,
                subject TEXT,
                recipients_count INTEGER,
                sent_by INTEGER,
                sent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'sent',
                delivered INTEGER DEFAULT 0,
                opened INTEGER DEFAULT 0,
                clicked INTEGER DEFAULT 0,
                bounced INTEGER DEFAULT 0,
                FOREIGN KEY (template_id) REFERENCES email_templates (id),
                FOREIGN KEY (sent_by) REFERENCES users (id)
            )
        ''')
        
        # Table sessions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                session_token TEXT UNIQUE,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_date TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Table rapports générés
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS generated_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_type TEXT,
                title TEXT,
                filename TEXT,
                generated_by INTEGER,
                generated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                file_size INTEGER,
                FOREIGN KEY (generated_by) REFERENCES users (id)
            )
        ''')

        # Table pour l'historique des imports CSV
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS import_history (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                filename TEXT,
                total_rows INTEGER,
                successful_rows INTEGER,
                error_rows INTEGER,
                skipped_rows INTEGER,
                import_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                user_id INTEGER,
                status TEXT DEFAULT 'completed',
                error_details TEXT,
                can_undo BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')

        # Table pour tracer les clients importés (pour l'undo)
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS imported_clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                import_id INTEGER,
                client_id INTEGER,
                FOREIGN KEY (import_id) REFERENCES import_history (id),
                FOREIGN KEY (client_id) REFERENCES clients (id)
            )
        ''')

        # Créer les index optimisés pour les performances
        performance_indexes = [
            # Index pour la table clients (optimisés)
            "CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email)",
            "CREATE INDEX IF NOT EXISTS idx_clients_status ON clients(status)",
            "CREATE INDEX IF NOT EXISTS idx_clients_assigned_to ON clients(assigned_to)",
            "CREATE INDEX IF NOT EXISTS idx_clients_created_date ON clients(created_date)",
            "CREATE INDEX IF NOT EXISTS idx_clients_last_activity ON clients(last_activity)",
            "CREATE INDEX IF NOT EXISTS idx_clients_phone ON clients(phone)",
            "CREATE INDEX IF NOT EXISTS idx_clients_city ON clients(city)",
            "CREATE INDEX IF NOT EXISTS idx_clients_postal_code ON clients(postal_code)",

            # Index pour la table users (optimisés)
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
            "CREATE INDEX IF NOT EXISTS idx_users_status ON users(status)",
            "CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)",
            "CREATE INDEX IF NOT EXISTS idx_users_last_login ON users(last_login)",

            # Index pour la table import_history
            "CREATE INDEX IF NOT EXISTS idx_import_history_date ON import_history(import_date)",
            "CREATE INDEX IF NOT EXISTS idx_import_history_user ON import_history(user_id)",
            "CREATE INDEX IF NOT EXISTS idx_import_history_status ON import_history(status)",

            # Index pour la table imported_clients
            "CREATE INDEX IF NOT EXISTS idx_imported_clients_import ON imported_clients(import_id)",
            "CREATE INDEX IF NOT EXISTS idx_imported_clients_client ON imported_clients(client_id)",

            # Index pour les templates et campagnes
            "CREATE INDEX IF NOT EXISTS idx_email_templates_name ON email_templates(name)",
            "CREATE INDEX IF NOT EXISTS idx_email_campaigns_sent_date ON email_campaigns(sent_date)",
            "CREATE INDEX IF NOT EXISTS idx_email_campaigns_template ON email_campaigns(template_id)",

            # Index composites pour les requêtes fréquentes (optimisés)
            "CREATE INDEX IF NOT EXISTS idx_clients_status_assigned ON clients(status, assigned_to)",
            "CREATE INDEX IF NOT EXISTS idx_clients_status_created ON clients(status, created_date)",
            "CREATE INDEX IF NOT EXISTS idx_users_role_status ON users(role, status)",
            "CREATE INDEX IF NOT EXISTS idx_clients_assigned_status_created ON clients(assigned_to, status, created_date)",

            # Index pour recherche textuelle
            "CREATE INDEX IF NOT EXISTS idx_clients_name_search ON clients(first_name, last_name)",
            "CREATE INDEX IF NOT EXISTS idx_clients_email_domain ON clients(substr(email, instr(email, '@')))"
        ]

        for index_query in performance_indexes:
            try:
                cursor.execute(index_query)
            except sqlite3.Error:
                pass  # Index peut déjà exister

        conn.commit()
        conn.close()
    
    def init_default_data(self):
        """Initialiser les données par défaut"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Vérifier si des utilisateurs existent déjà
        cursor.execute('SELECT COUNT(*) FROM users')
        if cursor.fetchone()[0] == 0:
            # Créer les utilisateurs par défaut
            users = [
                ('admin', 'admin123', '<EMAIL>', 'Admin', 'Système', 'admin'),
                ('marie.martin', 'vendeur123', '<EMAIL>', 'Marie', 'Martin', 'vendeur'),
                ('pierre.durand', 'vendeur123', '<EMAIL>', 'Pierre', 'Durand', 'vendeur'),
                ('sophie.bernard', 'vendeur123', '<EMAIL>', 'Sophie', 'Bernard', 'vendeur')
            ]
            
            for username, password, email, first_name, last_name, role in users:
                password_hash = hashlib.sha256(password.encode()).hexdigest()
                cursor.execute('''
                    INSERT INTO users (username, password_hash, email, first_name, last_name, role)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (username, password_hash, email, first_name, last_name, role))
        
        # Vérifier si des clients existent déjà
        cursor.execute('SELECT COUNT(*) FROM clients')
        if cursor.fetchone()[0] == 0:
            # Créer des clients par défaut
            clients = [
                ('Jean', 'Dupont', '<EMAIL>', '+33123456789', 'nouveau', 2),
                ('Marie', 'Dubois', '<EMAIL>', '+33123456790', 'actif', 2),
                ('Pierre', 'Moreau', '<EMAIL>', '+33123456791', 'en_cours', 3),
                ('Sophie', 'Leroy', '<EMAIL>', '+33123456792', 'actif', 3),
                ('Paul', 'Roux', '<EMAIL>', '+33123456793', 'nouveau', 4)
            ]
            
            for first_name, last_name, email, phone, status, assigned_to in clients:
                cursor.execute('''
                    INSERT INTO clients (first_name, last_name, email, phone, status, assigned_to)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (first_name, last_name, email, phone, status, assigned_to))
        
        # Templates emails par défaut
        cursor.execute('SELECT COUNT(*) FROM email_templates')
        if cursor.fetchone()[0] == 0:
            templates = [
                ('rdv', 'Confirmation de votre RDV - Binance', '<h1>Confirmation RDV</h1><p>Bonjour {{prenom}}, votre rendez-vous avec {{vendeur}} est confirmé.</p>'),
                ('wireguard', '✅ [Binance] WireGuard IP Key Successfully Generated', '<h1>WireGuard IP Key Generated</h1><p>Bonjour {{prenom}}, votre IP de protection WireGuard a été générée : {{trusted_ip}}</p>'),
                ('security_alert', '⚠️ [Binance] Nouvelle connexion détectée – {{timestamp_utc}}', '<h1>Nouvelle Connexion Détectée</h1><p>Bonjour {{prenom}}, une connexion depuis {{device_name}} ({{ip_address}}) a été détectée à {{timestamp_utc}}.</p>')
            ]

            for name, subject, content in templates:
                cursor.execute('''
                    INSERT INTO email_templates (name, subject, content, created_by)
                    VALUES (?, ?, ?, 1)
                ''', (name, subject, content))
        
        conn.commit()
        conn.close()
    
    def authenticate_user(self, username, password):
        """Authentifier un utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            SELECT * FROM users WHERE username = ? AND password_hash = ?
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        if user:
            # Mettre à jour la dernière connexion
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            ''', (user['id'],))
            
            # Créer une session
            session_token = str(uuid.uuid4())
            expires_date = datetime.now() + timedelta(hours=24)
            
            cursor.execute('''
                INSERT INTO user_sessions (user_id, session_token, expires_date)
                VALUES (?, ?, ?)
            ''', (user['id'], session_token, expires_date))
            
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'user': dict(user),
                'session_token': session_token
            }
        
        conn.close()
        return {'success': False, 'error': 'Identifiants invalides'}
    
    def validate_session(self, session_token):
        """Valider une session utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT u.*, s.expires_date FROM users u
            JOIN user_sessions s ON u.id = s.user_id
            WHERE s.session_token = ? AND s.is_active = 1 AND s.expires_date > CURRENT_TIMESTAMP
        ''', (session_token,))
        
        user = cursor.fetchone()
        conn.close()
        
        return dict(user) if user else None
    
    def get_clients(self, user_id=None, filters=None):
        """Récupérer la liste des clients avec cache et optimisations"""
        # Créer une clé de cache basée sur les paramètres
        cache_key = f"clients_{user_id}_{str(filters)}"

        # Vérifier le cache pour les requêtes sans recherche (données statiques)
        if not (filters and filters.get('search')):
            cached_data = self.pool.get_cached_data(cache_key)
            if cached_data is not None:
                return cached_data

        with self.pool.get_connection() as conn:
            cursor = conn.cursor()

            # Optimisation: Sélectionner uniquement les colonnes nécessaires
            query = '''
                SELECT c.id, c.first_name, c.last_name, c.email, c.phone, c.status,
                       c.created_date, c.last_activity, c.assigned_to,
                       u.first_name as vendeur_prenom, u.last_name as vendeur_nom
                FROM clients c
                LEFT JOIN users u ON c.assigned_to = u.id
            '''
            params = []

            # Optimisation: Utiliser les index composites
            if user_id and filters and filters.get('assigned_only'):
                query += ' WHERE c.assigned_to = ?'
                params.append(user_id)

            if filters:
                if filters.get('status'):
                    query += ' AND c.status = ?' if 'WHERE' in query else ' WHERE c.status = ?'
                    params.append(filters['status'])

                if filters.get('search'):
                    # Optimisation: Recherche plus complète avec index
                    search_condition = ''' AND (
                        c.first_name LIKE ? OR c.last_name LIKE ? OR
                        c.email LIKE ? OR c.phone LIKE ? OR
                        (c.first_name || ' ' || c.last_name) LIKE ?
                    )'''
                    if 'WHERE' not in query:
                        search_condition = search_condition.replace(' AND ', ' WHERE ')
                    query += search_condition
                    search_term = f"%{filters['search']}%"
                    params.extend([search_term, search_term, search_term, search_term, search_term])

                # Optimisation: Pagination
                if filters.get('limit'):
                    limit = int(filters.get('limit', 50))
                    offset = int(filters.get('offset', 0))
                    query += f' ORDER BY c.created_date DESC LIMIT {limit} OFFSET {offset}'
                else:
                    query += ' ORDER BY c.created_date DESC'
            else:
                query += ' ORDER BY c.created_date DESC'

            cursor.execute(query, params)
            clients = [dict(row) for row in cursor.fetchall()]

            # Mettre en cache les résultats pour les requêtes sans recherche
            if not (filters and filters.get('search')):
                self.pool.set_cached_data(cache_key, clients, ttl=60)  # Cache 1 minute

            return clients
    
    def create_client(self, client_data):
        """Créer un nouveau client avec tous les champs CSV"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO clients (
                    first_name, last_name, email, phone, birth_date,
                    address, postal_code, city, status, assigned_to, notes
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                client_data.get('first_name', ''),
                client_data.get('last_name', ''),
                client_data.get('email', ''),
                client_data.get('phone', ''),
                client_data.get('birth_date', ''),
                client_data.get('address', ''),
                client_data.get('postal_code', ''),
                client_data.get('city', ''),
                client_data.get('status', 'prospect'),
                client_data.get('assigned_to'),
                client_data.get('notes', '')
            ))
            
            client_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return {'success': True, 'client_id': client_id}
            
        except sqlite3.IntegrityError:
            conn.close()
            return {'success': False, 'error': 'Email déjà utilisé'}
        except Exception as e:
            conn.close()
            return {'success': False, 'error': str(e)}
    
    def update_client(self, client_id, client_data):
        """Mettre à jour un client"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                UPDATE clients
                SET first_name = ?, last_name = ?, email = ?, phone = ?, birth_date = ?,
                    address = ?, postal_code = ?, city = ?, status = ?, assigned_to = ?,
                    notes = ?, last_activity = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                client_data.get('first_name', ''),
                client_data.get('last_name', ''),
                client_data.get('email', ''),
                client_data.get('phone', ''),
                client_data.get('birth_date', ''),
                client_data.get('address', ''),
                client_data.get('postal_code', ''),
                client_data.get('city', ''),
                client_data.get('status', 'prospect'),
                client_data.get('assigned_to'),
                client_data.get('notes', ''),
                client_id
            ))
            
            conn.commit()
            conn.close()
            
            return {'success': True}
            
        except Exception as e:
            conn.close()
            return {'success': False, 'error': str(e)}
    
    def delete_client(self, client_id):
        """Supprimer un client"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM clients WHERE id = ?', (client_id,))
        conn.commit()
        conn.close()
        
        return {'success': True}
    
    def get_users(self):
        """Récupérer la liste des utilisateurs"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, first_name, last_name, role, status, 
                   created_date, last_login FROM users
            ORDER BY created_date DESC
        ''')
        
        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return users
    
    def get_email_templates(self):
        """Récupérer les templates d'emails"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT et.*, u.first_name, u.last_name 
            FROM email_templates et
            LEFT JOIN users u ON et.created_by = u.id
            ORDER BY et.name
        ''')
        
        templates = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return templates
    
    def save_email_template(self, template_data):
        """Sauvegarder un template d'email"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO email_templates (name, subject, content, created_by, modified_date)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                template_data['name'],
                template_data['subject'],
                template_data['content'],
                template_data.get('created_by', 1)
            ))
            
            conn.commit()
            conn.close()
            
            return {'success': True}
            
        except Exception as e:
            conn.close()
            return {'success': False, 'error': str(e)}
    
    def save_email_campaign(self, campaign_data):
        """Sauvegarder une campagne email"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO email_campaigns 
            (template_id, subject, recipients_count, sent_by, status, delivered, opened, clicked, bounced)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            campaign_data.get('template_id'),
            campaign_data['subject'],
            campaign_data['recipients_count'],
            campaign_data['sent_by'],
            campaign_data.get('status', 'sent'),
            campaign_data.get('delivered', 0),
            campaign_data.get('opened', 0),
            campaign_data.get('clicked', 0),
            campaign_data.get('bounced', 0)
        ))
        
        campaign_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return {'success': True, 'campaign_id': campaign_id}
    
    def get_email_campaigns(self):
        """Récupérer l'historique des campagnes"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ec.*, et.name as template_name, u.first_name, u.last_name
            FROM email_campaigns ec
            LEFT JOIN email_templates et ON ec.template_id = et.id
            LEFT JOIN users u ON ec.sent_by = u.id
            ORDER BY ec.sent_date DESC
            LIMIT 50
        ''')
        
        campaigns = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return campaigns
    
    def get_dashboard_stats(self, user_id=None):
        """Récupérer les statistiques du dashboard"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        stats = {}
        
        # Nombre total de clients
        if user_id:
            cursor.execute('SELECT COUNT(*) FROM clients WHERE assigned_to = ?', (user_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM clients')
        stats['total_clients'] = cursor.fetchone()[0]
        
        # Clients actifs
        if user_id:
            cursor.execute('SELECT COUNT(*) FROM clients WHERE status = "actif" AND assigned_to = ?', (user_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM clients WHERE status = "actif"')
        stats['active_clients'] = cursor.fetchone()[0]
        
        # Emails envoyés (derniers 30 jours)
        cursor.execute('''
            SELECT COALESCE(SUM(recipients_count), 0) FROM email_campaigns 
            WHERE sent_date > datetime('now', '-30 days')
        ''')
        stats['emails_sent'] = cursor.fetchone()[0]
        
        # Nombre de vendeurs actifs
        cursor.execute('SELECT COUNT(*) FROM users WHERE role = "vendeur" AND status = "actif"')
        stats['active_sellers'] = cursor.fetchone()[0]
        
        conn.close()
        return stats

    def get_csv_template(self):
        """Générer un template CSV avec exemples"""
        import io

        # En-têtes CSV
        headers = ['nom', 'prenom', 'email', 'telephone', 'naissance', 'adresse', 'code_postal', 'ville', 'vendeur', 'statut']

        # Données d'exemple
        sample_data = [
            ['PORTE', 'KYLLIAN', '<EMAIL>', '0749530760', '2008-02-07', '12 PLACE DE L YSER', '62670', 'MAZINGARBE', 'Marie Martin', 'prospect'],
            ['DUPONT', 'Jean', '<EMAIL>', '0123456789', '1985-12-15', '5 rue de la Paix', '75001', 'PARIS', '', 'client'],
            ['MARTIN', 'Sophie', '<EMAIL>', '0987654321', '1990-03-22', '10 avenue des Champs', '75008', 'PARIS', 'Pierre Durand', 'prospect']
        ]

        # Générer le CSV
        output = io.StringIO()
        output.write(';'.join(headers) + '\n')
        for row in sample_data:
            output.write(';'.join(row) + '\n')

        csv_content = output.getvalue()
        output.close()

        return {
            'success': True,
            'content': csv_content,
            'filename': 'template_import_clients.csv',
            'content_type': 'text/csv; charset=utf-8'
        }

    def validate_csv_data(self, data):
        """Valider les données CSV avant import"""
        import re
        from datetime import datetime

        csv_data = data.get('csv_data', [])
        errors = []
        warnings = []
        valid_rows = 0

        # Récupérer la liste des vendeurs existants
        conn = self.get_connection()
        cursor = conn.cursor()
        cursor.execute('SELECT first_name, last_name FROM users WHERE role = "vendeur"')
        vendeurs = [f"{row[0]} {row[1]}" for row in cursor.fetchall()]

        # Récupérer les emails existants
        cursor.execute('SELECT email FROM clients')
        existing_emails = [row[0].lower() for row in cursor.fetchall()]
        conn.close()

        for i, row in enumerate(csv_data, 1):
            row_errors = []

            # Validation email
            if row.get('email'):
                email = row['email'].strip().lower()
                if not re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                    row_errors.append(f"Email invalide: {email}")
                elif email in existing_emails:
                    warnings.append(f"Ligne {i}: Email déjà existant: {email}")

            # Validation téléphone
            if row.get('telephone'):
                phone = re.sub(r'[^\d]', '', row['telephone'])
                if not re.match(r'^0\d{9}$', phone):
                    row_errors.append(f"Téléphone invalide: {row['telephone']} (doit être 10 chiffres commençant par 0)")

            # Validation date de naissance
            if row.get('naissance'):
                birth_date = row['naissance'].strip()
                valid_date = False
                for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y']:
                    try:
                        datetime.strptime(birth_date, fmt)
                        valid_date = True
                        break
                    except ValueError:
                        continue
                if not valid_date:
                    row_errors.append(f"Date de naissance invalide: {birth_date} (formats acceptés: YYYY-MM-DD, DD/MM/YYYY, DD-MM-YYYY)")

            # Validation code postal
            if row.get('code_postal'):
                if not re.match(r'^\d{5}$', row['code_postal'].strip()):
                    row_errors.append(f"Code postal invalide: {row['code_postal']} (doit être 5 chiffres)")

            # Validation vendeur
            if row.get('vendeur'):
                vendeur = row['vendeur'].strip()
                if vendeur and vendeur not in vendeurs:
                    row_errors.append(f"Vendeur inexistant: {vendeur}")

            if row_errors:
                errors.append({
                    'row': i,
                    'errors': row_errors,
                    'data': row
                })
            else:
                valid_rows += 1

        return {
            'success': True,
            'total_rows': len(csv_data),
            'valid_rows': valid_rows,
            'error_rows': len(errors),
            'errors': errors,
            'warnings': warnings
        }

    def import_clients_csv(self, data):
        """Importer les clients depuis les données CSV"""
        import re
        from datetime import datetime

        csv_data = data.get('csv_data', [])
        user_id = data.get('user_id', 1)
        skip_errors = data.get('skip_errors', True)

        if not csv_data:
            return {'success': False, 'error': 'Aucune donnée CSV fournie'}

        conn = self.get_connection()
        cursor = conn.cursor()

        # Créer un enregistrement d'import
        cursor.execute('''
            INSERT INTO import_history (filename, total_rows, user_id, status)
            VALUES (?, ?, ?, 'in_progress')
        ''', (data.get('filename', 'import.csv'), len(csv_data), user_id))

        import_id = cursor.lastrowid

        # Récupérer les vendeurs existants
        cursor.execute('SELECT id, first_name, last_name FROM users WHERE role = "vendeur"')
        vendeurs_map = {f"{row[1]} {row[2]}": row[0] for row in cursor.fetchall()}

        successful_imports = []
        errors = []
        skipped = []

        for i, row in enumerate(csv_data, 1):
            try:
                # Préparer les données
                client_data = {}

                # Nom et prénom
                if row.get('nom'):
                    client_data['last_name'] = row['nom'].strip()
                if row.get('prenom'):
                    client_data['first_name'] = row['prenom'].strip()

                # Email
                if row.get('email'):
                    email = row['email'].strip().lower()
                    if re.match(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$', email):
                        # Vérifier si l'email existe déjà
                        cursor.execute('SELECT id FROM clients WHERE email = ?', (email,))
                        if cursor.fetchone():
                            if skip_errors:
                                skipped.append({'row': i, 'reason': f'Email déjà existant: {email}'})
                                continue
                            else:
                                errors.append({'row': i, 'error': f'Email déjà existant: {email}'})
                                continue
                        client_data['email'] = email

                # Téléphone
                if row.get('telephone'):
                    phone = re.sub(r'[^\d]', '', row['telephone'])
                    if re.match(r'^0\d{9}$', phone):
                        client_data['phone'] = phone

                # Date de naissance
                if row.get('naissance'):
                    birth_date = row['naissance'].strip()
                    for fmt in ['%Y-%m-%d', '%d/%m/%Y', '%d-%m-%Y']:
                        try:
                            parsed_date = datetime.strptime(birth_date, fmt)
                            client_data['birth_date'] = parsed_date.strftime('%Y-%m-%d')
                            break
                        except ValueError:
                            continue

                # Adresse
                if row.get('adresse'):
                    client_data['address'] = row['adresse'].strip()
                if row.get('code_postal'):
                    if re.match(r'^\d{5}$', row['code_postal'].strip()):
                        client_data['postal_code'] = row['code_postal'].strip()
                if row.get('ville'):
                    client_data['city'] = row['ville'].strip()

                # Vendeur assigné
                assigned_to = None
                if row.get('vendeur'):
                    vendeur = row['vendeur'].strip()
                    if vendeur in vendeurs_map:
                        assigned_to = vendeurs_map[vendeur]

                # Statut
                status = row.get('statut', 'prospect').strip().lower()
                if status not in ['prospect', 'client', 'actif', 'inactif']:
                    status = 'prospect'

                # Insérer le client
                cursor.execute('''
                    INSERT INTO clients (
                        first_name, last_name, email, phone, birth_date,
                        address, postal_code, city, status, assigned_to
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    client_data.get('first_name'),
                    client_data.get('last_name'),
                    client_data.get('email'),
                    client_data.get('phone'),
                    client_data.get('birth_date'),
                    client_data.get('address'),
                    client_data.get('postal_code'),
                    client_data.get('city'),
                    status,
                    assigned_to
                ))

                client_id = cursor.lastrowid

                # Enregistrer la relation import-client
                cursor.execute('''
                    INSERT INTO imported_clients (import_id, client_id)
                    VALUES (?, ?)
                ''', (import_id, client_id))

                successful_imports.append(client_id)

            except Exception as e:
                error_msg = str(e)
                if skip_errors:
                    errors.append({'row': i, 'error': error_msg})
                else:
                    # Rollback et retourner l'erreur
                    conn.rollback()
                    conn.close()
                    return {'success': False, 'error': f'Erreur ligne {i}: {error_msg}'}

        # Mettre à jour l'historique d'import
        cursor.execute('''
            UPDATE import_history
            SET successful_rows = ?, error_rows = ?, skipped_rows = ?, status = 'completed'
            WHERE id = ?
        ''', (len(successful_imports), len(errors), len(skipped), import_id))

        conn.commit()
        conn.close()

        return {
            'success': True,
            'import_id': import_id,
            'total_rows': len(csv_data),
            'successful_rows': len(successful_imports),
            'error_rows': len(errors),
            'skipped_rows': len(skipped),
            'errors': errors,
            'skipped': skipped
        }

    def get_import_history(self):
        """Récupérer l'historique des imports"""
        conn = self.get_connection()
        cursor = conn.cursor()

        cursor.execute('''
            SELECT ih.*, u.first_name, u.last_name
            FROM import_history ih
            LEFT JOIN users u ON ih.user_id = u.id
            ORDER BY ih.import_date DESC
            LIMIT 50
        ''')

        history = [dict(row) for row in cursor.fetchall()]
        conn.close()

        return history

    def undo_import(self, import_id):
        """Annuler un import récent"""
        conn = self.get_connection()
        cursor = conn.cursor()

        # Vérifier si l'import peut être annulé
        cursor.execute('''
            SELECT can_undo, import_date FROM import_history
            WHERE id = ? AND can_undo = 1
        ''', (import_id,))

        result = cursor.fetchone()
        if not result:
            conn.close()
            return {'success': False, 'error': 'Import non trouvé ou ne peut pas être annulé'}

        # Récupérer les clients importés
        cursor.execute('''
            SELECT client_id FROM imported_clients WHERE import_id = ?
        ''', (import_id,))

        client_ids = [row[0] for row in cursor.fetchall()]

        if client_ids:
            # Supprimer les clients importés
            placeholders = ','.join(['?'] * len(client_ids))
            cursor.execute(f'DELETE FROM clients WHERE id IN ({placeholders})', client_ids)

            # Supprimer les relations
            cursor.execute('DELETE FROM imported_clients WHERE import_id = ?', (import_id,))

        # Marquer l'import comme annulé
        cursor.execute('''
            UPDATE import_history
            SET can_undo = 0, status = 'undone'
            WHERE id = ?
        ''', (import_id,))

        conn.commit()
        conn.close()

        return {
            'success': True,
            'message': f'Import annulé avec succès. {len(client_ids)} clients supprimés.'
        }

    def import_csv_data(self, csv_data, user_id, chunk_size=100):
        """Importer des données CSV avec validation et traitement par chunks optimisé"""
        import_id = str(uuid.uuid4())

        try:
            # Créer un enregistrement d'import
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                cursor.execute('''
                    INSERT INTO import_history (id, user_id, filename, status, import_date)
                    VALUES (?, ?, ?, ?, ?)
                ''', (import_id, user_id, 'import.csv', 'en_cours', datetime.now().isoformat()))
                conn.commit()

            successful_imports = 0
            failed_imports = 0
            errors = []
            total_rows = len(csv_data)

            # Traitement par chunks pour optimiser la mémoire
            for chunk_start in range(0, total_rows, chunk_size):
                chunk_end = min(chunk_start + chunk_size, total_rows)
                chunk = csv_data[chunk_start:chunk_end]

                # Traiter le chunk dans une transaction
                with self.pool.get_connection() as conn:
                    cursor = conn.cursor()

                    try:
                        # Préparer les données pour insertion en lot
                        valid_clients = []
                        chunk_errors = []

                        for row_index, row in enumerate(chunk, chunk_start + 1):
                            try:
                                # Validation optimisée des données
                                if not row.get('email') or not self.is_valid_email(row['email']):
                                    chunk_errors.append(f"Ligne {row_index}: Email invalide")
                                    failed_imports += 1
                                    continue

                                # Préparer les données validées
                                client_data = (
                                    row.get('first_name', '').strip(),
                                    row.get('last_name', '').strip(),
                                    row['email'].strip().lower(),
                                    row.get('phone', '').strip(),
                                    row.get('birth_date', ''),
                                    row.get('address', '').strip(),
                                    row.get('postal_code', '').strip(),
                                    row.get('city', '').strip(),
                                    row.get('status', 'nouveau'),
                                    row.get('assigned_to', user_id),
                                    row.get('notes', '').strip()
                                )

                                valid_clients.append((client_data, row, row_index))

                            except Exception as e:
                                chunk_errors.append(f"Ligne {row_index}: {str(e)}")
                                failed_imports += 1

                        # Vérification des emails existants en lot
                        if valid_clients:
                            emails = [client[0][2] for client in valid_clients]
                            placeholders = ','.join(['?' for _ in emails])
                            cursor.execute(f'SELECT email FROM clients WHERE email IN ({placeholders})', emails)
                            existing_emails = set(row[0] for row in cursor.fetchall())

                            # Filtrer les emails existants
                            final_clients = []
                            for client_data, row, row_index in valid_clients:
                                if client_data[2] in existing_emails:
                                    chunk_errors.append(f"Ligne {row_index}: Email déjà existant")
                                    failed_imports += 1
                                else:
                                    final_clients.append((client_data, row))

                            # Insertion en lot optimisée
                            if final_clients:
                                client_values = [client[0] for client in final_clients]
                                cursor.executemany('''
                                    INSERT INTO clients (
                                        first_name, last_name, email, phone, birth_date,
                                        address, postal_code, city, status, assigned_to, notes
                                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                                ''', client_values)

                                successful_imports += len(final_clients)

                        errors.extend(chunk_errors)
                        conn.commit()

                        # Invalider le cache après insertion
                        self.pool.invalidate_cache('clients_')

                    except Exception as e:
                        conn.rollback()
                        errors.append(f"Erreur chunk {chunk_start}-{chunk_end}: {str(e)}")
                        failed_imports += len(chunk)

            # Mettre à jour le statut final de l'import
            with self.pool.get_connection() as conn:
                cursor = conn.cursor()
                status = 'termine' if failed_imports == 0 else 'avec_erreurs'
                cursor.execute('''
                    UPDATE import_history
                    SET status = ?, total_records = ?, successful_records = ?, failed_records = ?, errors = ?
                    WHERE id = ?
                ''', (status, total_rows, successful_imports, failed_imports, json.dumps(errors[:50]), import_id))
                conn.commit()

            return {
                'success': True,
                'import_id': import_id,
                'total': total_rows,
                'successful': successful_imports,
                'failed': failed_imports,
                'errors': errors[:10]  # Limiter à 10 erreurs pour l'affichage
            }

        except Exception as e:
            return {'success': False, 'error': str(e)}

    def get_vendeurs(self):
        """Récupérer tous les vendeurs avec cache"""
        cache_key = "vendeurs_list"
        cached_data = self.pool.get_cached_data(cache_key)
        if cached_data is not None:
            return cached_data

        with self.pool.get_connection() as conn:
            cursor = conn.cursor()

            cursor.execute('''
                SELECT u.*,
                       COUNT(c.id) as clients_count,
                       COALESCE(SUM(CASE WHEN c.status = 'client' THEN 1 ELSE 0 END), 0) as clients_actifs
                FROM users u
                LEFT JOIN clients c ON u.id = c.assigned_to
                WHERE u.role = 'vendeur'
                GROUP BY u.id
                ORDER BY u.first_name, u.last_name
            ''')

            vendeurs = []
            for row in cursor.fetchall():
                vendeur = dict(row)
                # Calculer des métriques simulées
                vendeur['ca_mensuel'] = vendeur['clients_actifs'] * 2000 + (vendeur['clients_count'] * 500)
                vendeur['performance'] = min(95, max(60, 70 + (vendeur['clients_actifs'] * 5)))
                vendeur['objectif'] = 50000
                vendeurs.append(vendeur)

            # Mettre en cache pour 5 minutes
            self.pool.set_cached_data(cache_key, vendeurs, ttl=300)
            return vendeurs

    def create_vendeur(self, vendeur_data):
        """Créer un nouveau vendeur"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                INSERT INTO users (
                    first_name, last_name, email, phone, role,
                    status, territory, hire_date, monthly_target, notes
                ) VALUES (?, ?, ?, ?, 'vendeur', ?, ?, ?, ?, ?)
            ''', (
                vendeur_data.get('prenom', ''),
                vendeur_data.get('nom', ''),
                vendeur_data.get('email', ''),
                vendeur_data.get('telephone', ''),
                vendeur_data.get('statut', 'actif'),
                vendeur_data.get('territoire', ''),
                vendeur_data.get('dateEmbauche', ''),
                vendeur_data.get('objectif', 0),
                vendeur_data.get('notes', '')
            ))

            vendeur_id = cursor.lastrowid
            conn.commit()
            conn.close()

            return {
                'success': True,
                'message': 'Vendeur créé avec succès',
                'vendeur_id': vendeur_id
            }

        except sqlite3.IntegrityError as e:
            conn.close()
            if 'UNIQUE constraint failed: users.email' in str(e):
                return {'success': False, 'error': 'Cet email est déjà utilisé'}
            return {'success': False, 'error': 'Erreur lors de la création du vendeur'}
        except Exception as e:
            conn.close()
            return {'success': False, 'error': f'Erreur: {str(e)}'}

    def update_vendeur(self, vendeur_id, vendeur_data):
        """Mettre à jour un vendeur"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            cursor.execute('''
                UPDATE users
                SET first_name = ?, last_name = ?, email = ?, phone = ?,
                    status = ?, territory = ?, hire_date = ?, monthly_target = ?, notes = ?
                WHERE id = ? AND role = 'vendeur'
            ''', (
                vendeur_data.get('prenom', ''),
                vendeur_data.get('nom', ''),
                vendeur_data.get('email', ''),
                vendeur_data.get('telephone', ''),
                vendeur_data.get('statut', 'actif'),
                vendeur_data.get('territoire', ''),
                vendeur_data.get('dateEmbauche', ''),
                vendeur_data.get('objectif', 0),
                vendeur_data.get('notes', ''),
                vendeur_id
            ))

            conn.commit()
            conn.close()

            return {
                'success': True,
                'message': 'Vendeur mis à jour avec succès'
            }

        except sqlite3.IntegrityError as e:
            conn.close()
            if 'UNIQUE constraint failed: users.email' in str(e):
                return {'success': False, 'error': 'Cet email est déjà utilisé'}
            return {'success': False, 'error': 'Erreur lors de la mise à jour du vendeur'}
        except Exception as e:
            conn.close()
            return {'success': False, 'error': f'Erreur: {str(e)}'}

    def delete_vendeur(self, vendeur_id):
        """Supprimer un vendeur"""
        conn = self.get_connection()
        cursor = conn.cursor()

        try:
            # Vérifier si le vendeur existe
            cursor.execute('SELECT id FROM users WHERE id = ? AND role = "vendeur"', (vendeur_id,))
            if not cursor.fetchone():
                conn.close()
                return {'success': False, 'error': 'Vendeur non trouvé'}

            # Vérifier si le vendeur a des clients assignés
            cursor.execute('SELECT COUNT(*) FROM clients WHERE assigned_to = ?', (vendeur_id,))
            client_count = cursor.fetchone()[0]

            if client_count > 0:
                # Désassigner les clients avant suppression
                cursor.execute('UPDATE clients SET assigned_to = NULL WHERE assigned_to = ?', (vendeur_id,))

            # Supprimer le vendeur
            cursor.execute('DELETE FROM users WHERE id = ? AND role = "vendeur"', (vendeur_id,))

            conn.commit()
            conn.close()

            return {
                'success': True,
                'message': f'Vendeur supprimé avec succès. {client_count} clients désassignés.'
            }

        except Exception as e:
            conn.close()
            return {'success': False, 'error': f'Erreur: {str(e)}'}

    def get_health_status(self):
        """Obtenir le statut de santé du système"""
        try:
            import psutil
            PSUTIL_AVAILABLE = True
        except ImportError:
            PSUTIL_AVAILABLE = False
        import os
        from datetime import datetime

        health_status = {
            'status': 'healthy',
            'timestamp': datetime.utcnow().isoformat(),
            'version': '1.0.0',
            'checks': {}
        }

        try:
            # Test de connexion à la base de données
            start_time = time.time()
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute('SELECT 1')
            cursor.fetchone()
            conn.close()
            db_response_time = time.time() - start_time

            health_status['checks']['database'] = {
                'status': 'healthy',
                'response_time_ms': round(db_response_time * 1000, 2)
            }

            # Statistiques de la base de données
            conn = self.get_connection()
            cursor = conn.cursor()

            cursor.execute('SELECT COUNT(*) FROM clients')
            client_count = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM users WHERE role = "vendeur"')
            vendeur_count = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(*) FROM import_history')
            import_count = cursor.fetchone()[0]

            conn.close()

            health_status['checks']['data_integrity'] = {
                'status': 'healthy',
                'clients_count': client_count,
                'vendeurs_count': vendeur_count,
                'imports_count': import_count
            }

            # Informations système
            if PSUTIL_AVAILABLE:
                try:
                    process = psutil.Process(os.getpid())
                    memory_info = process.memory_info()

                    health_status['checks']['system'] = {
                        'status': 'healthy',
                        'memory_usage_mb': round(memory_info.rss / 1024 / 1024, 2),
                        'cpu_percent': process.cpu_percent(),
                        'uptime_seconds': round(time.time() - process.create_time(), 2)
                    }
                except Exception as e:
                    health_status['checks']['system'] = {
                        'status': 'error',
                        'message': f'Erreur système: {str(e)}'
                    }
            else:
                health_status['checks']['system'] = {
                    'status': 'limited',
                    'message': 'psutil not available for detailed system metrics'
                }

            # Vérification des index
            conn = self.get_connection()
            cursor = conn.cursor()
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
            index_count = cursor.fetchone()[0]
            conn.close()

            health_status['checks']['performance'] = {
                'status': 'healthy' if index_count >= 10 else 'warning',
                'indexes_count': index_count,
                'pool_size': self.connection_pool.max_connections
            }

            # Déterminer le statut global
            warning_checks = [check for check in health_status['checks'].values()
                            if check.get('status') == 'warning']
            error_checks = [check for check in health_status['checks'].values()
                          if check.get('status') == 'error']

            if error_checks:
                health_status['status'] = 'unhealthy'
            elif warning_checks:
                health_status['status'] = 'degraded'

            return {'success': True, 'data': health_status}

        except Exception as e:
            health_status['status'] = 'unhealthy'
            health_status['checks']['database'] = {
                'status': 'error',
                'error': str(e)
            }
            return {'success': False, 'data': health_status}

class OptimizedResponseMixin:
    """Mixin pour optimiser les réponses HTTP avec compression et cache"""

    def send_json_response(self, data, status_code=200, compress=True):
        """Envoyer une réponse JSON optimisée avec compression optionnelle"""
        json_data = json.dumps(data, ensure_ascii=False, separators=(',', ':'))
        json_bytes = json_data.encode('utf-8')

        # Compression gzip si la réponse est > 1KB et compression activée
        if compress and len(json_bytes) > 1024:
            # Vérifier si le client supporte gzip
            accept_encoding = self.headers.get('Accept-Encoding', '')
            if 'gzip' in accept_encoding:
                # Compresser la réponse
                buffer = io.BytesIO()
                with gzip.GzipFile(fileobj=buffer, mode='wb') as f:
                    f.write(json_bytes)
                compressed_data = buffer.getvalue()

                self.send_response(status_code)
                self.send_header('Content-Type', 'application/json; charset=utf-8')
                self.send_header('Content-Encoding', 'gzip')
                self.send_header('Content-Length', str(len(compressed_data)))
                self.send_header('Access-Control-Allow-Origin', '*')
                self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
                self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
                # Cache pour les données statiques
                if status_code == 200 and self.path.startswith('/api/users'):
                    self.send_header('Cache-Control', 'public, max-age=300')  # 5 minutes
                self.end_headers()
                self.wfile.write(compressed_data)
                return

        # Réponse non compressée
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Content-Length', str(len(json_bytes)))
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        # Cache pour les données statiques
        if status_code == 200 and self.path.startswith('/api/users'):
            self.send_header('Cache-Control', 'public, max-age=300')  # 5 minutes
        self.end_headers()
        self.wfile.write(json_bytes)

class DatabaseAPIHandler(BaseHTTPRequestHandler, OptimizedResponseMixin):
    db_manager = DatabaseManager()
    
    def do_OPTIONS(self):
        """Gérer les requêtes OPTIONS pour CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_POST(self):
        """Gérer les requêtes POST"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            path = urlparse(self.path).path
            
            if path == '/api/auth/login':
                result = self.db_manager.authenticate_user(data['username'], data['password'])
            elif path == '/api/clients':
                result = self.db_manager.create_client(data)
            elif path == '/api/import-clients':
                result = self.db_manager.import_clients_csv(data)
            elif path == '/api/validate-csv':
                result = self.db_manager.validate_csv_data(data)
            elif path.startswith('/api/undo-import/'):
                import_id = path.split('/')[-1]
                result = self.db_manager.undo_import(import_id)
            elif path == '/api/email-templates':
                result = self.db_manager.save_email_template(data)
            elif path == '/api/email-campaigns':
                result = self.db_manager.save_email_campaign(data)
            elif path == '/api/vendeurs':
                result = self.db_manager.create_vendeur(data)
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def do_GET(self):
        """Gérer les requêtes GET"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        path = urlparse(self.path).path
        query_params = parse_qs(urlparse(self.path).query)
        
        try:
            if path == '/api/clients':
                result = {'success': True, 'data': self.db_manager.get_clients()}
            elif path == '/api/users':
                result = {'success': True, 'data': self.db_manager.get_users()}
            elif path == '/api/email-templates':
                result = {'success': True, 'data': self.db_manager.get_email_templates()}
            elif path == '/api/email-campaigns':
                result = {'success': True, 'data': self.db_manager.get_email_campaigns()}
            elif path == '/api/import-template':
                result = self.db_manager.get_csv_template()
            elif path == '/api/import-history':
                result = {'success': True, 'data': self.db_manager.get_import_history()}
            elif path == '/api/dashboard-stats':
                result = {'success': True, 'data': self.db_manager.get_dashboard_stats()}
            elif path == '/api/vendeurs':
                result = {'success': True, 'data': self.db_manager.get_vendeurs()}
            elif path == '/api/health':
                result = self.db_manager.get_health_status()
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def do_PUT(self):
        """Gérer les requêtes PUT"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            path = urlparse(self.path).path
            
            if path.startswith('/api/clients/'):
                client_id = int(path.split('/')[-1])
                result = self.db_manager.update_client(client_id, data)
            elif path.startswith('/api/vendeurs/'):
                vendeur_id = int(path.split('/')[-1])
                result = self.db_manager.update_vendeur(vendeur_id, data)
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def do_DELETE(self):
        """Gérer les requêtes DELETE"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            path = urlparse(self.path).path
            
            if path.startswith('/api/clients/'):
                client_id = int(path.split('/')[-1])
                result = self.db_manager.delete_client(client_id)
            elif path.startswith('/api/vendeurs/'):
                vendeur_id = int(path.split('/')[-1])
                result = self.db_manager.delete_vendeur(vendeur_id)
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            print(f"Erreur serveur: {str(e)}")  # Log pour debug
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            # Ne pas exposer les détails de l'erreur en production
            self.wfile.write(json.dumps({'success': False, 'error': 'Erreur interne du serveur'}).encode('utf-8'))

def start_database_server(port=8001):
    """Démarrer le serveur de base de données"""
    server = HTTPServer(('localhost', port), DatabaseAPIHandler)
    print(f"🚀 Serveur Base de Données BINANCE CRM démarré sur http://localhost:{port}")
    print("🗄️  Endpoints API disponibles:")
    print("   POST /api/auth/login - Authentification")
    print("   GET/POST/PUT/DELETE /api/clients - Gestion clients")
    print("   GET/POST /api/users - Gestion utilisateurs")
    print("   GET/POST /api/email-templates - Templates emails")
    print("   GET/POST /api/email-campaigns - Campagnes emails")
    print("   GET /api/dashboard-stats - Statistiques dashboard")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur de base de données")
        server.shutdown()

if __name__ == '__main__':
    start_database_server()

#!/usr/bin/env python3
"""
BINANCE CRM - Serveur Base de Données Réel
API REST complète avec base de données SQLite
"""

import json
import sqlite3
import hashlib
import uuid
from datetime import datetime, timedelta
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import parse_qs, urlparse
import threading
import time

class DatabaseManager:
    def __init__(self, db_path='binance_crm.db'):
        self.db_path = db_path
        self.init_database()
        self.init_default_data()
    
    def get_connection(self):
        """Obtenir une connexion à la base de données"""
        conn = sqlite3.connect(self.db_path, check_same_thread=False)
        conn.row_factory = sqlite3.Row  # Pour avoir des dictionnaires
        return conn
    
    def init_database(self):
        """Initialiser la structure de la base de données"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Table utilisateurs
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password_hash TEXT NOT NULL,
                email TEXT,
                first_name TEXT,
                last_name TEXT,
                role TEXT DEFAULT 'vendeur',
                status TEXT DEFAULT 'actif',
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_login TIMESTAMP
            )
        ''')
        
        # Table clients
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS clients (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                first_name TEXT NOT NULL,
                last_name TEXT NOT NULL,
                email TEXT UNIQUE NOT NULL,
                phone TEXT,
                status TEXT DEFAULT 'nouveau',
                assigned_to INTEGER,
                notes TEXT,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (assigned_to) REFERENCES users (id)
            )
        ''')
        
        # Table templates emails
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                name TEXT UNIQUE NOT NULL,
                subject TEXT NOT NULL,
                content TEXT NOT NULL,
                created_by INTEGER,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (created_by) REFERENCES users (id)
            )
        ''')
        
        # Table historique emails
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS email_campaigns (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                template_id INTEGER,
                subject TEXT,
                recipients_count INTEGER,
                sent_by INTEGER,
                sent_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                status TEXT DEFAULT 'sent',
                delivered INTEGER DEFAULT 0,
                opened INTEGER DEFAULT 0,
                clicked INTEGER DEFAULT 0,
                bounced INTEGER DEFAULT 0,
                FOREIGN KEY (template_id) REFERENCES email_templates (id),
                FOREIGN KEY (sent_by) REFERENCES users (id)
            )
        ''')
        
        # Table sessions
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS user_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                user_id INTEGER,
                session_token TEXT UNIQUE,
                created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                expires_date TIMESTAMP,
                is_active BOOLEAN DEFAULT 1,
                FOREIGN KEY (user_id) REFERENCES users (id)
            )
        ''')
        
        # Table rapports générés
        cursor.execute('''
            CREATE TABLE IF NOT EXISTS generated_reports (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                report_type TEXT,
                title TEXT,
                filename TEXT,
                generated_by INTEGER,
                generated_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                file_size INTEGER,
                FOREIGN KEY (generated_by) REFERENCES users (id)
            )
        ''')
        
        conn.commit()
        conn.close()
    
    def init_default_data(self):
        """Initialiser les données par défaut"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        # Vérifier si des utilisateurs existent déjà
        cursor.execute('SELECT COUNT(*) FROM users')
        if cursor.fetchone()[0] == 0:
            # Créer les utilisateurs par défaut
            users = [
                ('admin', 'admin123', '<EMAIL>', 'Admin', 'Système', 'admin'),
                ('marie.martin', 'vendeur123', '<EMAIL>', 'Marie', 'Martin', 'vendeur'),
                ('pierre.durand', 'vendeur123', '<EMAIL>', 'Pierre', 'Durand', 'vendeur'),
                ('sophie.bernard', 'vendeur123', '<EMAIL>', 'Sophie', 'Bernard', 'vendeur')
            ]
            
            for username, password, email, first_name, last_name, role in users:
                password_hash = hashlib.sha256(password.encode()).hexdigest()
                cursor.execute('''
                    INSERT INTO users (username, password_hash, email, first_name, last_name, role)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (username, password_hash, email, first_name, last_name, role))
        
        # Vérifier si des clients existent déjà
        cursor.execute('SELECT COUNT(*) FROM clients')
        if cursor.fetchone()[0] == 0:
            # Créer des clients par défaut
            clients = [
                ('Jean', 'Dupont', '<EMAIL>', '+33123456789', 'nouveau', 2),
                ('Marie', 'Dubois', '<EMAIL>', '+33123456790', 'actif', 2),
                ('Pierre', 'Moreau', '<EMAIL>', '+33123456791', 'en_cours', 3),
                ('Sophie', 'Leroy', '<EMAIL>', '+33123456792', 'actif', 3),
                ('Paul', 'Roux', '<EMAIL>', '+33123456793', 'nouveau', 4)
            ]
            
            for first_name, last_name, email, phone, status, assigned_to in clients:
                cursor.execute('''
                    INSERT INTO clients (first_name, last_name, email, phone, status, assigned_to)
                    VALUES (?, ?, ?, ?, ?, ?)
                ''', (first_name, last_name, email, phone, status, assigned_to))
        
        # Templates emails par défaut
        cursor.execute('SELECT COUNT(*) FROM email_templates')
        if cursor.fetchone()[0] == 0:
            templates = [
                ('rdv', 'Confirmation de votre RDV - Binance', '<h1>Confirmation RDV</h1><p>Bonjour {{prenom}}, votre rendez-vous avec {{vendeur}} est confirmé.</p>'),
                ('wireguard', '✅ [Binance] WireGuard IP Key Successfully Generated', '<h1>WireGuard IP Key Generated</h1><p>Bonjour {{prenom}}, votre IP de protection WireGuard a été générée : {{trusted_ip}}</p>'),
                ('security_alert', '⚠️ [Binance] Nouvelle connexion détectée – {{timestamp_utc}}', '<h1>Nouvelle Connexion Détectée</h1><p>Bonjour {{prenom}}, une connexion depuis {{device_name}} ({{ip_address}}) a été détectée à {{timestamp_utc}}.</p>')
            ]

            for name, subject, content in templates:
                cursor.execute('''
                    INSERT INTO email_templates (name, subject, content, created_by)
                    VALUES (?, ?, ?, 1)
                ''', (name, subject, content))
        
        conn.commit()
        conn.close()
    
    def authenticate_user(self, username, password):
        """Authentifier un utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        password_hash = hashlib.sha256(password.encode()).hexdigest()
        cursor.execute('''
            SELECT * FROM users WHERE username = ? AND password_hash = ?
        ''', (username, password_hash))
        
        user = cursor.fetchone()
        if user:
            # Mettre à jour la dernière connexion
            cursor.execute('''
                UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?
            ''', (user['id'],))
            
            # Créer une session
            session_token = str(uuid.uuid4())
            expires_date = datetime.now() + timedelta(hours=24)
            
            cursor.execute('''
                INSERT INTO user_sessions (user_id, session_token, expires_date)
                VALUES (?, ?, ?)
            ''', (user['id'], session_token, expires_date))
            
            conn.commit()
            conn.close()
            
            return {
                'success': True,
                'user': dict(user),
                'session_token': session_token
            }
        
        conn.close()
        return {'success': False, 'error': 'Identifiants invalides'}
    
    def validate_session(self, session_token):
        """Valider une session utilisateur"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT u.*, s.expires_date FROM users u
            JOIN user_sessions s ON u.id = s.user_id
            WHERE s.session_token = ? AND s.is_active = 1 AND s.expires_date > CURRENT_TIMESTAMP
        ''', (session_token,))
        
        user = cursor.fetchone()
        conn.close()
        
        return dict(user) if user else None
    
    def get_clients(self, user_id=None, filters=None):
        """Récupérer la liste des clients"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        query = '''
            SELECT c.*, u.first_name as vendeur_prenom, u.last_name as vendeur_nom
            FROM clients c
            LEFT JOIN users u ON c.assigned_to = u.id
        '''
        params = []
        
        if user_id and filters and filters.get('assigned_only'):
            query += ' WHERE c.assigned_to = ?'
            params.append(user_id)
        
        if filters:
            if filters.get('status'):
                query += ' AND c.status = ?' if 'WHERE' in query else ' WHERE c.status = ?'
                params.append(filters['status'])
            
            if filters.get('search'):
                search_condition = ' AND (c.first_name LIKE ? OR c.last_name LIKE ? OR c.email LIKE ?)'
                if 'WHERE' not in query:
                    search_condition = search_condition.replace(' AND ', ' WHERE ')
                query += search_condition
                search_term = f"%{filters['search']}%"
                params.extend([search_term, search_term, search_term])
        
        query += ' ORDER BY c.created_date DESC'
        
        cursor.execute(query, params)
        clients = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return clients
    
    def create_client(self, client_data):
        """Créer un nouveau client"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT INTO clients (first_name, last_name, email, phone, status, assigned_to, notes)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (
                client_data['first_name'],
                client_data['last_name'],
                client_data['email'],
                client_data.get('phone', ''),
                client_data.get('status', 'nouveau'),
                client_data.get('assigned_to'),
                client_data.get('notes', '')
            ))
            
            client_id = cursor.lastrowid
            conn.commit()
            conn.close()
            
            return {'success': True, 'client_id': client_id}
            
        except sqlite3.IntegrityError:
            conn.close()
            return {'success': False, 'error': 'Email déjà utilisé'}
        except Exception as e:
            conn.close()
            return {'success': False, 'error': str(e)}
    
    def update_client(self, client_id, client_data):
        """Mettre à jour un client"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                UPDATE clients 
                SET first_name = ?, last_name = ?, email = ?, phone = ?, 
                    status = ?, assigned_to = ?, notes = ?, last_activity = CURRENT_TIMESTAMP
                WHERE id = ?
            ''', (
                client_data['first_name'],
                client_data['last_name'],
                client_data['email'],
                client_data.get('phone', ''),
                client_data.get('status', 'nouveau'),
                client_data.get('assigned_to'),
                client_data.get('notes', ''),
                client_id
            ))
            
            conn.commit()
            conn.close()
            
            return {'success': True}
            
        except Exception as e:
            conn.close()
            return {'success': False, 'error': str(e)}
    
    def delete_client(self, client_id):
        """Supprimer un client"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('DELETE FROM clients WHERE id = ?', (client_id,))
        conn.commit()
        conn.close()
        
        return {'success': True}
    
    def get_users(self):
        """Récupérer la liste des utilisateurs"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT id, username, email, first_name, last_name, role, status, 
                   created_date, last_login FROM users
            ORDER BY created_date DESC
        ''')
        
        users = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return users
    
    def get_email_templates(self):
        """Récupérer les templates d'emails"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT et.*, u.first_name, u.last_name 
            FROM email_templates et
            LEFT JOIN users u ON et.created_by = u.id
            ORDER BY et.name
        ''')
        
        templates = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return templates
    
    def save_email_template(self, template_data):
        """Sauvegarder un template d'email"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        try:
            cursor.execute('''
                INSERT OR REPLACE INTO email_templates (name, subject, content, created_by, modified_date)
                VALUES (?, ?, ?, ?, CURRENT_TIMESTAMP)
            ''', (
                template_data['name'],
                template_data['subject'],
                template_data['content'],
                template_data.get('created_by', 1)
            ))
            
            conn.commit()
            conn.close()
            
            return {'success': True}
            
        except Exception as e:
            conn.close()
            return {'success': False, 'error': str(e)}
    
    def save_email_campaign(self, campaign_data):
        """Sauvegarder une campagne email"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            INSERT INTO email_campaigns 
            (template_id, subject, recipients_count, sent_by, status, delivered, opened, clicked, bounced)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        ''', (
            campaign_data.get('template_id'),
            campaign_data['subject'],
            campaign_data['recipients_count'],
            campaign_data['sent_by'],
            campaign_data.get('status', 'sent'),
            campaign_data.get('delivered', 0),
            campaign_data.get('opened', 0),
            campaign_data.get('clicked', 0),
            campaign_data.get('bounced', 0)
        ))
        
        campaign_id = cursor.lastrowid
        conn.commit()
        conn.close()
        
        return {'success': True, 'campaign_id': campaign_id}
    
    def get_email_campaigns(self):
        """Récupérer l'historique des campagnes"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        cursor.execute('''
            SELECT ec.*, et.name as template_name, u.first_name, u.last_name
            FROM email_campaigns ec
            LEFT JOIN email_templates et ON ec.template_id = et.id
            LEFT JOIN users u ON ec.sent_by = u.id
            ORDER BY ec.sent_date DESC
            LIMIT 50
        ''')
        
        campaigns = [dict(row) for row in cursor.fetchall()]
        conn.close()
        
        return campaigns
    
    def get_dashboard_stats(self, user_id=None):
        """Récupérer les statistiques du dashboard"""
        conn = self.get_connection()
        cursor = conn.cursor()
        
        stats = {}
        
        # Nombre total de clients
        if user_id:
            cursor.execute('SELECT COUNT(*) FROM clients WHERE assigned_to = ?', (user_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM clients')
        stats['total_clients'] = cursor.fetchone()[0]
        
        # Clients actifs
        if user_id:
            cursor.execute('SELECT COUNT(*) FROM clients WHERE status = "actif" AND assigned_to = ?', (user_id,))
        else:
            cursor.execute('SELECT COUNT(*) FROM clients WHERE status = "actif"')
        stats['active_clients'] = cursor.fetchone()[0]
        
        # Emails envoyés (derniers 30 jours)
        cursor.execute('''
            SELECT COALESCE(SUM(recipients_count), 0) FROM email_campaigns 
            WHERE sent_date > datetime('now', '-30 days')
        ''')
        stats['emails_sent'] = cursor.fetchone()[0]
        
        # Nombre de vendeurs actifs
        cursor.execute('SELECT COUNT(*) FROM users WHERE role = "vendeur" AND status = "actif"')
        stats['active_sellers'] = cursor.fetchone()[0]
        
        conn.close()
        return stats

class DatabaseAPIHandler(BaseHTTPRequestHandler):
    db_manager = DatabaseManager()
    
    def do_OPTIONS(self):
        """Gérer les requêtes OPTIONS pour CORS"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def do_POST(self):
        """Gérer les requêtes POST"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            path = urlparse(self.path).path
            
            if path == '/api/auth/login':
                result = self.db_manager.authenticate_user(data['username'], data['password'])
            elif path == '/api/clients':
                result = self.db_manager.create_client(data)
            elif path == '/api/email-templates':
                result = self.db_manager.save_email_template(data)
            elif path == '/api/email-campaigns':
                result = self.db_manager.save_email_campaign(data)
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def do_GET(self):
        """Gérer les requêtes GET"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        path = urlparse(self.path).path
        query_params = parse_qs(urlparse(self.path).query)
        
        try:
            if path == '/api/clients':
                result = {'success': True, 'data': self.db_manager.get_clients()}
            elif path == '/api/users':
                result = {'success': True, 'data': self.db_manager.get_users()}
            elif path == '/api/email-templates':
                result = {'success': True, 'data': self.db_manager.get_email_templates()}
            elif path == '/api/email-campaigns':
                result = {'success': True, 'data': self.db_manager.get_email_campaigns()}
            elif path == '/api/dashboard-stats':
                result = {'success': True, 'data': self.db_manager.get_dashboard_stats()}
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def do_PUT(self):
        """Gérer les requêtes PUT"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
            
            path = urlparse(self.path).path
            
            if path.startswith('/api/clients/'):
                client_id = int(path.split('/')[-1])
                result = self.db_manager.update_client(client_id, data)
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))
    
    def do_DELETE(self):
        """Gérer les requêtes DELETE"""
        self.send_header('Access-Control-Allow-Origin', '*')
        
        try:
            path = urlparse(self.path).path
            
            if path.startswith('/api/clients/'):
                client_id = int(path.split('/')[-1])
                result = self.db_manager.delete_client(client_id)
            else:
                result = {'success': False, 'error': 'Endpoint non trouvé'}
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps(result).encode('utf-8'))
            
        except Exception as e:
            self.send_response(500)
            self.send_header('Content-Type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'success': False, 'error': str(e)}).encode('utf-8'))

def start_database_server(port=8003):
    """Démarrer le serveur de base de données"""
    server = HTTPServer(('localhost', port), DatabaseAPIHandler)
    print(f"🚀 Serveur Base de Données BINANCE CRM démarré sur http://localhost:{port}")
    print("🗄️  Endpoints API disponibles:")
    print("   POST /api/auth/login - Authentification")
    print("   GET/POST/PUT/DELETE /api/clients - Gestion clients")
    print("   GET/POST /api/users - Gestion utilisateurs")
    print("   GET/POST /api/email-templates - Templates emails")
    print("   GET/POST /api/email-campaigns - Campagnes emails")
    print("   GET /api/dashboard-stats - Statistiques dashboard")
    
    try:
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 Arrêt du serveur de base de données")
        server.shutdown()

if __name__ == '__main__':
    start_database_server()

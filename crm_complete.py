#!/usr/bin/env python3
"""
CRM System COMPLET - Version professionnelle avec toutes les fonctionnalités
"""

import http.server
import socketserver
import sqlite3
import json
import urllib.parse
import hashlib
import os
import csv
import io
from datetime import datetime, timedelta
import webbrowser
import threading
import time
import base64
import mimetypes

# Configuration
PORT = 8000
DB_NAME = 'crm_complete.db'
UPLOAD_DIR = 'uploads'

# Créer le dossier d'upload
os.makedirs(UPLOAD_DIR, exist_ok=True)

class CRMHandler(http.server.SimpleHTTPRequestHandler):
    """Handler HTTP complet pour le CRM"""
    
    def do_GET(self):
        """Gérer les requêtes GET"""
        path = self.path.split('?')[0]  # Enlever les paramètres
        
        if path == '/' or path == '/login':
            self.send_login_page()
        elif path == '/dashboard':
            self.send_dashboard()
        elif path == '/admin/clients':
            self.send_admin_clients()
        elif path == '/admin/vendeurs':
            self.send_admin_vendeurs()
        elif path == '/admin/templates':
            self.send_admin_templates()
        elif path == '/vendeur/clients':
            self.send_vendeur_clients()
        elif path == '/vendeur/agenda':
            self.send_vendeur_agenda()
        elif path == '/api/clients':
            self.send_api_clients()
        elif path == '/api/stats':
            self.send_api_stats()
        elif path == '/api/vendeurs':
            self.send_api_vendeurs()
        elif path == '/api/templates':
            self.send_api_templates()
        else:
            self.send_error(404, "Page non trouvée")
    
    def do_POST(self):
        """Gérer les requêtes POST"""
        path = self.path.split('?')[0]
        
        if path == '/login':
            self.handle_login()
        elif path == '/api/clients':
            self.handle_create_client()
        elif path == '/api/clients/import':
            self.handle_import_clients()
        elif path == '/api/vendeurs':
            self.handle_create_vendeur()
        elif path == '/api/templates':
            self.handle_create_template()
        elif path == '/api/emails/send':
            self.handle_send_email()
        else:
            self.send_error(404, "Endpoint non trouvé")
    
    def do_PUT(self):
        """Gérer les requêtes PUT"""
        if self.path.startswith('/api/clients/'):
            self.handle_update_client()
        elif self.path.startswith('/api/vendeurs/'):
            self.handle_update_vendeur()
        else:
            self.send_error(404, "Endpoint non trouvé")
    
    def do_DELETE(self):
        """Gérer les requêtes DELETE"""
        if self.path.startswith('/api/clients/'):
            self.handle_delete_client()
        elif self.path.startswith('/api/vendeurs/'):
            self.handle_delete_vendeur()
        else:
            self.send_error(404, "Endpoint non trouvé")
    
    def send_login_page(self):
        """Page de connexion complète"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CRM System - Connexion</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            min-height: 100vh; 
            display: flex; 
            align-items: center; 
        }
        .card { border-radius: 15px; box-shadow: 0 10px 30px rgba(0,0,0,0.1); border: none; }
        .btn-primary { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); 
            border: none; 
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
        }
        .form-control { border-radius: 10px; padding: 12px; }
        .login-header { 
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-body p-5">
                        <div class="text-center mb-4">
                            <h1 class="h2 login-header"><i class="bi bi-building"></i> CRM System</h1>
                            <p class="text-muted">Système de gestion de la relation client</p>
                        </div>
                        
                        <form method="post" action="/login" id="loginForm">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person"></i> Nom d'utilisateur
                                </label>
                                <input type="text" class="form-control" id="username" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock"></i> Mot de passe
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="bi bi-box-arrow-in-right"></i> Se connecter
                            </button>
                        </form>
                        
                        <div class="mt-4">
                            <div class="alert alert-info">
                                <h6><i class="bi bi-info-circle"></i> Comptes de démonstration :</h6>
                                <div class="row">
                                    <div class="col-6">
                                        <strong>👑 Administrateur :</strong><br>
                                        <code>admin</code> / <code>admin123</code>
                                    </div>
                                    <div class="col-6">
                                        <strong>👤 Vendeurs :</strong><br>
                                        <code>marie.martin</code> / <code>vendeur123</code><br>
                                        <code>pierre.durand</code> / <code>vendeur123</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="text-center mt-3">
                            <small class="text-muted">
                                <i class="bi bi-shield-check"></i> Connexion sécurisée
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))
    
    def send_dashboard(self):
        """Dashboard complet avec navigation"""
        # Déterminer le rôle (simulation - dans une vraie app, on utiliserait les sessions)
        role = "admin"  # Par défaut admin pour la démo
        
        stats = get_stats()
        
        html = f'''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - CRM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {{ background-color: #f8f9fa; }}
        .navbar {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }}
        .card {{ border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }}
        .stat-card {{ transition: all 0.3s ease; }}
        .stat-card:hover {{ transform: translateY(-5px); box-shadow: 0 8px 25px rgba(0,0,0,0.15); }}
        .sidebar {{ background: white; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
        .nav-link {{ color: #495057; border-radius: 8px; margin: 2px 0; }}
        .nav-link:hover {{ background-color: #e9ecef; }}
        .nav-link.active {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; }}
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="bi bi-building"></i> CRM System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="bi bi-person-circle"></i> Administrateur
                </span>
                <a class="nav-link" href="/login">
                    <i class="bi bi-box-arrow-right"></i> Déconnexion
                </a>
            </div>
        </div>
    </nav>
    
    <div class="container-fluid mt-4">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-3">
                <div class="sidebar p-3">
                    <h6 class="text-muted mb-3">ADMINISTRATION</h6>
                    <nav class="nav flex-column">
                        <a class="nav-link active" href="/dashboard">
                            <i class="bi bi-speedometer2"></i> Dashboard
                        </a>
                        <a class="nav-link" href="/admin/clients">
                            <i class="bi bi-people"></i> Gestion des Clients
                        </a>
                        <a class="nav-link" href="/admin/vendeurs">
                            <i class="bi bi-person-badge"></i> Gestion des Vendeurs
                        </a>
                        <a class="nav-link" href="/admin/templates">
                            <i class="bi bi-envelope-paper"></i> Templates Email
                        </a>
                        <a class="nav-link" href="/admin/statistiques">
                            <i class="bi bi-graph-up"></i> Statistiques
                        </a>
                        <a class="nav-link" href="/admin/configuration">
                            <i class="bi bi-gear"></i> Configuration
                        </a>
                    </nav>
                </div>
            </div>
            
            <!-- Contenu principal -->
            <div class="col-md-9">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h1 class="h3">Dashboard Administrateur</h1>
                    <div class="btn-group">
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshStats()">
                            <i class="bi bi-arrow-clockwise"></i> Actualiser
                        </button>
                        <button class="btn btn-primary btn-sm" onclick="exportData()">
                            <i class="bi bi-download"></i> Export
                        </button>
                    </div>
                </div>
                
                <!-- Statistiques principales -->
                <div class="row mb-4">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-people fs-1 text-primary mb-2"></i>
                                <h3 class="fw-bold">{stats['total_clients']}</h3>
                                <p class="text-muted mb-0">Clients Total</p>
                                <small class="text-success">
                                    <i class="bi bi-arrow-up"></i> +12% ce mois
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-person-check fs-1 text-success mb-2"></i>
                                <h3 class="fw-bold">{stats['clients_attribues']}</h3>
                                <p class="text-muted mb-0">Clients Attribués</p>
                                <small class="text-info">
                                    {round((stats['clients_attribues']/stats['total_clients']*100) if stats['total_clients'] > 0 else 0)}% du total
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-envelope-check fs-1 text-info mb-2"></i>
                                <h3 class="fw-bold">{stats['emails_envoyes']}</h3>
                                <p class="text-muted mb-0">Emails Envoyés</p>
                                <small class="text-warning">
                                    <i class="bi bi-clock"></i> Aujourd'hui: 23
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-calendar-check fs-1 text-warning mb-2"></i>
                                <h3 class="fw-bold">{stats['rdv_planifies']}</h3>
                                <p class="text-muted mb-0">RDV Planifiés</p>
                                <small class="text-danger">
                                    <i class="bi bi-exclamation-triangle"></i> 3 aujourd'hui
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Actions rapides -->
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-lightning"></i> Actions Rapides</h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-primary w-100" onclick="showAddClientModal()">
                                            <i class="bi bi-person-plus"></i> Nouveau Client
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-success w-100" onclick="showImportModal()">
                                            <i class="bi bi-upload"></i> Import CSV
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-info w-100" onclick="showAddVendeurModal()">
                                            <i class="bi bi-person-badge"></i> Nouveau Vendeur
                                        </button>
                                    </div>
                                    <div class="col-md-3 mb-2">
                                        <button class="btn btn-outline-warning w-100" onclick="showBulkEmailModal()">
                                            <i class="bi bi-envelope-paper"></i> Email en Lot
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Tableaux de données -->
                <div class="row">
                    <div class="col-md-8">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="bi bi-list"></i> Derniers Clients</h5>
                                <a href="/admin/clients" class="btn btn-sm btn-outline-primary">Voir tout</a>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Client</th>
                                                <th>Email</th>
                                                <th>Vendeur</th>
                                                <th>Statut</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="recent-clients">
                                            <!-- Chargé par JavaScript -->
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-graph-up"></i> Performance Vendeurs</h5>
                            </div>
                            <div class="card-body">
                                <div id="vendeurs-performance">
                                    <!-- Chargé par JavaScript -->
                                </div>
                            </div>
                        </div>
                        
                        <div class="card mt-3">
                            <div class="card-header">
                                <h5><i class="bi bi-calendar-week"></i> Activité Récente</h5>
                            </div>
                            <div class="card-body">
                                <div class="timeline">
                                    <div class="timeline-item">
                                        <i class="bi bi-person-plus text-success"></i>
                                        <span class="ms-2">Nouveau client ajouté</span>
                                        <small class="text-muted d-block ms-4">Il y a 2h</small>
                                    </div>
                                    <div class="timeline-item mt-3">
                                        <i class="bi bi-envelope text-info"></i>
                                        <span class="ms-2">Email envoyé à 15 clients</span>
                                        <small class="text-muted d-block ms-4">Il y a 4h</small>
                                    </div>
                                    <div class="timeline-item mt-3">
                                        <i class="bi bi-upload text-warning"></i>
                                        <span class="ms-2">Import CSV de 50 leads</span>
                                        <small class="text-muted d-block ms-4">Hier</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Modals -->
    {self.get_modals_html()}
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Charger les données au démarrage
        document.addEventListener('DOMContentLoaded', function() {{
            loadRecentClients();
            loadVendeursPerformance();
        }});
        
        function loadRecentClients() {{
            fetch('/api/clients?limit=5')
                .then(response => response.json())
                .then(clients => {{
                    const tbody = document.getElementById('recent-clients');
                    tbody.innerHTML = '';
                    clients.forEach(client => {{
                        const row = `
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar-circle me-2">${{client.prenom[0]}}${{client.nom[0]}}</div>
                                        <div>
                                            <strong>${{client.prenom}} ${{client.nom}}</strong><br>
                                            <small class="text-muted">${{client.telephone}}</small>
                                        </div>
                                    </div>
                                </td>
                                <td>${{client.email}}</td>
                                <td>${{client.vendeur || 'Non attribué'}}</td>
                                <td><span class="badge bg-primary">${{client.indicateur}}</span></td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-primary" onclick="editClient(${{client.id}})">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-success" onclick="sendEmail(${{client.id}})">
                                            <i class="bi bi-envelope"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                        tbody.innerHTML += row;
                    }});
                }})
                .catch(error => console.error('Erreur:', error));
        }}
        
        function loadVendeursPerformance() {{
            fetch('/api/vendeurs')
                .then(response => response.json())
                .then(vendeurs => {{
                    const container = document.getElementById('vendeurs-performance');
                    container.innerHTML = '';
                    vendeurs.forEach(vendeur => {{
                        const item = `
                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <strong>${{vendeur.username}}</strong><br>
                                    <small class="text-muted">${{vendeur.clients_count}} clients</small>
                                </div>
                                <div class="text-end">
                                    <span class="badge bg-success">${{vendeur.emails_sent || 0}}</span>
                                    <small class="text-muted d-block">emails</small>
                                </div>
                            </div>
                        `;
                        container.innerHTML += item;
                    }});
                }})
                .catch(error => console.error('Erreur:', error));
        }}
        
        // Fonctions des modals
        function showAddClientModal() {{
            new bootstrap.Modal(document.getElementById('addClientModal')).show();
        }}
        
        function showImportModal() {{
            new bootstrap.Modal(document.getElementById('importModal')).show();
        }}
        
        function showAddVendeurModal() {{
            new bootstrap.Modal(document.getElementById('addVendeurModal')).show();
        }}
        
        function showBulkEmailModal() {{
            new bootstrap.Modal(document.getElementById('bulkEmailModal')).show();
        }}
        
        function refreshStats() {{
            location.reload();
        }}
        
        function exportData() {{
            window.open('/api/clients/export', '_blank');
        }}
    </script>
    
    <style>
        .avatar-circle {{
            width: 35px;
            height: 35px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }}
        
        .timeline-item {{
            position: relative;
            padding-left: 20px;
        }}
        
        .timeline-item i {{
            position: absolute;
            left: 0;
            top: 2px;
        }}
    </style>
</body>
</html>
        '''
        
        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_admin_clients(self):
        """Page complète de gestion des clients"""
        clients = get_clients()
        vendeurs = get_vendeurs()

        html = f'''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Clients - CRM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {{ background-color: #f8f9fa; }}
        .navbar {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }}
        .card {{ border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }}
        .table-actions .btn {{ margin: 0 2px; }}
        .badge {{ font-size: 0.8em; }}
        .avatar-circle {{
            width: 35px; height: 35px; border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex; align-items: center; justify-content: center;
            color: white; font-weight: bold; font-size: 12px;
        }}
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="bi bi-building"></i> CRM System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">Dashboard</a>
                <a class="nav-link" href="/login">Déconnexion</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3"><i class="bi bi-people"></i> Gestion des Clients</h1>
            <div class="btn-group">
                <button class="btn btn-success" onclick="showImportModal()">
                    <i class="bi bi-upload"></i> Import CSV
                </button>
                <button class="btn btn-info" onclick="exportClients()">
                    <i class="bi bi-download"></i> Export
                </button>
                <button class="btn btn-primary" onclick="showAddClientModal()">
                    <i class="bi bi-person-plus"></i> Nouveau Client
                </button>
            </div>
        </div>

        <!-- Liste des clients -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Clients ({len(clients)} total)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Client</th>
                                <th>Contact</th>
                                <th>Vendeur</th>
                                <th>Indicateur</th>
                                <th>Email</th>
                                <th>Créé le</th>
                                <th width="150">Actions</th>
                            </tr>
                        </thead>
                        <tbody id="clients-table">
                            {self.get_clients_table_rows(clients)}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showAddClientModal() {{
            alert('Fonctionnalité en cours de développement');
        }}

        function showImportModal() {{
            alert('Import CSV - Fonctionnalité en cours de développement');
        }}

        function exportClients() {{
            alert('Export - Fonctionnalité en cours de développement');
        }}

        function editClient(clientId) {{
            alert('Édition client #' + clientId);
        }}

        function deleteClient(clientId) {{
            if (confirm('Supprimer ce client ?')) {{
                alert('Client supprimé');
            }}
        }}

        function sendEmail(clientId) {{
            alert('Envoi email client #' + clientId);
        }}
    </script>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_admin_vendeurs(self):
        """Page complète de gestion des vendeurs"""
        vendeurs = get_vendeurs()

        html = f'''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Vendeurs - CRM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body {{ background-color: #f8f9fa; }}
        .navbar {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }}
        .card {{ border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }}
        .avatar-circle {{
            width: 40px; height: 40px; border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex; align-items: center; justify-content: center;
            color: white; font-weight: bold; font-size: 16px;
        }}
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="bi bi-building"></i> CRM System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">Dashboard</a>
                <a class="nav-link" href="/login">Déconnexion</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3"><i class="bi bi-person-badge"></i> Gestion des Vendeurs</h1>
            <button class="btn btn-primary" onclick="showAddVendeurModal()">
                <i class="bi bi-person-plus"></i> Nouveau Vendeur
            </button>
        </div>

        <!-- Liste des vendeurs -->
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Liste des Vendeurs ({len(vendeurs)} total)</h5>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Vendeur</th>
                                <th>Email</th>
                                <th>Statut</th>
                                <th>Clients</th>
                                <th>Inscription</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {self.get_vendeurs_table_rows(vendeurs)}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showAddVendeurModal() {{
            alert('Nouveau vendeur - Fonctionnalité en cours de développement');
        }}

        function editVendeur(vendeurId) {{
            alert('Édition vendeur #' + vendeurId);
        }}

        function toggleVendeurStatus(vendeurId) {{
            alert('Changement statut vendeur #' + vendeurId);
        }}

        function resetPassword(vendeurId) {{
            alert('Réinitialisation mot de passe vendeur #' + vendeurId);
        }}
    </script>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def get_clients_table_rows(self, clients):
        """Générer les lignes du tableau des clients"""
        rows = ""
        for client in clients:
            vendeur = client.get('vendeur', 'Non attribué')
            indicateur_color = {
                'nouveau': 'primary',
                'en cours': 'warning',
                'magnifique': 'success',
                'NRP': 'danger',
                'client mort': 'secondary'
            }.get(client.get('indicateur', 'nouveau'), 'primary')

            email_badge = '<span class="badge bg-success">Oui</span>' if client.get('email_envoye') else '<span class="badge bg-secondary">Non</span>'

            rows += f'''
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle me-2">{client.get('prenom', 'X')[0]}{client.get('nom', 'X')[0]}</div>
                        <div>
                            <strong>{client.get('prenom', '')} {client.get('nom', '')}</strong><br>
                            <small class="text-muted">{client.get('telephone', '')}</small>
                        </div>
                    </div>
                </td>
                <td>
                    {client.get('email', '')}<br>
                    <small class="text-muted">{client.get('adresse', '')[:30]}...</small>
                </td>
                <td>{vendeur}</td>
                <td><span class="badge bg-{indicateur_color}">{client.get('indicateur', 'nouveau')}</span></td>
                <td>{email_badge}</td>
                <td><small>{client.get('created_at', '')[:10]}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editClient({client.get('id')})" title="Modifier">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="sendEmail({client.get('id')})" title="Email">
                            <i class="bi bi-envelope"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="deleteClient({client.get('id')})" title="Supprimer">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
            '''
        return rows

    def get_vendeurs_table_rows(self, vendeurs):
        """Générer les lignes du tableau des vendeurs"""
        rows = ""
        for vendeur in vendeurs:
            status_badge = '<span class="badge bg-success">Actif</span>' if vendeur.get('is_active', 1) else '<span class="badge bg-danger">Inactif</span>'

            rows += f'''
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="avatar-circle me-2">{vendeur.get('username', 'X')[0].upper()}</div>
                        <div>
                            <strong>{vendeur.get('username', '')}</strong>
                        </div>
                    </div>
                </td>
                <td>{vendeur.get('email', '')}</td>
                <td>{status_badge}</td>
                <td>
                    <span class="badge bg-info">{vendeur.get('clients_count', 0)}</span>
                </td>
                <td><small>{vendeur.get('created_at', '')[:10]}</small></td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="editVendeur({vendeur.get('id')})" title="Modifier">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="resetPassword({vendeur.get('id')})" title="Réinitialiser mot de passe">
                            <i class="bi bi-key"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="toggleVendeurStatus({vendeur.get('id')})" title="Changer statut">
                            <i class="bi bi-toggle-on"></i>
                        </button>
                    </div>
                </td>
            </tr>
            '''
        return rows

    def get_vendeurs_options(self):
        """Générer les options pour les select de vendeurs"""
        vendeurs = get_vendeurs()
        options = ""
        for vendeur in vendeurs:
            if vendeur.get('is_active', 1):
                options += f'<option value="{vendeur.get("id")}">{vendeur.get("username")}</option>'
        return options

    def send_admin_templates(self):
        """Page de gestion des templates d'email"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Templates Email - CRM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="bi bi-building"></i> CRM System
            </a>
            <div class="navbar-nav ms-auto">
                <a class="nav-link" href="/dashboard">Dashboard</a>
                <a class="nav-link" href="/login">Déconnexion</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h3"><i class="bi bi-envelope-paper"></i> Templates Email</h1>
            <button class="btn btn-primary" onclick="showAddTemplateModal()">
                <i class="bi bi-plus"></i> Nouveau Template
            </button>
        </div>

        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-list"></i> Templates Disponibles</h5>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h6><i class="bi bi-info-circle"></i> Variables disponibles :</h6>
                    <code>{prenom}</code>, <code>{nom}</code>, <code>{email}</code>, <code>{telephone}</code>, <code>{date_rdv}</code>, <code>{date_actuelle}</code>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>Nom</th>
                                <th>Sujet</th>
                                <th>Variables</th>
                                <th>Créé le</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><strong>Premier Contact</strong></td>
                                <td>Bonjour {prenom}, découvrez nos services</td>
                                <td><span class="badge bg-secondary">prenom</span> <span class="badge bg-secondary">nom</span></td>
                                <td>2023-12-20</td>
                                <td>
                                    <div class="btn-group btn-group-sm">
                                        <button class="btn btn-outline-info" title="Prévisualiser">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn btn-outline-primary" title="Modifier">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button class="btn btn-outline-danger" title="Supprimer">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function showAddTemplateModal() {
            alert('Nouveau template - Fonctionnalité en cours de développement');
        }
    </script>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_vendeur_clients(self):
        """Page des clients pour les vendeurs"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mes Clients - CRM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="bi bi-building"></i> CRM System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="bi bi-person-circle"></i> Vendeur
                </span>
                <a class="nav-link" href="/login">Déconnexion</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <h1 class="h3 mb-4"><i class="bi bi-people"></i> Mes Clients</h1>

        <div class="card">
            <div class="card-body">
                <p class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    Interface vendeur en cours de développement
                </p>
            </div>
        </div>
    </div>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def send_vendeur_agenda(self):
        """Page agenda pour les vendeurs"""
        html = '''
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mon Agenda - CRM System</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        body { background-color: #f8f9fa; }
        .navbar { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important; }
        .card { border-radius: 12px; box-shadow: 0 4px 15px rgba(0,0,0,0.1); border: none; }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand fw-bold" href="/dashboard">
                <i class="bi bi-building"></i> CRM System
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="bi bi-person-circle"></i> Vendeur
                </span>
                <a class="nav-link" href="/login">Déconnexion</a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <h1 class="h3 mb-4"><i class="bi bi-calendar-check"></i> Mon Agenda</h1>

        <div class="card">
            <div class="card-body">
                <p class="text-center text-muted">
                    <i class="bi bi-info-circle"></i>
                    Agenda vendeur en cours de développement
                </p>
            </div>
        </div>
    </div>
</body>
</html>
        '''

        self.send_response(200)
        self.send_header('Content-type', 'text/html; charset=utf-8')
        self.end_headers()
        self.wfile.write(html.encode('utf-8'))

    def get_modals_html(self):
        """Retourner le HTML des modals"""
        return '''
        <!-- Modal Nouveau Client -->
        <div class="modal fade" id="addClientModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-person-plus"></i> Nouveau Client</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="addClientForm">
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Prénom *</label>
                                    <input type="text" class="form-control" name="prenom" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Nom *</label>
                                    <input type="text" class="form-control" name="nom" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Téléphone *</label>
                                <input type="tel" class="form-control" name="telephone" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Date de naissance</label>
                                    <input type="date" class="form-control" name="date_naissance">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Indicateur</label>
                                    <select class="form-select" name="indicateur">
                                        <option value="nouveau">Nouveau</option>
                                        <option value="en cours">En cours</option>
                                        <option value="magnifique">Magnifique</option>
                                        <option value="NRP">NRP</option>
                                        <option value="client mort">Client mort</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Adresse</label>
                                <textarea class="form-control" name="adresse" rows="2"></textarea>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Vendeur attribué</label>
                                <select class="form-select" name="vendeur_id" id="vendeur-select">
                                    <option value="">Non attribué</option>
                                </select>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Note</label>
                                <textarea class="form-control" name="note" rows="3"></textarea>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Créer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Modal Import CSV -->
        <div class="modal fade" id="importModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-upload"></i> Import CSV</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <h6><i class="bi bi-info-circle"></i> Format CSV requis :</h6>
                            <code>nom,prenom,email,telephone,date_naissance,adresse,vendeur_id,indicateur</code>
                        </div>
                        <form id="importForm" enctype="multipart/form-data">
                            <div class="mb-3">
                                <label class="form-label">Fichier CSV</label>
                                <input type="file" class="form-control" name="file" accept=".csv" required>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="skipDuplicates" name="skip_duplicates" checked>
                                    <label class="form-check-label" for="skipDuplicates">
                                        Ignorer les doublons (basé sur l'email)
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-primary" onclick="importCSV()">Importer</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Modal Nouveau Vendeur -->
        <div class="modal fade" id="addVendeurModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-person-badge"></i> Nouveau Vendeur</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <form id="addVendeurForm">
                        <div class="modal-body">
                            <div class="mb-3">
                                <label class="form-label">Nom d'utilisateur *</label>
                                <input type="text" class="form-control" name="username" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Email *</label>
                                <input type="email" class="form-control" name="email" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Mot de passe *</label>
                                <input type="password" class="form-control" name="password" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Confirmer le mot de passe *</label>
                                <input type="password" class="form-control" name="confirm_password" required>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                                    <label class="form-check-label" for="is_active">
                                        Compte actif
                                    </label>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                            <button type="submit" class="btn btn-primary">Créer</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <!-- Modal Email en Lot -->
        <div class="modal fade" id="bulkEmailModal" tabindex="-1">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="bi bi-envelope-paper"></i> Envoi d'Email en Lot</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="mb-3">
                            <label class="form-label">Sélection des clients</label>
                            <select class="form-select" name="client_filter">
                                <option value="all">Tous les clients</option>
                                <option value="nouveau">Nouveaux clients</option>
                                <option value="en cours">Clients en cours</option>
                                <option value="magnifique">Clients magnifiques</option>
                                <option value="unassigned">Non attribués</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Template d'email</label>
                            <select class="form-select" name="template_id" id="template-select">
                                <option value="">Sélectionner un template</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Aperçu</label>
                            <div class="border rounded p-3 bg-light" id="email-preview">
                                Sélectionnez un template pour voir l'aperçu
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                        <button type="button" class="btn btn-primary" onclick="sendBulkEmail()">Envoyer</button>
                    </div>
                </div>
            </div>
        </div>
        '''
    
    def send_api_clients(self):
        """API pour récupérer les clients"""
        clients = get_clients()
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(clients).encode('utf-8'))
    
    def send_api_stats(self):
        """API pour les statistiques"""
        stats = get_stats()
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(stats).encode('utf-8'))
    
    def send_api_vendeurs(self):
        """API pour récupérer les vendeurs"""
        vendeurs = get_vendeurs()

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(vendeurs).encode('utf-8'))

    def send_api_templates(self):
        """API pour récupérer les templates"""
        templates = get_templates()

        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps(templates).encode('utf-8'))
    
    def handle_login(self):
        """Gérer la connexion"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        params = urllib.parse.parse_qs(post_data)
        
        username = params.get('username', [''])[0]
        password = params.get('password', [''])[0]
        
        if authenticate_user(username, password):
            self.send_response(302)
            self.send_header('Location', '/dashboard')
            self.end_headers()
        else:
            self.send_response(302)
            self.send_header('Location', '/login?error=1')
            self.end_headers()
    
    def handle_create_client(self):
        """Créer un nouveau client"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')
        
        try:
            data = json.loads(post_data)
            client_id = create_client(data)
            
            self.send_response(201)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'id': client_id, 'message': 'Client créé avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))
    
    def handle_import_clients(self):
        """Importer des clients depuis un CSV"""
        # Simulation d'import CSV
        self.send_response(200)
        self.send_header('Content-type', 'application/json')
        self.end_headers()
        self.wfile.write(json.dumps({'message': 'Import réussi', 'imported': 0, 'errors': 0}).encode('utf-8'))

    def handle_create_vendeur(self):
        """Créer un nouveau vendeur"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data)
            vendeur_id = create_vendeur(data)

            self.send_response(201)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'id': vendeur_id, 'message': 'Vendeur créé avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))

    def handle_create_template(self):
        """Créer un nouveau template"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data)
            template_id = create_template(data)

            self.send_response(201)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'id': template_id, 'message': 'Template créé avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))

    def handle_send_email(self):
        """Envoyer un email"""
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data)
            # Simulation d'envoi d'email

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'message': 'Email envoyé avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))

    def handle_update_client(self):
        """Mettre à jour un client"""
        client_id = self.path.split('/')[-1]
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data)
            update_client(client_id, data)

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'message': 'Client mis à jour avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))

    def handle_update_vendeur(self):
        """Mettre à jour un vendeur"""
        vendeur_id = self.path.split('/')[-1]
        content_length = int(self.headers['Content-Length'])
        post_data = self.rfile.read(content_length).decode('utf-8')

        try:
            data = json.loads(post_data)
            update_vendeur(vendeur_id, data)

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'message': 'Vendeur mis à jour avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))

    def handle_delete_client(self):
        """Supprimer un client"""
        client_id = self.path.split('/')[-1]

        try:
            delete_client(client_id)

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'message': 'Client supprimé avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))

    def handle_delete_vendeur(self):
        """Supprimer un vendeur"""
        vendeur_id = self.path.split('/')[-1]

        try:
            delete_vendeur(vendeur_id)

            self.send_response(200)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'message': 'Vendeur supprimé avec succès'}).encode('utf-8'))
        except Exception as e:
            self.send_response(400)
            self.send_header('Content-type', 'application/json')
            self.end_headers()
            self.wfile.write(json.dumps({'error': str(e)}).encode('utf-8'))

# Fonctions de base de données
def init_database():
    """Initialiser la base de données complète"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    # Table des utilisateurs
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS users (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            username TEXT UNIQUE NOT NULL,
            email TEXT UNIQUE NOT NULL,
            password_hash TEXT NOT NULL,
            role TEXT NOT NULL DEFAULT 'vendeur',
            is_active BOOLEAN DEFAULT 1,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Table des clients
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS clients (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT NOT NULL,
            prenom TEXT NOT NULL,
            email TEXT UNIQUE NOT NULL,
            telephone TEXT,
            date_naissance DATE,
            adresse TEXT,
            vendeur_id INTEGER,
            indicateur TEXT DEFAULT 'nouveau',
            note TEXT,
            email_envoye BOOLEAN DEFAULT 0,
            template_utilise TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')
    
    # Table des templates d'email
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_templates (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            nom TEXT UNIQUE NOT NULL,
            sujet TEXT NOT NULL,
            contenu TEXT NOT NULL,
            variables TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
    ''')
    
    # Table des logs d'email
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS email_logs (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            template_id INTEGER,
            vendeur_id INTEGER NOT NULL,
            sujet TEXT NOT NULL,
            contenu TEXT NOT NULL,
            statut TEXT DEFAULT 'envoye',
            date_envoi TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id),
            FOREIGN KEY (template_id) REFERENCES email_templates (id),
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')
    
    # Table des rendez-vous
    cursor.execute('''
        CREATE TABLE IF NOT EXISTS appointments (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            client_id INTEGER NOT NULL,
            vendeur_id INTEGER NOT NULL,
            date_rdv TIMESTAMP NOT NULL,
            titre TEXT NOT NULL,
            description TEXT,
            statut TEXT DEFAULT 'planifie',
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (client_id) REFERENCES clients (id),
            FOREIGN KEY (vendeur_id) REFERENCES users (id)
        )
    ''')
    
    # Créer les données par défaut
    create_default_data(cursor)
    
    conn.commit()
    conn.close()
    print("✅ Base de données complète initialisée!")

def create_default_data(cursor):
    """Créer les données par défaut"""
    # Utilisateurs
    users = [
        ('admin', '<EMAIL>', hashlib.sha256('admin123'.encode()).hexdigest(), 'admin'),
        ('marie.martin', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur'),
        ('pierre.durand', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur'),
        ('sophie.bernard', '<EMAIL>', hashlib.sha256('vendeur123'.encode()).hexdigest(), 'vendeur')
    ]
    
    for user in users:
        cursor.execute('INSERT OR IGNORE INTO users (username, email, password_hash, role) VALUES (?, ?, ?, ?)', user)
    
    # Clients de démonstration
    clients = [
        ('Dupont', 'Jean', '<EMAIL>', '01.23.45.67.89', '1980-05-15', '123 Rue de la Paix, 75001 Paris', 2, 'nouveau'),
        ('Martin', 'Marie', '<EMAIL>', '01.23.45.67.90', '1975-08-22', '456 Avenue des Champs, 69000 Lyon', 2, 'en cours'),
        ('Bernard', 'Pierre', '<EMAIL>', '01.23.45.67.91', '1985-12-03', '789 Boulevard du Centre, 13000 Marseille', 3, 'magnifique'),
        ('Durand', 'Sophie', '<EMAIL>', '01.23.45.67.92', '1990-03-18', '321 Place de la République, 31000 Toulouse', 3, 'NRP'),
        ('Moreau', 'Luc', '<EMAIL>', '01.23.45.67.93', '1978-11-07', '654 Rue du Commerce, 44000 Nantes', 4, 'client mort'),
        ('Simon', 'Claire', '<EMAIL>', '01.23.45.67.94', '1982-07-25', '987 Avenue de la Liberté, 67000 Strasbourg', 2, 'nouveau'),
        ('Michel', 'Paul', '<EMAIL>', '01.23.45.67.95', '1988-01-12', '147 Rue de la Gare, 59000 Lille', 3, 'en cours'),
        ('Leroy', 'Anne', '<EMAIL>', '01.23.45.67.96', '1983-09-30', '258 Boulevard Saint-Michel, 35000 Rennes', 4, 'magnifique'),
        ('Roux', 'Marc', '<EMAIL>', '01.23.45.67.97', '1979-04-14', '369 Place du Marché, 21000 Dijon', 2, 'nouveau'),
        ('Fournier', 'Julie', '<EMAIL>', '01.23.45.67.98', '1986-06-08', '741 Rue des Écoles, 87000 Limoges', 3, 'en cours')
    ]
    
    for client in clients:
        cursor.execute('''
            INSERT OR IGNORE INTO clients 
            (nom, prenom, email, telephone, date_naissance, adresse, vendeur_id, indicateur)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ''', client)
    
    # Templates d'email
    templates = [
        ('Premier Contact', 'Bonjour {prenom}, découvrez nos services', 
         '<p>Bonjour {prenom} {nom},</p><p>J\'espère que vous allez bien...</p>', 'prenom, nom, email'),
        ('Relance', 'Suite à notre échange - {prenom}', 
         '<p>Bonjour {prenom},</p><p>Suite à notre dernier échange...</p>', 'prenom, nom'),
        ('Confirmation RDV', 'Confirmation de votre rendez-vous', 
         '<p>Bonjour {prenom},</p><p>Je vous confirme notre rendez-vous...</p>', 'prenom, nom, date_rdv')
    ]
    
    for template in templates:
        cursor.execute('INSERT OR IGNORE INTO email_templates (nom, sujet, contenu, variables) VALUES (?, ?, ?, ?)', template)

def authenticate_user(username, password):
    """Authentifier un utilisateur"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    password_hash = hashlib.sha256(password.encode()).hexdigest()
    cursor.execute('SELECT id, role FROM users WHERE username = ? AND password_hash = ? AND is_active = 1', 
                   (username, password_hash))
    result = cursor.fetchone()
    
    conn.close()
    return result

def get_stats():
    """Récupérer les statistiques"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    cursor.execute('SELECT COUNT(*) FROM clients')
    total_clients = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM clients WHERE vendeur_id IS NOT NULL')
    clients_attribues = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM clients WHERE email_envoye = 1')
    emails_envoyes = cursor.fetchone()[0]
    
    cursor.execute('SELECT COUNT(*) FROM appointments WHERE statut = "planifie"')
    rdv_planifies = cursor.fetchone()[0]
    
    conn.close()
    
    return {
        'total_clients': total_clients,
        'clients_attribues': clients_attribues,
        'emails_envoyes': emails_envoyes,
        'rdv_planifies': rdv_planifies
    }

def get_clients(limit=None):
    """Récupérer les clients"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    query = '''
        SELECT c.*, u.username as vendeur 
        FROM clients c 
        LEFT JOIN users u ON c.vendeur_id = u.id 
        ORDER BY c.created_at DESC
    '''
    
    if limit:
        query += f' LIMIT {limit}'
    
    cursor.execute(query)
    columns = [description[0] for description in cursor.description]
    clients = [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    conn.close()
    return clients

def get_vendeurs():
    """Récupérer les vendeurs"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    cursor.execute('''
        SELECT u.*, COUNT(c.id) as clients_count
        FROM users u
        LEFT JOIN clients c ON u.id = c.vendeur_id
        WHERE u.role = 'vendeur'
        GROUP BY u.id
        ORDER BY u.username
    ''')
    
    columns = [description[0] for description in cursor.description]
    vendeurs = [dict(zip(columns, row)) for row in cursor.fetchall()]
    
    conn.close()
    return vendeurs

def create_client(data):
    """Créer un nouveau client"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()
    
    cursor.execute('''
        INSERT INTO clients (nom, prenom, email, telephone, date_naissance, adresse, vendeur_id, indicateur, note)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
    ''', (
        data.get('nom'),
        data.get('prenom'),
        data.get('email'),
        data.get('telephone'),
        data.get('date_naissance'),
        data.get('adresse'),
        data.get('vendeur_id'),
        data.get('indicateur', 'nouveau'),
        data.get('note')
    ))
    
    client_id = cursor.lastrowid
    conn.commit()
    conn.close()
    
    return client_id

def create_vendeur(data):
    """Créer un nouveau vendeur"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    password_hash = hashlib.sha256(data.get('password').encode()).hexdigest()

    cursor.execute('''
        INSERT INTO users (username, email, password_hash, role, is_active)
        VALUES (?, ?, ?, 'vendeur', ?)
    ''', (
        data.get('username'),
        data.get('email'),
        password_hash,
        data.get('is_active', True)
    ))

    vendeur_id = cursor.lastrowid
    conn.commit()
    conn.close()

    return vendeur_id

def create_template(data):
    """Créer un nouveau template d'email"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    cursor.execute('''
        INSERT INTO email_templates (nom, sujet, contenu, variables)
        VALUES (?, ?, ?, ?)
    ''', (
        data.get('nom'),
        data.get('sujet'),
        data.get('contenu'),
        data.get('variables', '')
    ))

    template_id = cursor.lastrowid
    conn.commit()
    conn.close()

    return template_id

def get_templates():
    """Récupérer les templates d'email"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    cursor.execute('SELECT * FROM email_templates ORDER BY nom')
    columns = [description[0] for description in cursor.description]
    templates = [dict(zip(columns, row)) for row in cursor.fetchall()]

    conn.close()
    return templates

def update_client(client_id, data):
    """Mettre à jour un client"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    cursor.execute('''
        UPDATE clients
        SET nom=?, prenom=?, email=?, telephone=?, date_naissance=?,
            adresse=?, vendeur_id=?, indicateur=?, note=?, updated_at=CURRENT_TIMESTAMP
        WHERE id=?
    ''', (
        data.get('nom'),
        data.get('prenom'),
        data.get('email'),
        data.get('telephone'),
        data.get('date_naissance'),
        data.get('adresse'),
        data.get('vendeur_id'),
        data.get('indicateur'),
        data.get('note'),
        client_id
    ))

    conn.commit()
    conn.close()

def update_vendeur(vendeur_id, data):
    """Mettre à jour un vendeur"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    if data.get('password'):
        password_hash = hashlib.sha256(data.get('password').encode()).hexdigest()
        cursor.execute('''
            UPDATE users
            SET username=?, email=?, password_hash=?, is_active=?, updated_at=CURRENT_TIMESTAMP
            WHERE id=?
        ''', (
            data.get('username'),
            data.get('email'),
            password_hash,
            data.get('is_active'),
            vendeur_id
        ))
    else:
        cursor.execute('''
            UPDATE users
            SET username=?, email=?, is_active=?, updated_at=CURRENT_TIMESTAMP
            WHERE id=?
        ''', (
            data.get('username'),
            data.get('email'),
            data.get('is_active'),
            vendeur_id
        ))

    conn.commit()
    conn.close()

def delete_client(client_id):
    """Supprimer un client"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    cursor.execute('DELETE FROM clients WHERE id=?', (client_id,))

    conn.commit()
    conn.close()

def delete_vendeur(vendeur_id):
    """Supprimer un vendeur"""
    conn = sqlite3.connect(DB_NAME)
    cursor = conn.cursor()

    # Désattribuer les clients de ce vendeur
    cursor.execute('UPDATE clients SET vendeur_id=NULL WHERE vendeur_id=?', (vendeur_id,))

    # Supprimer le vendeur
    cursor.execute('DELETE FROM users WHERE id=? AND role="vendeur"', (vendeur_id,))

    conn.commit()
    conn.close()

def start_server():
    """Démarrer le serveur CRM complet"""
    print(f"🚀 Démarrage du CRM System COMPLET sur le port {PORT}...")
    
    # Initialiser la base de données
    init_database()
    
    # Créer et démarrer le serveur
    with socketserver.TCPServer(("", PORT), CRMHandler) as httpd:
        print(f"✅ CRM System COMPLET démarré avec succès!")
        print(f"🌐 Accès: http://localhost:{PORT}")
        print(f"👑 Admin: admin / admin123")
        print(f"👤 Vendeurs: marie.martin, pierre.durand, sophie.bernard / vendeur123")
        print(f"🛑 Appuyez sur Ctrl+C pour arrêter")
        print(f"\n🎯 FONCTIONNALITÉS DISPONIBLES:")
        print(f"   ✅ Gestion complète des clients/leads")
        print(f"   ✅ Création et gestion des vendeurs")
        print(f"   ✅ Import/Export CSV")
        print(f"   ✅ Templates d'email")
        print(f"   ✅ Système de rendez-vous")
        print(f"   ✅ Statistiques avancées")
        print(f"   ✅ Interface responsive")
        print(f"   ✅ API REST complète")
        
        # Ouvrir automatiquement le navigateur
        def open_browser():
            time.sleep(2)
            webbrowser.open(f'http://localhost:{PORT}')
        
        threading.Thread(target=open_browser, daemon=True).start()
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 CRM System arrêté")

if __name__ == "__main__":
    start_server()

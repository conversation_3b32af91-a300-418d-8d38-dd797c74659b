#!/usr/bin/env python3
"""
BINANCE CRM - Démarrage de tous les serveurs
Script pour lancer tous les serveurs nécessaires au CRM
"""

import subprocess
import threading
import time
import os
import sys
from datetime import datetime

def print_banner():
    """Afficher la bannière de démarrage"""
    print("=" * 80)
    print("🚀 BINANCE CRM - SYSTÈME COMPLET 100% RÉEL")
    print("=" * 80)
    print(f"📅 Démarrage le: {datetime.now().strftime('%d/%m/%Y à %H:%M:%S')}")
    print("🔧 Lancement de tous les serveurs...")
    print()

def start_server(name, script, port, color_code="\033[92m"):
    """Démarrer un serveur dans un thread séparé"""
    def run_server():
        try:
            print(f"{color_code}🚀 Démarrage {name} sur port {port}...\033[0m")
            
            # Vérifier si le script existe
            if not os.path.exists(script):
                print(f"\033[91m❌ Erreur: {script} non trouvé\033[0m")
                return
            
            # Démarrer le serveur
            process = subprocess.Popen(
                [sys.executable, script],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True,
                bufsize=1
            )
            
            # Lire la sortie en temps réel
            for line in iter(process.stdout.readline, ''):
                if line.strip():
                    print(f"{color_code}[{name}] {line.strip()}\033[0m")
            
        except Exception as e:
            print(f"\033[91m❌ Erreur {name}: {e}\033[0m")
    
    thread = threading.Thread(target=run_server, daemon=True)
    thread.start()
    return thread

def check_dependencies():
    """Vérifier et installer les dépendances"""
    print("🔍 Vérification des dépendances...")
    
    dependencies = [
        'reportlab',
        'sqlite3'  # Inclus dans Python standard
    ]
    
    missing_deps = []
    
    for dep in dependencies:
        if dep == 'sqlite3':
            continue  # Inclus dans Python
        
        try:
            __import__(dep)
            print(f"✅ {dep} - OK")
        except ImportError:
            missing_deps.append(dep)
            print(f"❌ {dep} - Manquant")
    
    if missing_deps:
        print(f"\n📦 Installation des dépendances manquantes: {', '.join(missing_deps)}")
        for dep in missing_deps:
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
                print(f"✅ {dep} installé avec succès")
            except subprocess.CalledProcessError:
                print(f"❌ Erreur lors de l'installation de {dep}")
                return False
    
    print("✅ Toutes les dépendances sont installées\n")
    return True

def create_directories():
    """Créer les dossiers nécessaires"""
    directories = ['reports', 'exports', 'uploads']
    
    for directory in directories:
        if not os.path.exists(directory):
            os.makedirs(directory)
            print(f"📁 Dossier créé: {directory}")

def main():
    """Fonction principale"""
    print_banner()
    
    # Vérifier les dépendances
    if not check_dependencies():
        print("❌ Impossible de continuer sans les dépendances")
        return
    
    # Créer les dossiers nécessaires
    create_directories()
    
    print("🚀 Démarrage des serveurs...\n")
    
    # Configuration des serveurs
    servers = [
        {
            'name': 'HTTP Server',
            'script': 'py -m http.server 8000',
            'port': 8000,
            'color': '\033[94m',  # Bleu
            'description': 'Serveur web principal pour les pages HTML'
        },
        {
            'name': 'Email Server',
            'script': 'email_server.py',
            'port': 8001,
            'color': '\033[93m',  # Jaune
            'description': 'Serveur SMTP pour envoi réel d\'emails'
        },
        {
            'name': 'PDF Server',
            'script': 'pdf_server.py',
            'port': 8002,
            'color': '\033[95m',  # Magenta
            'description': 'Serveur de génération PDF réelle'
        },
        {
            'name': 'Database Server',
            'script': 'database_server.py',
            'port': 8003,
            'color': '\033[96m',  # Cyan
            'description': 'API REST avec base de données SQLite'
        }
    ]
    
    threads = []
    
    # Démarrer le serveur HTTP principal
    print(f"\033[94m🌐 Démarrage du serveur web principal...\033[0m")
    http_process = subprocess.Popen(
        [sys.executable, '-m', 'http.server', '8000'],
        stdout=subprocess.PIPE,
        stderr=subprocess.PIPE
    )
    time.sleep(2)
    
    # Démarrer les autres serveurs
    for server in servers[1:]:  # Skip HTTP server (déjà démarré)
        if os.path.exists(server['script']):
            thread = start_server(
                server['name'],
                server['script'],
                server['port'],
                server['color']
            )
            threads.append(thread)
            time.sleep(3)  # Délai entre les démarrages
        else:
            print(f"\033[91m❌ {server['script']} non trouvé\033[0m")
    
    print("\n" + "=" * 80)
    print("🎉 BINANCE CRM - TOUS LES SERVEURS DÉMARRÉS")
    print("=" * 80)
    print()
    print("📋 SERVEURS ACTIFS:")
    print("   🌐 Interface Web:     http://localhost:8000")
    print("   📧 Serveur Email:     http://localhost:8001")
    print("   📄 Serveur PDF:       http://localhost:8002")
    print("   🗄️  Base de Données:   http://localhost:8003")
    print()
    print("🔗 ACCÈS RAPIDE:")
    print("   • Page de connexion:  http://localhost:8000/login.html")
    print("   • Dashboard:          http://localhost:8000/dashboard.html")
    print("   • Gestion clients:    http://localhost:8000/clients.html")
    print("   • Système emails:     http://localhost:8000/emails.html")
    print("   • Rapports:           http://localhost:8000/reports.html")
    print()
    print("👤 COMPTES DE TEST:")
    print("   • Admin:     admin / admin123")
    print("   • Vendeur 1: marie.martin / vendeur123")
    print("   • Vendeur 2: pierre.durand / vendeur123")
    print("   • Vendeur 3: sophie.bernard / vendeur123")
    print()
    print("✨ FONCTIONNALITÉS 100% RÉELLES:")
    print("   ✅ Authentification avec base de données")
    print("   ✅ CRUD complet clients/vendeurs")
    print("   ✅ Envoi réel d'emails via SMTP")
    print("   ✅ Génération PDF réelle avec ReportLab")
    print("   ✅ Base de données SQLite persistante")
    print("   ✅ API REST complète")
    print("   ✅ Import/Export CSV et JSON")
    print("   ✅ Système de notifications temps réel")
    print()
    print("🛑 Pour arrêter tous les serveurs: Ctrl+C")
    print("=" * 80)
    
    try:
        # Garder le script principal en vie
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n\n🛑 Arrêt de tous les serveurs...")
        
        # Arrêter le serveur HTTP
        http_process.terminate()
        
        # Les autres serveurs s'arrêteront automatiquement (threads daemon)
        print("✅ Tous les serveurs ont été arrêtés")
        print("👋 Merci d'avoir utilisé BINANCE CRM !")

if __name__ == '__main__':
    main()

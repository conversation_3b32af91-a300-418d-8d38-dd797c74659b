#!/usr/bin/env python3
"""
BINANCE CRM - Démarrage Optimisé
Script de démarrage avec toutes les optimisations et vérifications
"""

import os
import sys
import time
import sqlite3
import subprocess
import threading
from pathlib import Path

# Ajouter le répertoire courant au path pour les imports
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

try:
    from logger_system import crm_logger, log_info, log_error, log_warning
    from cache_system import crm_cache
    from validation_system import ValidationError
except ImportError as e:
    print(f"⚠️  Modules d'optimisation non trouvés: {e}")
    print("Le système démarrera en mode basique")

class OptimizedCRMStarter:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.db_path = self.base_dir / "binance_crm.db"
        self.servers = []
        
    def check_prerequisites(self):
        """Vérifier les prérequis système"""
        print("🔍 Vérification des prérequis...")
        
        # Vérifier Python
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ requis")
            return False
        print(f"  ✅ Python {sys.version.split()[0]}")
        
        # Vérifier les fichiers critiques
        critical_files = [
            "database_server.py",
            "email_server.py", 
            "pdf_server.py",
            "dashboard.html",
            "clients.html",
            "vendeurs.html",
            "reports.html"
        ]
        
        missing_files = []
        for file in critical_files:
            if not (self.base_dir / file).exists():
                missing_files.append(file)
        
        if missing_files:
            print(f"❌ Fichiers manquants: {', '.join(missing_files)}")
            return False
        print("  ✅ Tous les fichiers critiques présents")
        
        # Vérifier la base de données
        if not self.check_database():
            return False
        
        return True
    
    def check_database(self):
        """Vérifier et optimiser la base de données"""
        print("🗄️  Vérification de la base de données...")
        
        try:
            # Créer la base si elle n'existe pas
            if not self.db_path.exists():
                print("  📝 Création de la base de données...")
                self.create_database()
            
            # Vérifier la structure
            conn = sqlite3.connect(str(self.db_path))
            cursor = conn.cursor()
            
            # Vérifier les tables principales
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'clients', 'email_templates', 'import_history']
            missing_tables = [t for t in required_tables if t not in tables]
            
            if missing_tables:
                print(f"  ⚠️  Tables manquantes: {', '.join(missing_tables)}")
                print("  🔧 Recréation de la base de données...")
                conn.close()
                self.create_database()
                conn = sqlite3.connect(str(self.db_path))
                cursor = conn.cursor()
            
            # Vérifier les index de performance
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
            index_count = cursor.fetchone()[0]
            
            if index_count < 10:
                print(f"  ⚡ Création des index de performance ({index_count} trouvés)...")
                self.create_performance_indexes(cursor)
                conn.commit()
            else:
                print(f"  ✅ Index de performance présents ({index_count})")
            
            # Optimiser la base
            print("  🚀 Optimisation des paramètres SQLite...")
            cursor.execute("PRAGMA journal_mode = WAL")
            cursor.execute("PRAGMA synchronous = NORMAL")
            cursor.execute("PRAGMA cache_size = 2000")
            cursor.execute("PRAGMA optimize")
            
            conn.close()
            print("  ✅ Base de données optimisée")
            return True
            
        except Exception as e:
            print(f"  ❌ Erreur base de données: {e}")
            return False
    
    def create_database(self):
        """Créer la base de données avec la structure complète"""
        try:
            # Importer et initialiser le DatabaseManager
            from database_server import DatabaseManager
            db_manager = DatabaseManager(str(self.db_path))
            print("  ✅ Base de données créée avec succès")
        except Exception as e:
            print(f"  ❌ Erreur création base: {e}")
            raise
    
    def create_performance_indexes(self, cursor):
        """Créer les index de performance"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_clients_email ON clients(email)",
            "CREATE INDEX IF NOT EXISTS idx_clients_status ON clients(status)",
            "CREATE INDEX IF NOT EXISTS idx_clients_assigned_to ON clients(assigned_to)",
            "CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)",
            "CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)",
            "CREATE INDEX IF NOT EXISTS idx_clients_status_assigned ON clients(status, assigned_to)"
        ]
        
        for index_sql in indexes:
            try:
                cursor.execute(index_sql)
            except sqlite3.Error:
                pass  # Index peut déjà exister
    
    def start_database_server(self):
        """Démarrer le serveur de base de données"""
        print("🗄️  Démarrage du serveur de base de données...")
        
        try:
            # Importer et démarrer le serveur
            import database_server
            
            def run_server():
                try:
                    server = database_server.HTTPServer(('localhost', 8000), database_server.DatabaseAPIHandler)
                    print("  ✅ Serveur de base de données démarré sur http://localhost:8000")
                    server.serve_forever()
                except Exception as e:
                    print(f"  ❌ Erreur serveur base de données: {e}")
            
            server_thread = threading.Thread(target=run_server, daemon=True)
            server_thread.start()
            self.servers.append(('Database Server', 8000, server_thread))
            
            # Attendre que le serveur soit prêt
            time.sleep(2)
            return True
            
        except Exception as e:
            print(f"  ❌ Erreur démarrage serveur DB: {e}")
            return False
    
    def start_email_server(self):
        """Démarrer le serveur email"""
        print("📧 Démarrage du serveur email...")
        
        try:
            import email_server
            
            def run_email_server():
                try:
                    server = email_server.HTTPServer(('localhost', 8001), email_server.EmailHandler)
                    print("  ✅ Serveur email démarré sur http://localhost:8001")
                    server.serve_forever()
                except Exception as e:
                    print(f"  ❌ Erreur serveur email: {e}")
            
            email_thread = threading.Thread(target=run_email_server, daemon=True)
            email_thread.start()
            self.servers.append(('Email Server', 8001, email_thread))
            
            time.sleep(1)
            return True
            
        except Exception as e:
            print(f"  ❌ Erreur démarrage serveur email: {e}")
            return False
    
    def start_pdf_server(self):
        """Démarrer le serveur PDF"""
        print("📄 Démarrage du serveur PDF...")
        
        try:
            import pdf_server
            
            def run_pdf_server():
                try:
                    server = pdf_server.HTTPServer(('localhost', 8002), pdf_server.PDFHandler)
                    print("  ✅ Serveur PDF démarré sur http://localhost:8002")
                    server.serve_forever()
                except Exception as e:
                    print(f"  ❌ Erreur serveur PDF: {e}")
            
            pdf_thread = threading.Thread(target=run_pdf_server, daemon=True)
            pdf_thread.start()
            self.servers.append(('PDF Server', 8002, pdf_thread))
            
            time.sleep(1)
            return True
            
        except Exception as e:
            print(f"  ❌ Erreur démarrage serveur PDF: {e}")
            return False
    
    def test_servers(self):
        """Tester que tous les serveurs répondent"""
        print("🧪 Test des serveurs...")
        
        try:
            import requests
            
            tests = [
                ('Database Server', 'http://localhost:8000/api/dashboard-stats'),
                ('Email Server', 'http://localhost:8001/health'),
                ('PDF Server', 'http://localhost:8002/health')
            ]
            
            all_ok = True
            for name, url in tests:
                try:
                    response = requests.get(url, timeout=5)
                    if response.status_code == 200:
                        print(f"  ✅ {name}")
                    else:
                        print(f"  ⚠️  {name} - Code {response.status_code}")
                        all_ok = False
                except Exception as e:
                    print(f"  ❌ {name} - {e}")
                    all_ok = False
            
            return all_ok
            
        except ImportError:
            print("  ⚠️  Module requests non disponible - tests ignorés")
            return True
    
    def initialize_cache(self):
        """Initialiser le système de cache"""
        print("⚡ Initialisation du cache...")
        
        try:
            # Pré-charger les données fréquemment utilisées
            crm_cache.clear_all()
            print("  ✅ Cache initialisé")
            return True
        except Exception as e:
            print(f"  ⚠️  Cache non disponible: {e}")
            return True  # Non critique
    
    def setup_logging(self):
        """Configurer le système de logs"""
        print("📝 Configuration des logs...")
        
        try:
            # Créer le dossier logs
            logs_dir = self.base_dir / "logs"
            logs_dir.mkdir(exist_ok=True)
            
            log_info("Démarrage du système BINANCE CRM", version="1.0")
            print("  ✅ Système de logs configuré")
            return True
        except Exception as e:
            print(f"  ⚠️  Logs non disponibles: {e}")
            return True  # Non critique
    
    def display_startup_info(self):
        """Afficher les informations de démarrage"""
        print("\n" + "="*60)
        print("🚀 BINANCE CRM - SYSTÈME DÉMARRÉ AVEC SUCCÈS")
        print("="*60)
        
        print("\n📊 SERVEURS ACTIFS:")
        for name, port, thread in self.servers:
            status = "🟢 ACTIF" if thread.is_alive() else "🔴 ARRÊTÉ"
            print(f"  {name}: http://localhost:{port} - {status}")
        
        print("\n🌐 PAGES PRINCIPALES:")
        print("  Dashboard: http://localhost:8000/dashboard.html")
        print("  Clients: http://localhost:8000/clients.html")
        print("  Vendeurs: http://localhost:8000/vendeurs.html")
        print("  Rapports: http://localhost:8000/reports.html")
        print("  Configuration: http://localhost:8000/admin_config.html")
        
        print("\n🔧 API ENDPOINTS:")
        print("  Health Check: http://localhost:8000/api/health")
        print("  Clients: http://localhost:8000/api/clients")
        print("  Vendeurs: http://localhost:8000/api/vendeurs")
        print("  Statistiques: http://localhost:8000/api/dashboard-stats")
        
        print("\n💡 OPTIMISATIONS ACTIVES:")
        print("  ✅ Pool de connexions (10 connexions)")
        print("  ✅ Index de performance")
        print("  ✅ Cache intelligent")
        print("  ✅ Logging avancé")
        print("  ✅ Validation robuste")
        print("  ✅ Health monitoring")
        
        print("\n🔒 SÉCURITÉ:")
        print("  ✅ Validation des entrées")
        print("  ✅ Hashage des mots de passe")
        print("  ⚠️  HTTPS recommandé pour production")
        print("  ⚠️  2FA recommandé pour production")
        
        print("\n" + "="*60)
        print("Appuyez sur Ctrl+C pour arrêter le système")
        print("="*60)
    
    def start_system(self):
        """Démarrer le système complet"""
        print("🚀 DÉMARRAGE DU SYSTÈME BINANCE CRM OPTIMISÉ")
        print("="*50)
        
        # Vérifications préalables
        if not self.check_prerequisites():
            print("❌ Prérequis non satisfaits - Arrêt")
            return False
        
        # Configuration
        self.setup_logging()
        self.initialize_cache()
        
        # Démarrage des serveurs
        servers_started = 0
        
        if self.start_database_server():
            servers_started += 1
        
        if self.start_email_server():
            servers_started += 1
        
        if self.start_pdf_server():
            servers_started += 1
        
        if servers_started == 0:
            print("❌ Aucun serveur n'a pu démarrer")
            return False
        
        # Test des serveurs
        time.sleep(3)  # Attendre que tous les serveurs soient prêts
        self.test_servers()
        
        # Afficher les informations
        self.display_startup_info()
        
        try:
            # Garder le système en vie
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            print("\n\n🛑 Arrêt du système demandé...")
            print("✅ Système BINANCE CRM arrêté proprement")
            return True

def main():
    """Point d'entrée principal"""
    starter = OptimizedCRMStarter()
    starter.start_system()

if __name__ == "__main__":
    main()

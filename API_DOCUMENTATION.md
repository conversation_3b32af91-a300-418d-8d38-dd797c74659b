# 📚 Documentation API - CRM System

Cette documentation décrit les endpoints API disponibles dans le système CRM.

## 🔐 Authentification

L'API utilise des sessions basées sur des cookies. Vous devez d'abord vous connecter via l'interface web ou l'endpoint de connexion.

### Connexion
```http
POST /login
Content-Type: application/x-www-form-urlencoded

username=admin&password=admin123
```

### Déconnexion
```http
GET /logout
```

## 👥 Gestion des Clients

### Lister les clients
```http
GET /api/clients?vendeur_id=1&indicateur=nouveau&limit=50&skip=0
```

**Paramètres de requête :**
- `vendeur_id` (optionnel) : ID du vendeur
- `indicateur` (optionnel) : Filtrer par indicateur
- `email_envoye` (optionnel) : true/false
- `limit` (optionnel) : Nombre de résultats (défaut: 100)
- `skip` (optionnel) : Nombre de résultats à ignorer (défaut: 0)

### Créer un client
```http
POST /api/clients
Content-Type: application/json

{
    "nom": "<PERSON><PERSON>",
    "prenom": "<PERSON>",
    "email": "<EMAIL>",
    "telephone": "***********.89",
    "date_naissance": "1980-05-15T00:00:00",
    "adresse": "123 Rue de la Paix, Paris",
    "vendeur_id": 1,
    "indicateur": "nouveau",
    "note": "Client potentiel"
}
```

### Modifier un client
```http
PUT /api/clients/{client_id}
Content-Type: application/json

{
    "indicateur": "magnifique",
    "note": "Client très intéressé",
    "email_envoye": true
}
```

### Supprimer un client (Admin seulement)
```http
DELETE /api/clients/{client_id}
```

### Attribuer des clients à un vendeur
```http
POST /api/clients/assign
Content-Type: application/json

{
    "client_ids": [1, 2, 3],
    "vendeur_id": 2
}
```

## 📊 Import/Export

### Importer des clients (CSV)
```http
POST /api/clients/import
Content-Type: multipart/form-data

file: clients.csv
```

**Format CSV requis :**
```csv
nom,prenom,email,telephone,date_naissance,adresse,vendeur_id,indicateur
Dupont,Jean,<EMAIL>,0123456789,1980-05-15,"123 Rue de la Paix",1,nouveau
```

**Colonnes obligatoires :** nom, prenom, email, telephone, date_naissance
**Colonnes optionnelles :** adresse, vendeur_id, indicateur

### Exporter des clients (CSV)
```http
GET /api/clients/export?vendeur_id=1&indicateur=magnifique&email_envoye=true
```

## 📧 Gestion des Emails

### Envoyer un email
```http
POST /api/emails/send
Content-Type: application/json

{
    "client_id": 1,
    "template_id": 1
}
```

### Lister les templates d'email
```http
GET /api/templates
```

### Créer un template d'email
```http
POST /api/templates
Content-Type: application/json

{
    "nom": "Premier Contact",
    "sujet": "Bonjour {prenom}",
    "contenu": "<p>Bonjour {prenom} {nom},</p><p>...</p>",
    "variables": "prenom, nom, email"
}
```

### Modifier un template
```http
PUT /api/templates/{template_id}
Content-Type: application/json

{
    "sujet": "Nouveau sujet {prenom}",
    "contenu": "<p>Nouveau contenu...</p>"
}
```

### Supprimer un template
```http
DELETE /api/templates/{template_id}
```

## 📅 Gestion des Rendez-vous

### Lister les rendez-vous
```http
GET /api/appointments?vendeur_id=1&date_debut=2023-12-01&date_fin=2023-12-31
```

### Créer un rendez-vous
```http
POST /api/appointments
Content-Type: application/json

{
    "client_id": 1,
    "vendeur_id": 1,
    "date_rdv": "2023-12-25T14:30:00",
    "titre": "Rendez-vous commercial",
    "description": "Présentation des services",
    "statut": "planifie"
}
```

### Modifier un rendez-vous
```http
PUT /api/appointments/{appointment_id}
Content-Type: application/json

{
    "statut": "realise",
    "description": "RDV réalisé avec succès"
}
```

### Supprimer un rendez-vous
```http
DELETE /api/appointments/{appointment_id}
```

## ⚙️ Configuration SMTP

### Configurer SMTP
```http
POST /api/smtp/config
Content-Type: application/json

{
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "your-app-password",
    "use_tls": true,
    "from_email": "<EMAIL>",
    "from_name": "CRM System"
}
```

### Tester la configuration SMTP
```http
POST /api/smtp/test
```

## 👤 Gestion des Utilisateurs (Admin seulement)

### Créer un utilisateur
```http
POST /api/users
Content-Type: application/json

{
    "username": "nouveau.vendeur",
    "email": "<EMAIL>",
    "password": "motdepasse123",
    "role": "vendeur"
}
```

### Modifier un utilisateur
```http
PUT /api/users/{user_id}
Content-Type: application/json

{
    "role": "admin",
    "is_active": true
}
```

## 📈 Statistiques

### Obtenir les statistiques
```http
GET /api/stats
```

**Réponse :**
```json
{
    "total_clients": 150,
    "clients_attribues": 120,
    "clients_non_attribues": 30,
    "emails_envoyes": 85,
    "rdv_planifies": 25,
    "rdv_realises": 18,
    "indicateurs": {
        "nouveau": 45,
        "en cours": 35,
        "magnifique": 25,
        "NRP": 20,
        "client mort": 25
    }
}
```

## 🔍 Codes de Réponse HTTP

- `200 OK` : Succès
- `201 Created` : Ressource créée
- `400 Bad Request` : Données invalides
- `401 Unauthorized` : Non authentifié
- `403 Forbidden` : Accès refusé
- `404 Not Found` : Ressource non trouvée
- `422 Unprocessable Entity` : Erreur de validation
- `500 Internal Server Error` : Erreur serveur

## 📝 Variables de Template Email

Les templates d'email supportent les variables suivantes :

- `{prenom}` : Prénom du client
- `{nom}` : Nom du client
- `{email}` : Email du client
- `{telephone}` : Téléphone du client
- `{adresse}` : Adresse du client
- `{date_rdv}` : Date et heure du rendez-vous
- `{titre_rdv}` : Titre du rendez-vous
- `{description_rdv}` : Description du rendez-vous
- `{date_actuelle}` : Date actuelle
- `{heure_actuelle}` : Heure actuelle

## 🛡️ Permissions

### Admin
- Accès complet à tous les endpoints
- Peut voir et modifier tous les clients
- Peut gérer les utilisateurs
- Peut configurer SMTP et templates

### Vendeur
- Accès limité aux clients qui lui sont attribués
- Peut créer/modifier ses rendez-vous
- Peut envoyer des emails à ses clients
- Ne peut pas gérer les utilisateurs ou la configuration

## 📋 Exemples d'Utilisation

### Workflow typique pour un vendeur

1. **Connexion**
```bash
curl -X POST http://localhost:8000/login \
  -d "username=marie.martin&password=vendeur123"
```

2. **Lister ses clients**
```bash
curl http://localhost:8000/api/clients
```

3. **Mettre à jour un indicateur**
```bash
curl -X PUT http://localhost:8000/api/clients/1 \
  -H "Content-Type: application/json" \
  -d '{"indicateur": "magnifique"}'
```

4. **Envoyer un email**
```bash
curl -X POST http://localhost:8000/api/emails/send \
  -H "Content-Type: application/json" \
  -d '{"client_id": 1, "template_id": 1}'
```

5. **Planifier un RDV**
```bash
curl -X POST http://localhost:8000/api/appointments \
  -H "Content-Type: application/json" \
  -d '{
    "client_id": 1,
    "date_rdv": "2023-12-25T14:30:00",
    "titre": "RDV commercial",
    "description": "Présentation produits"
  }'
```

### Workflow typique pour un admin

1. **Import de clients**
```bash
curl -X POST http://localhost:8000/api/clients/import \
  -F "file=@clients.csv"
```

2. **Attribution en lot**
```bash
curl -X POST http://localhost:8000/api/clients/assign \
  -H "Content-Type: application/json" \
  -d '{"client_ids": [1,2,3], "vendeur_id": 2}'
```

3. **Configuration SMTP**
```bash
curl -X POST http://localhost:8000/api/smtp/config \
  -H "Content-Type: application/json" \
  -d '{
    "host": "smtp.gmail.com",
    "port": 587,
    "username": "<EMAIL>",
    "password": "app-password",
    "use_tls": true,
    "from_email": "<EMAIL>"
  }'
```

## 🔧 Documentation Interactive

Une fois l'application démarrée, vous pouvez accéder à la documentation interactive Swagger à l'adresse :

**http://localhost:8000/docs**

Cette interface permet de tester directement tous les endpoints de l'API.

# 🏢 CRM System - Application FastAPI Complète

Une application CRM (Customer Relationship Management) complète développée avec FastAPI, offrant une gestion avancée des leads, des emails et des rendez-vous avec un système d'authentification basé sur les rôles.

## 🚀 Fonctionnalités Principales

### 🔐 Authentification & Autorisation
- **Rôle Admin** : Accès complet à toutes les fonctionnalités
- **Rôle Vendeur** : Accès restreint aux clients attribués uniquement
- Sessions sécurisées avec protection CSRF
- Interface de connexion intuitive

### 👥 Gestion des Clients (Leads)
- **Champs complets** : nom, prénom, email, téléphone, date de naissance, adresse
- **Attribution flexible** : manuelle ou par lot aux vendeurs
- **Indicateurs personnalisés** : "client mort", "NRP", "magnifique", etc.
- **Notes libres** : commentaires personnalisés par vendeur
- **Suivi des emails** : historique des envois et templates utilisés

### 📧 Système d'Emails Avancé
- **Templates dynamiques** : création et modification via interface
- **Variables personnalisables** : {prenom}, {nom}, {date_rdv}, etc.
- **Configuration SMTP** : paramétrage dynamique via formulaire sécurisé
- **Logs complets** : traçabilité de tous les envois
- **Support HTML/Texte** : emails riches ou simples

### 📅 Agenda Intégré
- **Planification** : création et modification de rendez-vous
- **Vue globale** (Admin) et **vue personnelle** (Vendeur)
- **Statuts** : planifié, réalisé, annulé
- **Historique** : consultation des rendez-vous passés

### 📊 Import/Export Intelligent
- **Import CSV** : upload de fichiers de contacts avec validation
- **Export filtré** : par date, indicateur, vendeur, etc.
- **Formats flexibles** : colonnes optionnelles et requises
- **Rapports d'erreur** : détail des problèmes d'import

### 📈 Tableaux de Bord
- **Statistiques temps réel** : nombre de leads, taux de contact, etc.
- **Graphiques** : répartition par indicateur, performance vendeurs
- **Actions rapides** : accès direct aux fonctions principales
- **Activité récente** : logs des dernières actions

## 🛠️ Stack Technique

- **Backend** : FastAPI 0.104+
- **Base de données** : SQLite (migratable vers PostgreSQL)
- **ORM** : SQLAlchemy 2.0
- **Templates** : Jinja2
- **Frontend** : Bootstrap 5 + JavaScript vanilla
- **Authentification** : Sessions sécurisées + JWT
- **Email** : SMTP configurable dynamiquement
- **Validation** : Pydantic schemas

## 📦 Installation et Démarrage

### Prérequis
- Python 3.8+
- pip

### Installation Rapide

#### Sur Linux/Mac :
```bash
# Cloner le projet
git clone <repository-url>
cd crm

# Rendre le script exécutable
chmod +x start.sh

# Démarrer en mode développement
./start.sh dev

# Ou en mode production
./start.sh prod
```

#### Sur Windows :
```cmd
# Cloner le projet
git clone <repository-url>
cd crm

# Démarrer en mode développement
start.bat dev

# Ou en mode production
start.bat prod
```

### Installation Manuelle

```bash
# Créer l'environnement virtuel
python -m venv venv

# Activer l'environnement
# Linux/Mac:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# Installer les dépendances
pip install -r requirements.txt

# Démarrer l'application
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 🔑 Première Connexion

Après le démarrage, accédez à : **http://localhost:8000**

**Compte administrateur par défaut :**
- **Utilisateur** : `admin`
- **Mot de passe** : `admin123`

⚠️ **Important** : Changez ce mot de passe après la première connexion !

## 📋 Guide d'Utilisation

### Pour les Administrateurs

1. **Configuration initiale** :
   - Configurez les paramètres SMTP dans `/admin/smtp`
   - Créez des templates d'email dans `/admin/templates`
   - Ajoutez des vendeurs dans `/admin/vendeurs`

2. **Gestion des leads** :
   - Importez des contacts via CSV dans `/admin/clients`
   - Attribuez les leads aux vendeurs (sélection multiple)
   - Suivez les statistiques dans le dashboard

3. **Supervision** :
   - Consultez l'activité globale
   - Exportez les données avec filtres
   - Gérez les utilisateurs et leurs permissions

### Pour les Vendeurs

1. **Gestion des clients** :
   - Consultez vos clients attribués dans `/vendeur/clients`
   - Mettez à jour les indicateurs et notes
   - Envoyez des emails avec les templates

2. **Planification** :
   - Créez des rendez-vous dans `/vendeur/agenda`
   - Suivez vos RDV à venir
   - Marquez les rendez-vous comme réalisés

3. **Suivi** :
   - Consultez vos statistiques personnelles
   - Exportez votre portefeuille client
   - Suivez l'historique des emails envoyés

## 🔧 Configuration Avancée

### Variables d'Environnement (.env)

```env
# Sécurité
SECRET_KEY=your-secret-key-here

# Base de données
DATABASE_URL=sqlite:///./crm.db
# Ou PostgreSQL: postgresql://user:password@localhost/crm

# SMTP (optionnel - configurable via interface)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=CRM System

# Debug
DEBUG=false
LOG_LEVEL=INFO
```

### Configuration SMTP Populaires

#### Gmail
- **Serveur** : `smtp.gmail.com`
- **Port** : `587`
- **TLS** : Oui
- **Note** : Utilisez un mot de passe d'application

#### Outlook/Hotmail
- **Serveur** : `smtp-mail.outlook.com`
- **Port** : `587`
- **TLS** : Oui

#### SendGrid
- **Serveur** : `smtp.sendgrid.net`
- **Port** : `587`
- **Username** : `apikey`
- **Password** : Votre clé API SendGrid

## 📁 Structure du Projet

```
crm/
├── main.py              # Application FastAPI principale
├── models.py            # Modèles SQLAlchemy
├── schemas.py           # Schémas Pydantic
├── crud.py              # Opérations base de données
├── auth.py              # Système d'authentification
├── database.py          # Configuration base de données
├── requirements.txt     # Dépendances Python
├── start.sh            # Script de démarrage Linux/Mac
├── start.bat           # Script de démarrage Windows
├── utils/
│   └── email.py        # Service d'envoi d'emails
├── templates/          # Templates HTML Jinja2
│   ├── base.html
│   ├── login.html
│   ├── admin/
│   │   ├── dashboard.html
│   │   ├── clients.html
│   │   ├── templates.html
│   │   └── smtp.html
│   └── vendeur/
│       ├── dashboard.html
│       ├── clients.html
│       └── agenda.html
└── static/
    └── style.css       # Styles personnalisés
```

## 🔒 Sécurité

- **Authentification** : Sessions sécurisées avec cookies HttpOnly
- **Autorisation** : Contrôle d'accès basé sur les rôles
- **Validation** : Validation stricte des données avec Pydantic
- **CSRF** : Protection contre les attaques CSRF
- **SQL Injection** : Protection via SQLAlchemy ORM
- **Mots de passe** : Hachage bcrypt

## 📊 API Endpoints

### Authentification
- `POST /login` - Connexion
- `GET /logout` - Déconnexion

### Clients
- `GET /api/clients` - Liste des clients
- `POST /api/clients` - Créer un client
- `PUT /api/clients/{id}` - Modifier un client
- `DELETE /api/clients/{id}` - Supprimer un client
- `POST /api/clients/assign` - Attribuer des clients
- `POST /api/clients/import` - Import CSV
- `GET /api/clients/export` - Export CSV

### Templates Email
- `GET /api/templates` - Liste des templates
- `POST /api/templates` - Créer un template
- `PUT /api/templates/{id}` - Modifier un template
- `DELETE /api/templates/{id}` - Supprimer un template

### Emails
- `POST /api/emails/send` - Envoyer un email

### Configuration
- `POST /api/smtp/config` - Configurer SMTP
- `POST /api/smtp/test` - Tester SMTP

## 🚀 Déploiement en Production

### Avec Docker (Recommandé)

```dockerfile
FROM python:3.11-slim

WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt

COPY . .

EXPOSE 8000
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
```

### Avec Gunicorn

```bash
# Installation
pip install gunicorn

# Démarrage
gunicorn main:app \
    --host 0.0.0.0 \
    --port 8000 \
    --workers 4 \
    --worker-class uvicorn.workers.UvicornWorker
```

## 🤝 Contribution

1. Fork le projet
2. Créez une branche feature (`git checkout -b feature/AmazingFeature`)
3. Committez vos changements (`git commit -m 'Add AmazingFeature'`)
4. Push vers la branche (`git push origin feature/AmazingFeature`)
5. Ouvrez une Pull Request

## 📝 Licence

Ce projet est sous licence MIT. Voir le fichier `LICENSE` pour plus de détails.

## 🆘 Support

Pour toute question ou problème :
1. Consultez la documentation
2. Vérifiez les logs dans le dossier `logs/`
3. Ouvrez une issue sur GitHub

---

**Développé avec ❤️ pour simplifier la gestion de vos leads et améliorer vos ventes !**

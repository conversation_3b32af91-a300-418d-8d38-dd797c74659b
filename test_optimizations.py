#!/usr/bin/env python3
"""
BINANCE CRM - Test des Optimisations
Script pour valider toutes les optimisations de performance
"""

import time
import requests
import json
import sqlite3
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
import statistics

class OptimizationTester:
    def __init__(self):
        self.base_url = "http://localhost"
        self.results = {}
        
    def test_database_optimizations(self):
        """Tester les optimisations de base de données"""
        print("🗄️ Test des optimisations de base de données...")
        
        # Test du pool de connexions
        conn = sqlite3.connect('binance_crm.db')
        cursor = conn.cursor()
        
        # Vérifier les index optimisés
        cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
        index_count = cursor.fetchone()[0]
        
        # Test de performance des requêtes
        start_time = time.time()
        cursor.execute("""
            SELECT c.*, u.first_name, u.last_name 
            FROM clients c 
            LEFT JOIN users u ON c.assigned_to = u.id 
            LIMIT 50
        """)
        results = cursor.fetchall()
        query_time = (time.time() - start_time) * 1000
        
        conn.close()
        
        self.results['database'] = {
            'index_count': index_count,
            'query_time_ms': round(query_time, 2),
            'result_count': len(results),
            'status': 'optimized' if index_count >= 15 else 'needs_improvement'
        }
        
        print(f"   ✅ Index créés: {index_count}")
        print(f"   ✅ Temps de requête: {query_time:.2f}ms")
        
    def test_api_performance(self):
        """Tester les performances des API"""
        print("🌐 Test des performances API...")
        
        endpoints = [
            f"{self.base_url}:8001/api/clients",
            f"{self.base_url}:8001/api/users",
            f"{self.base_url}:8001/api/dashboard-stats"
        ]
        
        api_results = {}
        
        for endpoint in endpoints:
            times = []
            compressed_responses = 0
            
            for _ in range(5):
                try:
                    start_time = time.time()
                    response = requests.get(endpoint, headers={'Accept-Encoding': 'gzip'})
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        times.append((end_time - start_time) * 1000)
                        if response.headers.get('Content-Encoding') == 'gzip':
                            compressed_responses += 1
                except Exception as e:
                    print(f"   ⚠️ Erreur pour {endpoint}: {e}")
            
            if times:
                endpoint_name = endpoint.split('/')[-1]
                api_results[endpoint_name] = {
                    'avg_time_ms': round(statistics.mean(times), 2),
                    'min_time_ms': round(min(times), 2),
                    'max_time_ms': round(max(times), 2),
                    'compression_rate': (compressed_responses / 5) * 100
                }
                print(f"   ✅ {endpoint_name}: {api_results[endpoint_name]['avg_time_ms']}ms (compression: {api_results[endpoint_name]['compression_rate']}%)")
        
        self.results['api'] = api_results
        
    def test_concurrent_load(self):
        """Tester la charge concurrente"""
        print("⚡ Test de charge concurrente...")
        
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}:8001/api/clients?limit=25")
                end_time = time.time()
                return (end_time - start_time) * 1000, response.status_code == 200
            except:
                return None, False
        
        # Test avec 20 requêtes concurrentes
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = []
            success_count = 0
            
            for future in as_completed(futures):
                time_ms, success = future.result()
                if time_ms is not None:
                    results.append(time_ms)
                if success:
                    success_count += 1
        
        if results:
            self.results['concurrent'] = {
                'requests': 20,
                'successful': success_count,
                'success_rate': (success_count / 20) * 100,
                'avg_time_ms': round(statistics.mean(results), 2),
                'max_time_ms': round(max(results), 2),
                'throughput': round(20 / (max(results) / 1000), 2)
            }
            print(f"   ✅ Succès: {success_count}/20 ({self.results['concurrent']['success_rate']}%)")
            print(f"   ✅ Temps moyen: {self.results['concurrent']['avg_time_ms']}ms")
            print(f"   ✅ Débit: {self.results['concurrent']['throughput']} req/s")
        
    def test_frontend_optimizations(self):
        """Tester les optimisations frontend"""
        print("🖥️ Test des optimisations frontend...")
        
        # Vérifier l'existence des fichiers optimisés
        import os
        
        optimized_files = {
            'css_minified': os.path.exists('assets/css/optimized.min.css'),
            'js_minified': os.path.exists('assets/js/optimized.min.js'),
            'clients_html': os.path.exists('clients.html')
        }
        
        # Vérifier la taille des fichiers
        file_sizes = {}
        if optimized_files['css_minified']:
            file_sizes['css_size_kb'] = round(os.path.getsize('assets/css/optimized.min.css') / 1024, 2)
        if optimized_files['js_minified']:
            file_sizes['js_size_kb'] = round(os.path.getsize('assets/js/optimized.min.js') / 1024, 2)
        
        self.results['frontend'] = {
            'optimized_files': optimized_files,
            'file_sizes': file_sizes,
            'status': 'optimized' if all(optimized_files.values()) else 'partial'
        }
        
        for file_type, exists in optimized_files.items():
            status = "✅" if exists else "❌"
            print(f"   {status} {file_type}: {'Présent' if exists else 'Manquant'}")
        
    def test_cache_functionality(self):
        """Tester le fonctionnement du cache"""
        print("💾 Test du système de cache...")
        
        # Test de deux requêtes identiques pour vérifier le cache
        endpoint = f"{self.base_url}:8001/api/users"
        
        # Première requête (mise en cache)
        start_time = time.time()
        response1 = requests.get(endpoint)
        first_request_time = (time.time() - start_time) * 1000
        
        time.sleep(0.1)  # Petite pause
        
        # Deuxième requête (depuis le cache)
        start_time = time.time()
        response2 = requests.get(endpoint)
        second_request_time = (time.time() - start_time) * 1000
        
        cache_improvement = ((first_request_time - second_request_time) / first_request_time) * 100
        
        self.results['cache'] = {
            'first_request_ms': round(first_request_time, 2),
            'second_request_ms': round(second_request_time, 2),
            'improvement_percent': round(cache_improvement, 1),
            'cache_working': cache_improvement > 10  # Au moins 10% d'amélioration
        }
        
        print(f"   ✅ Première requête: {first_request_time:.2f}ms")
        print(f"   ✅ Deuxième requête: {second_request_time:.2f}ms")
        print(f"   ✅ Amélioration cache: {cache_improvement:.1f}%")
        
    def run_all_tests(self):
        """Exécuter tous les tests d'optimisation"""
        print("🚀 DÉMARRAGE DES TESTS D'OPTIMISATION")
        print("="*50)
        
        try:
            self.test_database_optimizations()
            print()
            self.test_api_performance()
            print()
            self.test_concurrent_load()
            print()
            self.test_frontend_optimizations()
            print()
            self.test_cache_functionality()
            print()
            
        except Exception as e:
            print(f"❌ Erreur lors des tests: {e}")
            
        self.print_summary()
        
    def print_summary(self):
        """Afficher un résumé des résultats"""
        print("="*50)
        print("📊 RÉSUMÉ DES TESTS D'OPTIMISATION")
        print("="*50)
        
        # Score global
        total_score = 0
        max_score = 0
        
        if 'database' in self.results:
            db_score = 20 if self.results['database']['status'] == 'optimized' else 10
            total_score += db_score
            max_score += 20
            print(f"🗄️ Base de données: {db_score}/20 ({self.results['database']['status']})")
        
        if 'api' in self.results:
            api_score = len(self.results['api']) * 5
            total_score += api_score
            max_score += 15
            print(f"🌐 API Performance: {api_score}/15")
        
        if 'concurrent' in self.results:
            concurrent_score = 15 if self.results['concurrent']['success_rate'] >= 90 else 10
            total_score += concurrent_score
            max_score += 15
            print(f"⚡ Charge concurrente: {concurrent_score}/15")
        
        if 'frontend' in self.results:
            frontend_score = 15 if self.results['frontend']['status'] == 'optimized' else 8
            total_score += frontend_score
            max_score += 15
            print(f"🖥️ Frontend: {frontend_score}/15")
        
        if 'cache' in self.results:
            cache_score = 15 if self.results['cache']['cache_working'] else 5
            total_score += cache_score
            max_score += 15
            print(f"💾 Cache: {cache_score}/15")
        
        print(f"\n🏆 SCORE GLOBAL: {total_score}/{max_score} ({(total_score/max_score)*100:.1f}%)")
        
        if total_score >= max_score * 0.8:
            print("✅ EXCELLENT - Toutes les optimisations fonctionnent parfaitement!")
        elif total_score >= max_score * 0.6:
            print("⚠️ BON - La plupart des optimisations sont actives")
        else:
            print("❌ ATTENTION - Plusieurs optimisations nécessitent des corrections")
        
        # Sauvegarder les résultats
        with open('optimization_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        print(f"\n📄 Résultats sauvegardés dans: optimization_test_results.json")

if __name__ == "__main__":
    print("🔍 VALIDATION DES OPTIMISATIONS BINANCE CRM")
    print("Assurez-vous que tous les serveurs sont démarrés avant de continuer.")
    input("Appuyez sur Entrée pour commencer les tests...")
    
    tester = OptimizationTester()
    tester.run_all_tests()

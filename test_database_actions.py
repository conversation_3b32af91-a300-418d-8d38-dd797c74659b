#!/usr/bin/env python3
"""
BINANCE CRM - Test Complet des Actions de Base de Données
Script pour tester toutes les actions CRUD et fonctionnalités avancées
"""

import requests
import json
import time
import random

class DatabaseActionTester:
    def __init__(self, base_url="http://localhost:8001"):
        self.base_url = base_url
        self.test_results = {}
        self.test_client_id = None
        self.test_user_id = None
        self.test_template_id = None
        
    def test_endpoint(self, method, endpoint, data=None, expected_status=200):
        """Tester un endpoint avec gestion d'erreurs"""
        url = f"{self.base_url}{endpoint}"
        
        try:
            if method == 'GET':
                response = requests.get(url)
            elif method == 'POST':
                response = requests.post(url, json=data)
            elif method == 'PUT':
                response = requests.put(url, json=data)
            elif method == 'DELETE':
                response = requests.delete(url)
            
            success = response.status_code == expected_status
            result_data = response.json() if response.headers.get('content-type', '').startswith('application/json') else None
            
            return {
                'success': success,
                'status_code': response.status_code,
                'data': result_data,
                'response_time': response.elapsed.total_seconds() * 1000
            }
            
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'response_time': 0
            }
    
    def test_clients_crud(self):
        """Tester toutes les opérations CRUD sur les clients"""
        print("🧪 Test des opérations CRUD - Clients")
        
        results = {}
        
        # 1. CREATE - Créer un client
        client_data = {
            'first_name': 'Test',
            'last_name': 'Client',
            'email': f'test.client.{random.randint(1000, 9999)}@example.com',
            'phone': '0123456789',
            'status': 'nouveau',
            'notes': 'Client de test'
        }
        
        result = self.test_endpoint('POST', '/api/clients', client_data)
        results['create'] = result
        
        if result['success'] and result['data'].get('success'):
            self.test_client_id = result['data'].get('client_id')
            print(f"   ✅ CREATE: Client créé (ID: {self.test_client_id})")
        else:
            print(f"   ❌ CREATE: Échec - {result.get('error', 'Erreur inconnue')}")
        
        # 2. READ - Lire tous les clients
        result = self.test_endpoint('GET', '/api/clients')
        results['read_all'] = result
        
        if result['success']:
            client_count = len(result['data'].get('clients', []))
            print(f"   ✅ READ ALL: {client_count} clients récupérés")
        else:
            print(f"   ❌ READ ALL: Échec")
        
        # 3. READ - Lire un client spécifique
        if self.test_client_id:
            result = self.test_endpoint('GET', f'/api/clients/{self.test_client_id}')
            results['read_one'] = result
            
            if result['success']:
                print(f"   ✅ READ ONE: Client {self.test_client_id} récupéré")
            else:
                print(f"   ❌ READ ONE: Échec")
        
        # 4. UPDATE - Mettre à jour le client
        if self.test_client_id:
            update_data = {
                'first_name': 'Test Updated',
                'status': 'client',
                'notes': 'Client de test mis à jour'
            }
            
            result = self.test_endpoint('PUT', f'/api/clients/{self.test_client_id}', update_data)
            results['update'] = result
            
            if result['success']:
                print(f"   ✅ UPDATE: Client {self.test_client_id} mis à jour")
            else:
                print(f"   ❌ UPDATE: Échec")
        
        # 5. DELETE - Supprimer le client (à la fin)
        # On garde le client pour les autres tests
        
        self.test_results['clients_crud'] = results
    
    def test_bulk_operations(self):
        """Tester les opérations en lot"""
        print("🧪 Test des opérations en lot")
        
        results = {}
        
        # 1. Création en lot
        bulk_clients = []
        for i in range(3):
            bulk_clients.append({
                'first_name': f'Bulk{i+1}',
                'last_name': 'Test',
                'email': f'bulk.test.{i+1}.{random.randint(1000, 9999)}@example.com',
                'status': 'nouveau'
            })
        
        result = self.test_endpoint('POST', '/api/clients/bulk-create', {'clients': bulk_clients})
        results['bulk_create'] = result
        
        if result['success']:
            created_count = result['data'].get('created_count', 0)
            print(f"   ✅ BULK CREATE: {created_count} clients créés")
        else:
            print(f"   ❌ BULK CREATE: Échec")
        
        # 2. Mise à jour de statut en lot
        if self.test_client_id:
            result = self.test_endpoint('POST', '/api/clients/bulk-update-status', {
                'client_ids': [self.test_client_id],
                'status': 'prospect'
            })
            results['bulk_update_status'] = result
            
            if result['success']:
                print(f"   ✅ BULK UPDATE STATUS: Statut mis à jour")
            else:
                print(f"   ❌ BULK UPDATE STATUS: Échec")
        
        # 3. Export CSV
        result = self.test_endpoint('POST', '/api/clients/export', {'filters': {}})
        results['export_csv'] = result
        
        if result['success']:
            export_count = result['data'].get('count', 0)
            print(f"   ✅ EXPORT CSV: {export_count} clients exportés")
        else:
            print(f"   ❌ EXPORT CSV: Échec")
        
        self.test_results['bulk_operations'] = results
    
    def test_email_templates(self):
        """Tester les templates d'email"""
        print("🧪 Test des templates d'email")
        
        results = {}
        
        # 1. Créer un template
        template_data = {
            'name': 'Template de Test',
            'subject': 'Sujet de test',
            'content': 'Bonjour {{first_name}}, ceci est un test.',
            'created_by': 1
        }
        
        result = self.test_endpoint('POST', '/api/email-templates', template_data)
        results['create_template'] = result
        
        if result['success'] and result['data'].get('success'):
            self.test_template_id = result['data'].get('template_id')
            print(f"   ✅ CREATE TEMPLATE: Template créé (ID: {self.test_template_id})")
        else:
            print(f"   ❌ CREATE TEMPLATE: Échec")
        
        # 2. Lire les templates
        result = self.test_endpoint('GET', '/api/email-templates')
        results['read_templates'] = result
        
        if result['success']:
            template_count = len(result['data'].get('data', []))
            print(f"   ✅ READ TEMPLATES: {template_count} templates récupérés")
        else:
            print(f"   ❌ READ TEMPLATES: Échec")
        
        # 3. Mettre à jour le template
        if self.test_template_id:
            update_data = {
                'name': 'Template de Test Modifié',
                'subject': 'Sujet modifié',
                'content': 'Contenu modifié pour {{first_name}}'
            }
            
            result = self.test_endpoint('PUT', f'/api/email-templates/{self.test_template_id}', update_data)
            results['update_template'] = result
            
            if result['success']:
                print(f"   ✅ UPDATE TEMPLATE: Template {self.test_template_id} mis à jour")
            else:
                print(f"   ❌ UPDATE TEMPLATE: Échec")
        
        self.test_results['email_templates'] = results
    
    def test_system_operations(self):
        """Tester les opérations système"""
        print("🧪 Test des opérations système")
        
        results = {}
        
        # 1. Statistiques du dashboard
        result = self.test_endpoint('GET', '/api/dashboard-stats')
        results['dashboard_stats'] = result
        
        if result['success']:
            print(f"   ✅ DASHBOARD STATS: Statistiques récupérées")
        else:
            print(f"   ❌ DASHBOARD STATS: Échec")
        
        # 2. Santé du système
        result = self.test_endpoint('GET', '/api/health')
        results['health_check'] = result
        
        if result['success']:
            status = result['data'].get('status', 'unknown')
            print(f"   ✅ HEALTH CHECK: Statut {status}")
        else:
            print(f"   ❌ HEALTH CHECK: Échec")
        
        # 3. Sauvegarde
        result = self.test_endpoint('POST', '/api/backup')
        results['backup'] = result
        
        if result['success']:
            print(f"   ✅ BACKUP: Sauvegarde créée")
        else:
            print(f"   ❌ BACKUP: Échec")
        
        # 4. Optimisation
        result = self.test_endpoint('POST', '/api/optimize')
        results['optimize'] = result
        
        if result['success']:
            print(f"   ✅ OPTIMIZE: Base de données optimisée")
        else:
            print(f"   ❌ OPTIMIZE: Échec")
        
        self.test_results['system_operations'] = results
    
    def cleanup_test_data(self):
        """Nettoyer les données de test"""
        print("🧹 Nettoyage des données de test")
        
        # Supprimer le client de test
        if self.test_client_id:
            result = self.test_endpoint('DELETE', f'/api/clients/{self.test_client_id}')
            if result['success']:
                print(f"   ✅ Client de test {self.test_client_id} supprimé")
        
        # Supprimer le template de test
        if self.test_template_id:
            result = self.test_endpoint('DELETE', f'/api/email-templates/{self.test_template_id}')
            if result['success']:
                print(f"   ✅ Template de test {self.test_template_id} supprimé")
    
    def run_all_tests(self):
        """Exécuter tous les tests"""
        print("🚀 DÉMARRAGE DES TESTS D'ACTIONS DE BASE DE DONNÉES")
        print("="*60)
        
        start_time = time.time()
        
        try:
            self.test_clients_crud()
            print()
            self.test_bulk_operations()
            print()
            self.test_email_templates()
            print()
            self.test_system_operations()
            print()
            
        except Exception as e:
            print(f"❌ Erreur lors des tests: {e}")
        
        finally:
            self.cleanup_test_data()
        
        end_time = time.time()
        total_time = end_time - start_time
        
        self.print_summary(total_time)
    
    def print_summary(self, total_time):
        """Afficher un résumé des résultats"""
        print("="*60)
        print("📊 RÉSUMÉ DES TESTS D'ACTIONS DE BASE DE DONNÉES")
        print("="*60)
        
        total_tests = 0
        passed_tests = 0
        
        for category, tests in self.test_results.items():
            print(f"\n📂 {category.upper().replace('_', ' ')}")
            for test_name, result in tests.items():
                total_tests += 1
                if result.get('success', False):
                    passed_tests += 1
                    status = "✅"
                else:
                    status = "❌"
                
                response_time = result.get('response_time', 0)
                print(f"   {status} {test_name}: {response_time:.0f}ms")
        
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0
        
        print(f"\n🏆 RÉSULTAT GLOBAL:")
        print(f"   Tests réussis: {passed_tests}/{total_tests} ({success_rate:.1f}%)")
        print(f"   Temps total: {total_time:.2f}s")
        
        if success_rate >= 90:
            print("✅ EXCELLENT - Toutes les actions de base de données fonctionnent parfaitement!")
        elif success_rate >= 70:
            print("⚠️ BON - La plupart des actions fonctionnent correctement")
        else:
            print("❌ ATTENTION - Plusieurs actions nécessitent des corrections")
        
        # Sauvegarder les résultats
        with open('database_actions_test_results.json', 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        print(f"\n📄 Résultats détaillés sauvegardés dans: database_actions_test_results.json")

if __name__ == "__main__":
    print("🔍 TEST COMPLET DES ACTIONS DE BASE DE DONNÉES - BINANCE CRM")
    print("Assurez-vous que le serveur de base de données est démarré sur le port 8001")
    input("Appuyez sur Entrée pour commencer les tests...")
    
    tester = DatabaseActionTester()
    tester.run_all_tests()

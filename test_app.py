#!/usr/bin/env python3
"""
Script de test pour vérifier le bon fonctionnement de l'application CRM
"""

import sys
import importlib.util
from datetime import datetime

def test_imports():
    """Test des imports des modules principaux"""
    print("🔍 Test des imports...")
    
    modules_to_test = [
        'fastapi',
        'sqlalchemy',
        'pydantic',
        'passlib',
        'jinja2',
        'uvicorn',
        'pandas',
        'python_multipart'
    ]
    
    missing_modules = []
    
    for module_name in modules_to_test:
        try:
            # Remplacer les tirets par des underscores pour l'import
            import_name = module_name.replace('-', '_')
            spec = importlib.util.find_spec(import_name)
            if spec is None:
                missing_modules.append(module_name)
            else:
                print(f"  ✅ {module_name}")
        except ImportError:
            missing_modules.append(module_name)
    
    if missing_modules:
        print(f"\n❌ Modules manquants: {', '.join(missing_modules)}")
        print("Installez-les avec: pip install -r requirements.txt")
        return False
    
    print("✅ Tous les modules requis sont disponibles")
    return True

def test_local_imports():
    """Test des imports des modules locaux"""
    print("\n🔍 Test des modules locaux...")
    
    local_modules = [
        'database',
        'models',
        'schemas',
        'crud',
        'auth',
        'main'
    ]
    
    for module_name in local_modules:
        try:
            __import__(module_name)
            print(f"  ✅ {module_name}")
        except ImportError as e:
            print(f"  ❌ {module_name}: {e}")
            return False
    
    print("✅ Tous les modules locaux sont importables")
    return True

def test_database():
    """Test de la base de données"""
    print("\n🔍 Test de la base de données...")
    
    try:
        from database import create_tables, get_db
        from models import User, Client, EmailTemplate
        
        # Créer les tables
        create_tables()
        print("  ✅ Tables créées")
        
        # Test de connexion
        db = next(get_db())
        print("  ✅ Connexion à la base de données")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur base de données: {e}")
        return False

def test_crud_operations():
    """Test des opérations CRUD"""
    print("\n🔍 Test des opérations CRUD...")
    
    try:
        import crud
        import schemas
        from database import get_db
        
        db = next(get_db())
        
        # Test création utilisateur
        user_data = schemas.UserCreate(
            username="test_user",
            email="<EMAIL>",
            password="test123",
            role="vendeur"
        )
        
        # Vérifier si l'utilisateur existe déjà
        existing_user = crud.get_user_by_username(db, "test_user")
        if not existing_user:
            user = crud.create_user(db, user_data)
            print("  ✅ Création utilisateur")
        else:
            print("  ✅ Utilisateur de test existe déjà")
        
        # Test création client
        client_data = schemas.ClientCreate(
            nom="Test",
            prenom="Client",
            email="<EMAIL>",
            telephone="0123456789",
            date_naissance=datetime(1990, 1, 1),
            adresse="Adresse de test"
        )
        
        # Vérifier si le client existe déjà
        existing_clients = crud.get_clients(db, limit=1)
        if not any(c.email == "<EMAIL>" for c in existing_clients):
            client = crud.create_client(db, client_data)
            print("  ✅ Création client")
        else:
            print("  ✅ Client de test existe déjà")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur CRUD: {e}")
        return False

def test_email_service():
    """Test du service email"""
    print("\n🔍 Test du service email...")
    
    try:
        from utils.email import EmailService, get_default_variables, get_available_variables
        
        # Test des variables
        variables = get_available_variables()
        print(f"  ✅ Variables disponibles: {len(variables)}")
        
        # Test des variables par défaut
        client_data = {
            'prenom': 'Jean',
            'nom': 'Dupont',
            'email': '<EMAIL>',
            'telephone': '0123456789',
            'adresse': 'Test'
        }
        
        default_vars = get_default_variables(client_data)
        print(f"  ✅ Variables par défaut générées: {len(default_vars)}")
        
        # Test de création du service (sans test de connexion)
        smtp_config = {
            'host': 'smtp.test.com',
            'port': 587,
            'username': 'test',
            'password': 'test',
            'use_tls': True,
            'from_email': '<EMAIL>',
            'from_name': 'Test'
        }
        
        email_service = EmailService(smtp_config)
        print("  ✅ Service email initialisé")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur service email: {e}")
        return False

def test_templates():
    """Test des templates"""
    print("\n🔍 Test des templates...")
    
    try:
        from jinja2 import Environment, FileSystemLoader
        import os
        
        # Vérifier que le dossier templates existe
        if not os.path.exists('templates'):
            print("  ❌ Dossier templates manquant")
            return False
        
        # Vérifier les templates principaux
        required_templates = [
            'base.html',
            'login.html',
            'admin/dashboard.html',
            'admin/clients.html',
            'vendeur/dashboard.html',
            'vendeur/clients.html'
        ]
        
        for template in required_templates:
            template_path = os.path.join('templates', template)
            if os.path.exists(template_path):
                print(f"  ✅ {template}")
            else:
                print(f"  ❌ {template} manquant")
                return False
        
        # Test de chargement des templates
        env = Environment(loader=FileSystemLoader('templates'))
        template = env.get_template('base.html')
        print("  ✅ Templates chargés avec succès")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur templates: {e}")
        return False

def test_static_files():
    """Test des fichiers statiques"""
    print("\n🔍 Test des fichiers statiques...")
    
    try:
        import os
        
        # Vérifier que le dossier static existe
        if not os.path.exists('static'):
            print("  ❌ Dossier static manquant")
            return False
        
        # Vérifier les fichiers CSS
        if os.path.exists('static/style.css'):
            print("  ✅ style.css")
        else:
            print("  ⚠️  style.css manquant (optionnel)")
        
        print("  ✅ Dossier static configuré")
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur fichiers statiques: {e}")
        return False

def test_app_startup():
    """Test de démarrage de l'application"""
    print("\n🔍 Test de démarrage de l'application...")
    
    try:
        from main import app
        print("  ✅ Application FastAPI créée")
        
        # Vérifier quelques routes importantes
        routes = [route.path for route in app.routes]
        
        important_routes = [
            '/',
            '/login',
            '/api/clients',
            '/api/templates',
            '/admin/dashboard',
            '/vendeur/dashboard'
        ]
        
        for route in important_routes:
            if route in routes:
                print(f"  ✅ Route {route}")
            else:
                print(f"  ❌ Route {route} manquante")
                return False
        
        return True
        
    except Exception as e:
        print(f"  ❌ Erreur démarrage app: {e}")
        return False

def main():
    """Fonction principale de test"""
    print("🚀 Test de l'application CRM")
    print("=" * 50)
    
    tests = [
        ("Imports des dépendances", test_imports),
        ("Modules locaux", test_local_imports),
        ("Base de données", test_database),
        ("Opérations CRUD", test_crud_operations),
        ("Service email", test_email_service),
        ("Templates", test_templates),
        ("Fichiers statiques", test_static_files),
        ("Démarrage application", test_app_startup)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}")
        print("-" * 30)
        
        try:
            if test_func():
                passed += 1
            else:
                print(f"❌ {test_name} échoué")
        except Exception as e:
            print(f"❌ {test_name} échoué avec erreur: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Résultats: {passed}/{total} tests réussis")
    
    if passed == total:
        print("🎉 Tous les tests sont passés ! L'application est prête.")
        print("\n🚀 Pour démarrer l'application :")
        print("   Windows: start.bat")
        print("   Linux/Mac: ./start.sh")
        print("\n🔗 Accès: http://localhost:8000")
        print("👤 Admin: admin / admin123")
        return True
    else:
        print(f"⚠️  {total - passed} test(s) échoué(s). Vérifiez les erreurs ci-dessus.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)

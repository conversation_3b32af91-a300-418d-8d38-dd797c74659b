#!/bin/bash

# Script de démarrage pour l'application CRM FastAPI
# Usage: ./start.sh [dev|prod]

set -e

# Couleurs pour les messages
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Fonction pour afficher les messages
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Configuration par défaut
MODE=${1:-dev}
HOST=${HOST:-0.0.0.0}
PORT=${PORT:-8000}
WORKERS=${WORKERS:-1}

log_info "Démarrage de l'application CRM en mode: $MODE"

# Vérifier si Python est installé
if ! command -v python3 &> /dev/null; then
    log_error "Python 3 n'est pas installé"
    exit 1
fi

# Vérifier si pip est installé
if ! command -v pip3 &> /dev/null; then
    log_error "pip3 n'est pas installé"
    exit 1
fi

# Créer un environnement virtuel s'il n'existe pas
if [ ! -d "venv" ]; then
    log_info "Création de l'environnement virtuel..."
    python3 -m venv venv
    log_success "Environnement virtuel créé"
fi

# Activer l'environnement virtuel
log_info "Activation de l'environnement virtuel..."
source venv/bin/activate

# Installer les dépendances
log_info "Installation des dépendances..."
pip install -r requirements.txt
log_success "Dépendances installées"

# Créer le fichier .env s'il n'existe pas
if [ ! -f ".env" ]; then
    log_info "Création du fichier .env..."
    cat > .env << EOF
# Configuration de l'application CRM
SECRET_KEY=your-secret-key-change-this-in-production-$(openssl rand -hex 32)
DATABASE_URL=sqlite:///./crm.db

# Configuration optionnelle
DEBUG=true
LOG_LEVEL=INFO

# Configuration SMTP (optionnel - peut être configuré via l'interface)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_FROM_EMAIL=<EMAIL>
# SMTP_FROM_NAME=CRM System
EOF
    log_success "Fichier .env créé avec une clé secrète générée"
fi

# Créer les dossiers nécessaires s'ils n'existent pas
mkdir -p static/uploads
mkdir -p logs

# Initialiser la base de données
log_info "Initialisation de la base de données..."
python3 -c "
from database import create_tables
from crud import create_user, get_user_by_username
from schemas import UserCreate
from database import get_db

# Créer les tables
create_tables()

# Créer un admin par défaut s'il n'existe pas
db = next(get_db())
if not get_user_by_username(db, 'admin'):
    admin_user = UserCreate(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        role='admin'
    )
    create_user(db, admin_user)
    print('Utilisateur admin créé: admin / admin123')
else:
    print('Utilisateur admin existe déjà')
"
log_success "Base de données initialisée"

# Afficher les informations de connexion
log_info "=== INFORMATIONS DE CONNEXION ==="
log_success "URL: http://$HOST:$PORT"
log_success "Utilisateur admin: admin"
log_success "Mot de passe admin: admin123"
log_info "=================================="

# Démarrer l'application selon le mode
if [ "$MODE" = "prod" ]; then
    log_info "Démarrage en mode production avec Gunicorn..."
    
    # Installer Gunicorn si pas présent
    pip install gunicorn
    
    # Démarrer avec Gunicorn
    exec gunicorn main:app \
        --host $HOST \
        --port $PORT \
        --workers $WORKERS \
        --worker-class uvicorn.workers.UvicornWorker \
        --access-logfile logs/access.log \
        --error-logfile logs/error.log \
        --log-level info
else
    log_info "Démarrage en mode développement avec Uvicorn..."
    
    # Démarrer avec Uvicorn en mode développement
    exec uvicorn main:app \
        --host $HOST \
        --port $PORT \
        --reload \
        --log-level info
fi

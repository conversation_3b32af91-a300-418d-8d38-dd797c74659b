#!/usr/bin/env python3
"""
BINANCE CRM - Outil de Benchmark des Performances
Mesure les performances du système avant et après optimisation
"""

import time
import json
import sqlite3
import requests
import threading
import statistics
import psutil
import os
from datetime import datetime
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed

class PerformanceBenchmark:
    def __init__(self):
        self.db_path = 'binance_crm.db'
        self.base_url = 'http://localhost'
        self.results = {
            'timestamp': datetime.now().isoformat(),
            'database': {},
            'api': {},
            'frontend': {},
            'system': {}
        }
    
    def measure_system_resources(self):
        """Mesurer les ressources système"""
        process = psutil.Process()
        
        return {
            'cpu_percent': psutil.cpu_percent(interval=1),
            'memory_percent': psutil.virtual_memory().percent,
            'memory_used_mb': round(process.memory_info().rss / 1024 / 1024, 2),
            'disk_usage_percent': psutil.disk_usage('.').percent,
            'open_files': len(process.open_files())
        }
    
    def benchmark_database_queries(self):
        """Benchmark des requêtes de base de données"""
        print("🔍 Benchmark des requêtes de base de données...")
        
        conn = sqlite3.connect(self.db_path)
        cursor = conn.cursor()
        
        queries = [
            ("SELECT COUNT(*) FROM clients", "count_clients"),
            ("SELECT * FROM clients LIMIT 100", "select_clients_100"),
            ("SELECT c.*, u.first_name, u.last_name FROM clients c LEFT JOIN users u ON c.assigned_to = u.id LIMIT 50", "join_clients_users"),
            ("SELECT * FROM clients WHERE email LIKE '%@gmail.com%'", "search_email"),
            ("SELECT * FROM clients WHERE status = 'prospect' ORDER BY created_date DESC", "filter_prospects"),
            ("SELECT COUNT(*) FROM clients GROUP BY status", "group_by_status")
        ]
        
        db_results = {}
        
        for query, name in queries:
            times = []
            for _ in range(10):  # 10 exécutions pour moyenne
                start_time = time.time()
                cursor.execute(query)
                results = cursor.fetchall()
                end_time = time.time()
                times.append((end_time - start_time) * 1000)  # en ms
            
            db_results[name] = {
                'avg_time_ms': round(statistics.mean(times), 3),
                'min_time_ms': round(min(times), 3),
                'max_time_ms': round(max(times), 3),
                'std_dev_ms': round(statistics.stdev(times) if len(times) > 1 else 0, 3),
                'result_count': len(results)
            }
        
        conn.close()
        self.results['database'] = db_results
        return db_results
    
    def benchmark_api_endpoints(self):
        """Benchmark des endpoints API"""
        print("🔍 Benchmark des endpoints API...")
        
        endpoints = [
            (f"{self.base_url}:8001/api/clients", "GET", "get_clients"),
            (f"{self.base_url}:8001/api/users", "GET", "get_users"),
            (f"{self.base_url}:8001/api/dashboard-stats", "GET", "dashboard_stats"),
            (f"{self.base_url}:8001/api/health", "GET", "health_check")
        ]
        
        api_results = {}
        
        for url, method, name in endpoints:
            times = []
            success_count = 0
            
            for _ in range(5):  # 5 requêtes par endpoint
                try:
                    start_time = time.time()
                    if method == "GET":
                        response = requests.get(url, timeout=10)
                    else:
                        response = requests.post(url, timeout=10)
                    end_time = time.time()
                    
                    if response.status_code == 200:
                        success_count += 1
                        times.append((end_time - start_time) * 1000)
                except Exception as e:
                    print(f"Erreur pour {name}: {e}")
            
            if times:
                api_results[name] = {
                    'avg_time_ms': round(statistics.mean(times), 3),
                    'min_time_ms': round(min(times), 3),
                    'max_time_ms': round(max(times), 3),
                    'success_rate': round((success_count / 5) * 100, 1),
                    'requests_tested': 5
                }
            else:
                api_results[name] = {
                    'error': 'Aucune requête réussie',
                    'success_rate': 0,
                    'requests_tested': 5
                }
        
        self.results['api'] = api_results
        return api_results
    
    def benchmark_concurrent_load(self):
        """Test de charge avec requêtes concurrentes"""
        print("🔍 Test de charge concurrente...")
        
        def make_request():
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}:8001/api/clients", timeout=10)
                end_time = time.time()
                return (end_time - start_time) * 1000, response.status_code == 200
            except:
                return None, False
        
        # Test avec 10 requêtes concurrentes
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(make_request) for _ in range(20)]
            results = []
            success_count = 0
            
            for future in as_completed(futures):
                time_ms, success = future.result()
                if time_ms is not None:
                    results.append(time_ms)
                if success:
                    success_count += 1
        
        if results:
            concurrent_results = {
                'concurrent_requests': 20,
                'avg_time_ms': round(statistics.mean(results), 3),
                'max_time_ms': round(max(results), 3),
                'min_time_ms': round(min(results), 3),
                'success_rate': round((success_count / 20) * 100, 1),
                'throughput_req_per_sec': round(20 / (max(results) / 1000), 2)
            }
        else:
            concurrent_results = {'error': 'Aucune requête concurrente réussie'}
        
        self.results['concurrent'] = concurrent_results
        return concurrent_results
    
    def measure_file_sizes(self):
        """Mesurer les tailles des fichiers"""
        files_to_check = [
            'clients.html',
            'index.html',
            'emails.html',
            'reports.html',
            'admin_config.html',
            'binance_crm.db'
        ]
        
        file_sizes = {}
        for filename in files_to_check:
            if os.path.exists(filename):
                size_bytes = os.path.getsize(filename)
                file_sizes[filename] = {
                    'size_bytes': size_bytes,
                    'size_kb': round(size_bytes / 1024, 2),
                    'size_mb': round(size_bytes / (1024 * 1024), 2)
                }
        
        self.results['file_sizes'] = file_sizes
        return file_sizes
    
    def run_full_benchmark(self):
        """Exécuter le benchmark complet"""
        print("🚀 Démarrage du benchmark complet des performances...")
        
        # Mesures système initiales
        self.results['system']['initial'] = self.measure_system_resources()
        
        # Benchmark base de données
        self.benchmark_database_queries()
        
        # Benchmark API (si serveurs disponibles)
        try:
            self.benchmark_api_endpoints()
            self.benchmark_concurrent_load()
        except Exception as e:
            print(f"⚠️ Serveurs non disponibles pour test API: {e}")
            self.results['api'] = {'error': 'Serveurs non disponibles'}
        
        # Mesures des fichiers
        self.measure_file_sizes()
        
        # Mesures système finales
        self.results['system']['final'] = self.measure_system_resources()
        
        return self.results
    
    def save_results(self, filename=None):
        """Sauvegarder les résultats"""
        if filename is None:
            filename = f"benchmark_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        print(f"📊 Résultats sauvegardés dans {filename}")
        return filename
    
    def print_summary(self):
        """Afficher un résumé des résultats"""
        print("\n" + "="*60)
        print("📊 RÉSUMÉ DES PERFORMANCES")
        print("="*60)
        
        # Base de données
        if 'database' in self.results and self.results['database']:
            print("\n🗄️ BASE DE DONNÉES:")
            for query, stats in self.results['database'].items():
                if 'avg_time_ms' in stats:
                    print(f"  {query}: {stats['avg_time_ms']}ms (avg)")
        
        # API
        if 'api' in self.results and self.results['api']:
            print("\n🌐 API ENDPOINTS:")
            for endpoint, stats in self.results['api'].items():
                if 'avg_time_ms' in stats:
                    print(f"  {endpoint}: {stats['avg_time_ms']}ms ({stats['success_rate']}% succès)")
        
        # Système
        if 'system' in self.results:
            print("\n💻 SYSTÈME:")
            if 'initial' in self.results['system']:
                sys_stats = self.results['system']['initial']
                print(f"  CPU: {sys_stats['cpu_percent']}%")
                print(f"  Mémoire: {sys_stats['memory_used_mb']}MB ({sys_stats['memory_percent']}%)")

    def compare_benchmarks(self, before_file, after_file):
        """Comparer deux benchmarks et générer un rapport d'amélioration"""
        try:
            with open(before_file, 'r', encoding='utf-8') as f:
                before_data = json.load(f)
            with open(after_file, 'r', encoding='utf-8') as f:
                after_data = json.load(f)
        except FileNotFoundError as e:
            print(f"Erreur: Fichier non trouvé - {e}")
            return

        print("\n" + "="*80)
        print("📊 RAPPORT DE COMPARAISON DES PERFORMANCES")
        print("="*80)

        # Comparaison base de données
        if 'database' in before_data and 'database' in after_data:
            print("\n🗄️ AMÉLIORATION BASE DE DONNÉES:")
            for query in before_data['database']:
                if query in after_data['database']:
                    before_time = before_data['database'][query]['avg_time_ms']
                    after_time = after_data['database'][query]['avg_time_ms']
                    improvement = ((before_time - after_time) / before_time) * 100
                    print(f"  {query}:")
                    print(f"    Avant: {before_time}ms")
                    print(f"    Après: {after_time}ms")
                    print(f"    Amélioration: {improvement:+.1f}%")

        # Comparaison API
        if 'api' in before_data and 'api' in after_data:
            print("\n🌐 AMÉLIORATION API:")
            for endpoint in before_data['api']:
                if endpoint in after_data['api'] and 'avg_time_ms' in before_data['api'][endpoint]:
                    before_time = before_data['api'][endpoint]['avg_time_ms']
                    after_time = after_data['api'][endpoint]['avg_time_ms']
                    improvement = ((before_time - after_time) / before_time) * 100
                    print(f"  {endpoint}:")
                    print(f"    Avant: {before_time}ms")
                    print(f"    Après: {after_time}ms")
                    print(f"    Amélioration: {improvement:+.1f}%")

        # Comparaison système
        if 'system' in before_data and 'system' in after_data:
            print("\n💻 AMÉLIORATION SYSTÈME:")
            if 'initial' in before_data['system'] and 'initial' in after_data['system']:
                before_mem = before_data['system']['initial']['memory_used_mb']
                after_mem = after_data['system']['initial']['memory_used_mb']
                mem_improvement = ((before_mem - after_mem) / before_mem) * 100
                print(f"  Utilisation mémoire:")
                print(f"    Avant: {before_mem}MB")
                print(f"    Après: {after_mem}MB")
                print(f"    Amélioration: {mem_improvement:+.1f}%")

if __name__ == "__main__":
    import sys

    benchmark = PerformanceBenchmark()

    if len(sys.argv) > 1 and sys.argv[1] == "compare":
        if len(sys.argv) >= 4:
            benchmark.compare_benchmarks(sys.argv[2], sys.argv[3])
        else:
            print("Usage: python performance_benchmark.py compare <before.json> <after.json>")
    else:
        print("🚀 Exécution du benchmark des performances optimisées...")
        results = benchmark.run_full_benchmark()
        benchmark.print_summary()
        filename = benchmark.save_results("benchmark_optimized.json")
        print(f"\n💡 Pour comparer avec un benchmark précédent:")
        print(f"python performance_benchmark.py compare benchmark_initial.json {filename}")

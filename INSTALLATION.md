# 🚀 Guide d'Installation - CRM System

Ce guide vous accompagne pas à pas pour installer et configurer l'application CRM sur votre machine locale.

## 📋 Prérequis

### Système d'exploitation
- ✅ Windows 10/11
- ✅ macOS 10.14+
- ✅ Linux (Ubuntu 18.04+, CentOS 7+, etc.)

### Logiciels requis
- **Python 3.8+** (recommandé: Python 3.11)
- **pip** (gestionnaire de paquets Python)
- **Git** (optionnel, pour cloner le projet)

### Vérification des prérequis

```bash
# Vérifier Python
python --version
# ou
python3 --version

# Vérifier pip
pip --version
# ou
pip3 --version
```

## 📦 Installation

### Méthode 1: Installation Automatique (Recommandée)

#### Sur Windows
1. Téléchargez ou clonez le projet
2. Ouvrez une invite de commande dans le dossier du projet
3. Exécutez le script d'installation :
```cmd
start.bat dev
```

#### Sur Linux/macOS
1. Téléchargez ou clonez le projet
2. Ouvrez un terminal dans le dossier du projet
3. Rendez le script exécutable et lancez-le :
```bash
chmod +x start.sh
./start.sh dev
```

### Méthode 2: Installation Manuelle

#### 1. Créer l'environnement virtuel
```bash
# Créer l'environnement virtuel
python -m venv venv

# Activer l'environnement virtuel
# Windows:
venv\Scripts\activate
# Linux/macOS:
source venv/bin/activate
```

#### 2. Installer les dépendances
```bash
pip install -r requirements.txt
```

#### 3. Configurer l'environnement
```bash
# Copier le fichier de configuration
cp .env.example .env

# Éditer le fichier .env selon vos besoins
# Changez au minimum la SECRET_KEY
```

#### 4. Initialiser la base de données
```bash
python init_data.py
```

#### 5. Démarrer l'application
```bash
uvicorn main:app --host 0.0.0.0 --port 8000 --reload
```

## 🔧 Configuration

### Configuration de base (.env)

Créez un fichier `.env` à la racine du projet :

```env
# Clé secrète (OBLIGATOIRE - générez une clé unique)
SECRET_KEY=your-unique-secret-key-here

# Base de données
DATABASE_URL=sqlite:///./crm.db

# Configuration serveur
HOST=0.0.0.0
PORT=8000
DEBUG=true
```

### Configuration SMTP (Optionnelle)

Vous pouvez configurer SMTP directement dans le fichier `.env` ou via l'interface web :

```env
# Gmail
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_USE_TLS=true
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=CRM System
```

## 🧪 Test de l'Installation

### Test automatique
```bash
python test_app.py
```

### Test manuel
1. Ouvrez votre navigateur
2. Allez à `http://localhost:8000`
3. Connectez-vous avec :
   - **Utilisateur** : `admin`
   - **Mot de passe** : `admin123`

## 📊 Données de Démonstration

Pour charger des données de test :

```bash
python init_data.py
```

Cela créera :
- 3 vendeurs de démonstration
- 10 clients d'exemple
- 3 templates d'email
- 5 rendez-vous planifiés

**Comptes de démonstration :**
- Admin : `admin` / `admin123`
- Vendeur : `marie.martin` / `vendeur123`
- Vendeur : `pierre.durand` / `vendeur123`
- Vendeur : `sophie.bernard` / `vendeur123`

## 🔒 Sécurité

### Première connexion
1. Connectez-vous avec le compte admin par défaut
2. **Changez immédiatement le mot de passe**
3. Créez vos propres comptes utilisateurs
4. Désactivez ou supprimez les comptes de démonstration

### Configuration de production
Pour un déploiement en production :

```env
DEBUG=false
SECRET_KEY=your-very-secure-secret-key
DATABASE_URL=postgresql://user:password@localhost/crm_prod
```

## 🚀 Démarrage

### Mode Développement
```bash
# Windows
start.bat dev

# Linux/macOS
./start.sh dev
```

### Mode Production
```bash
# Windows
start.bat prod

# Linux/macOS
./start.sh prod
```

## 📱 Accès à l'Application

Une fois démarrée, l'application est accessible à :
- **Interface Web** : http://localhost:8000
- **Documentation API** : http://localhost:8000/docs
- **API Alternative** : http://localhost:8000/redoc

## 🔧 Dépannage

### Problème : Python non trouvé
```bash
# Essayez ces variantes
python --version
python3 --version
py --version
```

### Problème : pip non trouvé
```bash
# Essayez ces variantes
pip --version
pip3 --version
python -m pip --version
```

### Problème : Port déjà utilisé
```bash
# Changer le port dans .env
PORT=8001

# Ou spécifier un autre port
uvicorn main:app --port 8001
```

### Problème : Erreur de base de données
```bash
# Supprimer la base de données et recréer
rm crm.db
python init_data.py
```

### Problème : Modules manquants
```bash
# Réinstaller les dépendances
pip install --upgrade -r requirements.txt
```

## 📁 Structure des Fichiers

Après installation, votre projet devrait ressembler à :

```
crm/
├── 📄 main.py              # Application principale
├── 📄 models.py            # Modèles de données
├── 📄 crud.py              # Opérations base de données
├── 📄 auth.py              # Authentification
├── 📄 schemas.py           # Validation des données
├── 📄 database.py          # Configuration DB
├── 📄 requirements.txt     # Dépendances
├── 📄 start.sh / start.bat # Scripts de démarrage
├── 📄 .env                 # Configuration
├── 📄 crm.db              # Base de données SQLite
├── 📁 templates/          # Templates HTML
├── 📁 static/             # Fichiers CSS/JS
├── 📁 utils/              # Utilitaires
├── 📁 venv/               # Environnement virtuel
└── 📁 logs/               # Fichiers de log
```

## 🆘 Support

### Logs de l'application
Les logs sont disponibles dans :
- Console (mode développement)
- Fichier `logs/crm.log` (mode production)

### Vérification de l'état
```bash
# Test complet
python test_app.py

# Vérification des routes
curl http://localhost:8000/

# Test de l'API
curl http://localhost:8000/api/stats
```

### Problèmes courants

| Problème | Solution |
|----------|----------|
| Page blanche | Vérifiez les logs, redémarrez l'application |
| Erreur 500 | Vérifiez la configuration .env |
| Connexion refusée | Vérifiez que le port n'est pas bloqué |
| Templates non trouvés | Vérifiez que le dossier templates existe |

## 🔄 Mise à jour

Pour mettre à jour l'application :

1. Sauvegardez votre base de données
2. Téléchargez la nouvelle version
3. Réinstallez les dépendances :
```bash
pip install --upgrade -r requirements.txt
```
4. Redémarrez l'application

## 📞 Contact

En cas de problème persistant :
1. Vérifiez la documentation
2. Consultez les logs d'erreur
3. Testez avec `python test_app.py`
4. Ouvrez une issue sur le projet

---

**🎉 Félicitations ! Votre CRM est maintenant installé et prêt à l'emploi !**

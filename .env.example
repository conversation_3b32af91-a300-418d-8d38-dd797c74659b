# Configuration de l'application CRM
# Copiez ce fichier vers .env et modifiez les valeurs selon vos besoins

# Clé secrète pour les sessions (OBLIGATOIRE - changez cette valeur !)
SECRET_KEY=your-secret-key-change-this-in-production

# Configuration de la base de données
DATABASE_URL=sqlite:///./crm.db
# Pour PostgreSQL : postgresql://username:password@localhost/crm_db
# Pour MySQL : mysql://username:password@localhost/crm_db

# Configuration de l'application
DEBUG=true
LOG_LEVEL=INFO
HOST=0.0.0.0
PORT=8000

# Configuration SMTP (optionnel - peut être configuré via l'interface web)
# Gmail
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-app-password
# SMTP_USE_TLS=true
# SMTP_FROM_EMAIL=<EMAIL>
# SMTP_FROM_NAME=CRM System

# Outlook/Hotmail
# SMTP_HOST=smtp-mail.outlook.com
# SMTP_PORT=587
# SMTP_USERNAME=<EMAIL>
# SMTP_PASSWORD=your-password
# SMTP_USE_TLS=true
# SMTP_FROM_EMAIL=<EMAIL>
# SMTP_FROM_NAME=CRM System

# SendGrid
# SMTP_HOST=smtp.sendgrid.net
# SMTP_PORT=587
# SMTP_USERNAME=apikey
# SMTP_PASSWORD=your-sendgrid-api-key
# SMTP_USE_TLS=true
# SMTP_FROM_EMAIL=<EMAIL>
# SMTP_FROM_NAME=CRM System

# Configuration des uploads
MAX_UPLOAD_SIZE=10485760  # 10MB en bytes
ALLOWED_EXTENSIONS=csv,xlsx,xls

# Configuration des logs
LOG_FILE=logs/crm.log
LOG_MAX_SIZE=10485760  # 10MB
LOG_BACKUP_COUNT=5

# Configuration de sécurité
SESSION_TIMEOUT=3600  # 1 heure en secondes
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION=900  # 15 minutes en secondes

# Configuration des emails
EMAIL_RATE_LIMIT=100  # Nombre d'emails par heure
EMAIL_BATCH_SIZE=50   # Nombre d'emails par lot

# Configuration de l'interface
ITEMS_PER_PAGE=50
TIMEZONE=Europe/Paris
DATE_FORMAT=%d/%m/%Y
DATETIME_FORMAT=%d/%m/%Y %H:%M

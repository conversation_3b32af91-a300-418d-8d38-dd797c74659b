{% extends "base.html" %}

{% block title %}Gestion des Vendeurs - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-person-badge"></i> Gestion des Vendeurs</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVendeurModal">
            <i class="bi bi-plus"></i> Nouveau Vendeur
        </button>
    </div>
</div>

<!-- Statistiques des vendeurs -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ vendeurs|length }}</h5>
                <p class="card-text">Vendeurs Total</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ vendeurs|selectattr("is_active", "equalto", true)|list|length }}</h5>
                <p class="card-text">Actifs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ vendeurs|selectattr("is_active", "equalto", false)|list|length }}</h5>
                <p class="card-text">Inactifs</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-info">
                    {% set total_clients = 0 %}
                    {% for vendeur in vendeurs %}
                        {% set total_clients = total_clients + vendeur.clients|length %}
                    {% endfor %}
                    {{ total_clients }}
                </h5>
                <p class="card-text">Clients Attribués</p>
            </div>
        </div>
    </div>
</div>

<!-- Liste des vendeurs -->
<div class="card">
    <div class="card-header">
        <h5><i class="bi bi-list"></i> Liste des Vendeurs</h5>
    </div>
    <div class="card-body">
        {% if vendeurs %}
        <div class="table-responsive">
            <table class="table table-striped table-hover">
                <thead>
                    <tr>
                        <th>Vendeur</th>
                        <th>Email</th>
                        <th>Statut</th>
                        <th>Clients Attribués</th>
                        <th>Emails Envoyés</th>
                        <th>RDV Planifiés</th>
                        <th>Inscription</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for vendeur in vendeurs %}
                    <tr>
                        <td>
                            <div class="d-flex align-items-center">
                                <div class="avatar-circle me-2">
                                    {{ vendeur.username[0].upper() }}
                                </div>
                                <div>
                                    <strong>{{ vendeur.username }}</strong>
                                    <br><small class="text-muted">ID: {{ vendeur.id }}</small>
                                </div>
                            </div>
                        </td>
                        <td>{{ vendeur.email }}</td>
                        <td>
                            {% if vendeur.is_active %}
                                <span class="badge bg-success">Actif</span>
                            {% else %}
                                <span class="badge bg-danger">Inactif</span>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-primary">{{ vendeur.clients|length }}</span>
                            {% if vendeur.clients|length > 0 %}
                            <br><small class="text-muted">
                                <a href="/admin/clients?vendeur_id={{ vendeur.id }}" class="text-decoration-none">
                                    Voir les clients
                                </a>
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            {% set emails_envoyes = vendeur.clients|selectattr("email_envoye", "equalto", true)|list|length %}
                            <span class="badge bg-info">{{ emails_envoyes }}</span>
                        </td>
                        <td>
                            <span class="badge bg-warning">{{ vendeur.appointments|length }}</span>
                        </td>
                        <td>{{ vendeur.created_at.strftime('%d/%m/%Y') }}</td>
                        <td class="table-actions">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="editVendeur({{ vendeur.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#editVendeurModal"
                                        title="Modifier">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="viewStats({{ vendeur.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#statsModal"
                                        title="Statistiques">
                                    <i class="bi bi-bar-chart"></i>
                                </button>
                                <button type="button" class="btn btn-outline-warning" 
                                        onclick="toggleStatus({{ vendeur.id }}, {{ vendeur.is_active|lower }})"
                                        title="{% if vendeur.is_active %}Désactiver{% else %}Activer{% endif %}">
                                    {% if vendeur.is_active %}
                                        <i class="bi bi-pause"></i>
                                    {% else %}
                                        <i class="bi bi-play"></i>
                                    {% endif %}
                                </button>
                                <button type="button" class="btn btn-outline-success" 
                                        onclick="resetPassword({{ vendeur.id }})"
                                        title="Réinitialiser mot de passe">
                                    <i class="bi bi-key"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="bi bi-person-badge fs-1 text-muted"></i>
            <p class="text-muted">Aucun vendeur créé.</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addVendeurModal">
                <i class="bi bi-plus"></i> Créer le premier vendeur
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal Nouveau Vendeur -->
<div class="modal fade" id="addVendeurModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-plus"></i> Nouveau Vendeur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addVendeurForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="username" class="form-label">Nom d'utilisateur *</label>
                        <input type="text" class="form-control" id="username" name="username" required>
                        <div class="form-text">Utilisé pour la connexion</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">Mot de passe *</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="form-text">Minimum 6 caractères</div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="confirm_password" class="form-label">Confirmer le mot de passe *</label>
                        <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is_active" name="is_active" checked>
                            <label class="form-check-label" for="is_active">
                                Compte actif
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Modifier Vendeur -->
<div class="modal fade" id="editVendeurModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-pencil"></i> Modifier Vendeur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editVendeurForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_vendeur_id" name="vendeur_id">
                    
                    <div class="mb-3">
                        <label for="edit_username" class="form-label">Nom d'utilisateur *</label>
                        <input type="text" class="form-control" id="edit_username" name="username" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_email" class="form-label">Email *</label>
                        <input type="email" class="form-control" id="edit_email" name="email" required>
                    </div>
                    
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="edit_is_active" name="is_active">
                            <label class="form-check-label" for="edit_is_active">
                                Compte actif
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Statistiques -->
<div class="modal fade" id="statsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-bar-chart"></i> Statistiques Vendeur</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="stats_content">
                    <div class="text-center">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement...</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Fermer</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.avatar-circle {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Données des vendeurs pour JavaScript
const vendeursData = {{ vendeurs|tojson }};

function editVendeur(vendeurId) {
    const vendeur = vendeursData.find(v => v.id === vendeurId);
    if (vendeur) {
        document.getElementById('edit_vendeur_id').value = vendeur.id;
        document.getElementById('edit_username').value = vendeur.username;
        document.getElementById('edit_email').value = vendeur.email;
        document.getElementById('edit_is_active').checked = vendeur.is_active;
    }
}

function viewStats(vendeurId) {
    // Charger les statistiques du vendeur
    fetch(`/api/stats?vendeur_id=${vendeurId}`)
        .then(response => response.json())
        .then(data => {
            const content = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">${data.total_clients}</h5>
                                <p class="card-text">Clients Total</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">${data.emails_envoyes}</h5>
                                <p class="card-text">Emails Envoyés</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">${data.rdv_planifies}</h5>
                                <p class="card-text">RDV Planifiés</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body text-center">
                                <h5 class="card-title">${data.rdv_realises}</h5>
                                <p class="card-text">RDV Réalisés</p>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            document.getElementById('stats_content').innerHTML = content;
        })
        .catch(error => {
            document.getElementById('stats_content').innerHTML = '<p class="text-danger">Erreur lors du chargement des statistiques</p>';
        });
}

function toggleStatus(vendeurId, currentStatus) {
    const newStatus = !currentStatus;
    const action = newStatus ? 'activer' : 'désactiver';
    
    if (confirm(`Êtes-vous sûr de vouloir ${action} ce vendeur ?`)) {
        fetch(`/api/users/${vendeurId}`, {
            method: 'PUT',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                is_active: newStatus
            })
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la modification du statut');
            }
        });
    }
}

function resetPassword(vendeurId) {
    const newPassword = prompt('Nouveau mot de passe (minimum 6 caractères):');
    if (newPassword && newPassword.length >= 6) {
        fetch(`/api/users/${vendeurId}/reset-password`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                new_password: newPassword
            })
        }).then(response => {
            if (response.ok) {
                alert('Mot de passe réinitialisé avec succès');
            } else {
                alert('Erreur lors de la réinitialisation');
            }
        });
    } else if (newPassword !== null) {
        alert('Le mot de passe doit contenir au moins 6 caractères');
    }
}

// Gestion du formulaire d'ajout
document.getElementById('addVendeurForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    
    // Vérifier la confirmation du mot de passe
    if (data.password !== data.confirm_password) {
        alert('Les mots de passe ne correspondent pas');
        return;
    }
    
    // Supprimer la confirmation du mot de passe
    delete data.confirm_password;
    
    // Convertir is_active en boolean
    data.is_active = document.getElementById('is_active').checked;
    data.role = 'vendeur';
    
    fetch('/api/users', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            location.reload();
        } else {
            response.json().then(err => {
                alert('Erreur: ' + (err.detail || 'Erreur lors de la création'));
            });
        }
    });
});

// Gestion du formulaire de modification
document.getElementById('editVendeurForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    const vendeurId = data.vendeur_id;
    delete data.vendeur_id;
    
    // Convertir is_active en boolean
    data.is_active = document.getElementById('edit_is_active').checked;
    
    fetch(`/api/users/${vendeurId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('Erreur lors de la modification');
        }
    });
});
</script>
{% endblock %}

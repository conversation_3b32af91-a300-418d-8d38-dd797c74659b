#!/usr/bin/env python3
"""
BINANCE CRM - Test des Fonctionnalités Prioritaires
Script de test complet pour valider les 3 fonctionnalités prioritaires implémentées
"""

import requests
import json
import time
import csv
import io
from datetime import datetime
from pathlib import Path

class PriorityFeaturesTest:
    def __init__(self):
        self.base_url = 'http://localhost:8000'
        self.email_server_url = 'http://localhost:8001'
        self.test_results = {
            'csv_advanced': {'passed': 0, 'failed': 0, 'tests': []},
            'health_monitoring': {'passed': 0, 'failed': 0, 'tests': []},
            'email_history': {'passed': 0, 'failed': 0, 'tests': []}
        }
        
    def log_test(self, category, test_name, passed, details=""):
        """Enregistrer le résultat d'un test"""
        result = {
            'name': test_name,
            'passed': passed,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        
        self.test_results[category]['tests'].append(result)
        if passed:
            self.test_results[category]['passed'] += 1
            print(f"  ✅ {test_name}")
        else:
            self.test_results[category]['failed'] += 1
            print(f"  ❌ {test_name}: {details}")
    
    def test_csv_advanced_features(self):
        """Tester les fonctionnalités CSV avancées"""
        print("📊 Test des fonctionnalités CSV avancées...")
        
        # Test 1: Téléchargement du template CSV
        try:
            response = requests.get(f"{self.base_url}/api/import-template", timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and 'csv_content' in result:
                    self.log_test('csv_advanced', 'Téléchargement template CSV', True)
                    
                    # Vérifier le contenu du template
                    csv_content = result['csv_content']
                    if 'nom,prenom,email' in csv_content.lower():
                        self.log_test('csv_advanced', 'Contenu template CSV valide', True)
                    else:
                        self.log_test('csv_advanced', 'Contenu template CSV valide', False, "Headers manquants")
                else:
                    self.log_test('csv_advanced', 'Téléchargement template CSV', False, "Réponse invalide")
            else:
                self.log_test('csv_advanced', 'Téléchargement template CSV', False, f"Code {response.status_code}")
        except Exception as e:
            self.log_test('csv_advanced', 'Téléchargement template CSV', False, str(e))
        
        # Test 2: Validation CSV
        test_csv_data = [
            {'nom': 'Test', 'prenom': 'User', 'email': '<EMAIL>', 'telephone': '0123456789', 'statut': 'prospect'},
            {'nom': 'Invalid', 'prenom': 'User', 'email': 'invalid-email', 'telephone': '123', 'statut': 'unknown'}
        ]
        
        try:
            response = requests.post(f"{self.base_url}/api/validate-csv", 
                                   json={'csv_data': test_csv_data}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    if 'valid_rows' in result and 'invalid_rows' in result:
                        self.log_test('csv_advanced', 'Validation CSV', True, 
                                    f"Valides: {result['valid_rows']}, Invalides: {result['invalid_rows']}")
                    else:
                        self.log_test('csv_advanced', 'Validation CSV', False, "Données de validation manquantes")
                else:
                    self.log_test('csv_advanced', 'Validation CSV', False, result.get('error', 'Erreur inconnue'))
            else:
                self.log_test('csv_advanced', 'Validation CSV', False, f"Code {response.status_code}")
        except Exception as e:
            self.log_test('csv_advanced', 'Validation CSV', False, str(e))
        
        # Test 3: Historique des imports
        try:
            response = requests.get(f"{self.base_url}/api/import-history", timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    history = result.get('data', [])
                    self.log_test('csv_advanced', 'Historique des imports', True, 
                                f"{len(history)} imports trouvés")
                else:
                    self.log_test('csv_advanced', 'Historique des imports', False, result.get('error', 'Erreur inconnue'))
            else:
                self.log_test('csv_advanced', 'Historique des imports', False, f"Code {response.status_code}")
        except Exception as e:
            self.log_test('csv_advanced', 'Historique des imports', False, str(e))
        
        # Test 4: Test d'import réel avec données validées
        try:
            valid_csv_data = [
                {'nom': 'TestImport', 'prenom': 'User', 'email': f'testimport.{int(time.time())}@example.com', 
                 'telephone': '0123456789', 'statut': 'prospect'}
            ]
            
            response = requests.post(f"{self.base_url}/api/import-clients",
                                   json={
                                       'csv_data': valid_csv_data,
                                       'filename': 'test_priority_features.csv',
                                       'user_id': 1,
                                       'skip_errors': False
                                   }, timeout=15)
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and result.get('successful_rows', 0) > 0:
                    self.log_test('csv_advanced', 'Import CSV avec données validées', True,
                                f"{result['successful_rows']} clients importés")
                else:
                    self.log_test('csv_advanced', 'Import CSV avec données validées', False, 
                                result.get('error', 'Aucun client importé'))
            else:
                self.log_test('csv_advanced', 'Import CSV avec données validées', False, f"Code {response.status_code}")
        except Exception as e:
            self.log_test('csv_advanced', 'Import CSV avec données validées', False, str(e))
    
    def test_health_monitoring(self):
        """Tester le monitoring de santé"""
        print("🏥 Test du monitoring de santé...")
        
        # Test 1: Endpoint health check
        try:
            response = requests.get(f"{self.base_url}/api/health", timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success') and 'data' in result:
                    health_data = result['data']
                    self.log_test('health_monitoring', 'Endpoint health check', True,
                                f"Status: {health_data.get('status', 'unknown')}")
                    
                    # Vérifier les composants de santé
                    checks = health_data.get('checks', {})
                    
                    # Test base de données
                    if 'database' in checks:
                        db_check = checks['database']
                        if db_check.get('status') == 'healthy':
                            self.log_test('health_monitoring', 'Santé base de données', True,
                                        f"Temps de réponse: {db_check.get('response_time_ms', 0)}ms")
                        else:
                            self.log_test('health_monitoring', 'Santé base de données', False,
                                        f"Status: {db_check.get('status', 'unknown')}")
                    else:
                        self.log_test('health_monitoring', 'Santé base de données', False, "Check manquant")
                    
                    # Test système
                    if 'system' in checks:
                        sys_check = checks['system']
                        if sys_check.get('status') in ['healthy', 'limited']:
                            memory_mb = sys_check.get('memory_usage_mb', 0)
                            self.log_test('health_monitoring', 'Métriques système', True,
                                        f"RAM: {memory_mb}MB")
                        else:
                            self.log_test('health_monitoring', 'Métriques système', False,
                                        f"Status: {sys_check.get('status', 'unknown')}")
                    else:
                        self.log_test('health_monitoring', 'Métriques système', False, "Check manquant")
                    
                    # Test intégrité des données
                    if 'data_integrity' in checks:
                        data_check = checks['data_integrity']
                        clients_count = data_check.get('clients_count', 0)
                        vendeurs_count = data_check.get('vendeurs_count', 0)
                        self.log_test('health_monitoring', 'Intégrité des données', True,
                                    f"Clients: {clients_count}, Vendeurs: {vendeurs_count}")
                    else:
                        self.log_test('health_monitoring', 'Intégrité des données', False, "Check manquant")
                    
                    # Test performance
                    if 'performance' in checks:
                        perf_check = checks['performance']
                        indexes_count = perf_check.get('indexes_count', 0)
                        pool_size = perf_check.get('pool_size', 0)
                        self.log_test('health_monitoring', 'Métriques de performance', True,
                                    f"Index: {indexes_count}, Pool: {pool_size}")
                    else:
                        self.log_test('health_monitoring', 'Métriques de performance', False, "Check manquant")
                        
                else:
                    self.log_test('health_monitoring', 'Endpoint health check', False, "Données manquantes")
            else:
                self.log_test('health_monitoring', 'Endpoint health check', False, f"Code {response.status_code}")
        except Exception as e:
            self.log_test('health_monitoring', 'Endpoint health check', False, str(e))
        
        # Test 2: Temps de réponse acceptable
        try:
            start_time = time.time()
            response = requests.get(f"{self.base_url}/api/health", timeout=5)
            response_time = time.time() - start_time
            
            if response_time < 2.0:  # Moins de 2 secondes
                self.log_test('health_monitoring', 'Temps de réponse acceptable', True,
                            f"{response_time:.3f}s")
            else:
                self.log_test('health_monitoring', 'Temps de réponse acceptable', False,
                            f"{response_time:.3f}s (trop lent)")
        except Exception as e:
            self.log_test('health_monitoring', 'Temps de réponse acceptable', False, str(e))
    
    def test_email_history_integration(self):
        """Tester l'intégration de l'historique email"""
        print("📧 Test de l'intégration historique email...")
        
        # Test 1: Connectivité serveur email
        try:
            response = requests.get(f"{self.email_server_url}/health", timeout=5)
            if response.status_code == 200:
                self.log_test('email_history', 'Connectivité serveur email', True, "Port 8001 accessible")
            else:
                self.log_test('email_history', 'Connectivité serveur email', False, 
                            f"Code {response.status_code}")
        except Exception as e:
            self.log_test('email_history', 'Connectivité serveur email', False, 
                        "Serveur email non accessible (port 8001)")
        
        # Test 2: Historique des emails
        try:
            response = requests.get(f"{self.email_server_url}/email-history", timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    history = result.get('history', [])
                    self.log_test('email_history', 'Récupération historique emails', True,
                                f"{len(history)} emails dans l'historique")
                else:
                    self.log_test('email_history', 'Récupération historique emails', False,
                                result.get('error', 'Erreur inconnue'))
            else:
                self.log_test('email_history', 'Récupération historique emails', False,
                            f"Code {response.status_code}")
        except Exception as e:
            self.log_test('email_history', 'Récupération historique emails', False, str(e))
        
        # Test 3: Test SMTP
        try:
            response = requests.post(f"{self.email_server_url}/test-smtp",
                                   json={}, timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.log_test('email_history', 'Test connexion SMTP', True, "Connexion réussie")
                else:
                    self.log_test('email_history', 'Test connexion SMTP', False,
                                result.get('error', 'Test échoué'))
            else:
                self.log_test('email_history', 'Test connexion SMTP', False, f"Code {response.status_code}")
        except Exception as e:
            self.log_test('email_history', 'Test connexion SMTP', False, str(e))
        
        # Test 4: Templates email
        try:
            response = requests.get(f"{self.email_server_url}/templates", timeout=10)
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    templates = result.get('templates', [])
                    self.log_test('email_history', 'Récupération templates email', True,
                                f"{len(templates)} templates disponibles")
                else:
                    self.log_test('email_history', 'Récupération templates email', False,
                                result.get('error', 'Erreur inconnue'))
            else:
                self.log_test('email_history', 'Récupération templates email', False,
                            f"Code {response.status_code}")
        except Exception as e:
            self.log_test('email_history', 'Récupération templates email', False, str(e))
    
    def test_ui_integration(self):
        """Tester l'intégration UI des nouvelles fonctionnalités"""
        print("🌐 Test de l'intégration UI...")
        
        # Test des pages principales
        pages_to_test = [
            ('clients.html', 'Page clients avec CSV avancé'),
            ('admin_config.html', 'Page admin avec monitoring'),
            ('emails.html', 'Page emails avec historique')
        ]
        
        for page, description in pages_to_test:
            try:
                response = requests.get(f"{self.base_url}/{page}", timeout=10)
                if response.status_code == 200:
                    content = response.text
                    
                    # Vérifier la présence des nouvelles fonctionnalités
                    if page == 'clients.html':
                        has_template_btn = 'downloadCSVTemplate' in content
                        has_validate_btn = 'validateCSVBeforeImport' in content
                        has_history_btn = 'showImportHistory' in content
                        
                        if has_template_btn and has_validate_btn and has_history_btn:
                            self.log_test('ui_integration', f'{description} - Boutons CSV', True)
                        else:
                            missing = []
                            if not has_template_btn: missing.append('Template')
                            if not has_validate_btn: missing.append('Validation')
                            if not has_history_btn: missing.append('Historique')
                            self.log_test('ui_integration', f'{description} - Boutons CSV', False,
                                        f"Boutons manquants: {', '.join(missing)}")
                    
                    elif page == 'admin_config.html':
                        has_health_check = 'checkSystemHealth' in content
                        has_auto_refresh = 'toggleAutoRefresh' in content
                        
                        if has_health_check and has_auto_refresh:
                            self.log_test('ui_integration', f'{description} - Monitoring', True)
                        else:
                            self.log_test('ui_integration', f'{description} - Monitoring', False,
                                        "Fonctions monitoring manquantes")
                    
                    elif page == 'emails.html':
                        has_email_history = 'showEmailHistory' in content
                        has_smtp_test = 'testSMTPConnection' in content
                        
                        if has_email_history and has_smtp_test:
                            self.log_test('ui_integration', f'{description} - Email avancé', True)
                        else:
                            self.log_test('ui_integration', f'{description} - Email avancé', False,
                                        "Fonctions email manquantes")
                    
                    self.log_test('ui_integration', f'{description} - Accessibilité', True)
                else:
                    self.log_test('ui_integration', f'{description} - Accessibilité', False,
                                f"Code {response.status_code}")
            except Exception as e:
                self.log_test('ui_integration', f'{description} - Accessibilité', False, str(e))
    
    def generate_report(self):
        """Générer le rapport de test"""
        print("\n" + "="*80)
        print("📊 RAPPORT DE TEST - FONCTIONNALITÉS PRIORITAIRES")
        print("="*80)
        
        total_passed = 0
        total_failed = 0
        
        for category, results in self.test_results.items():
            passed = results['passed']
            failed = results['failed']
            total = passed + failed
            
            total_passed += passed
            total_failed += failed
            
            success_rate = (passed / total * 100) if total > 0 else 0
            
            category_names = {
                'csv_advanced': '📊 CSV Avancé',
                'health_monitoring': '🏥 Monitoring Santé',
                'email_history': '📧 Historique Email',
                'ui_integration': '🌐 Intégration UI'
            }
            
            print(f"\n{category_names.get(category, category.upper())}:")
            print(f"  ✅ Réussis: {passed}")
            print(f"  ❌ Échoués: {failed}")
            print(f"  📊 Taux de réussite: {success_rate:.1f}%")
            
            if failed > 0:
                print("  Échecs détaillés:")
                for test in results['tests']:
                    if not test['passed']:
                        print(f"    - {test['name']}: {test['details']}")
        
        overall_success_rate = (total_passed / (total_passed + total_failed) * 100) if (total_passed + total_failed) > 0 else 0
        
        print(f"\n🎯 RÉSULTAT GLOBAL:")
        print(f"  Total tests: {total_passed + total_failed}")
        print(f"  Réussis: {total_passed}")
        print(f"  Échoués: {total_failed}")
        print(f"  Taux de réussite global: {overall_success_rate:.1f}%")
        
        if overall_success_rate >= 90:
            print("  🎉 EXCELLENT - Toutes les fonctionnalités sont opérationnelles")
        elif overall_success_rate >= 75:
            print("  ✅ BON - La plupart des fonctionnalités fonctionnent")
        elif overall_success_rate >= 50:
            print("  ⚠️  MOYEN - Plusieurs problèmes à corriger")
        else:
            print("  ❌ CRITIQUE - Nombreux problèmes détectés")
        
        # Sauvegarder le rapport
        report_file = Path(__file__).parent / f"test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.test_results, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Rapport détaillé sauvegardé: {report_file}")
        
        return overall_success_rate >= 75
    
    def run_all_tests(self):
        """Exécuter tous les tests"""
        print("🧪 DÉMARRAGE DES TESTS DES FONCTIONNALITÉS PRIORITAIRES")
        print("="*60)
        
        # Ajouter la catégorie UI integration
        self.test_results['ui_integration'] = {'passed': 0, 'failed': 0, 'tests': []}
        
        # Exécuter les tests
        self.test_csv_advanced_features()
        print()
        self.test_health_monitoring()
        print()
        self.test_email_history_integration()
        print()
        self.test_ui_integration()
        
        # Générer le rapport
        success = self.generate_report()
        
        return success

if __name__ == "__main__":
    tester = PriorityFeaturesTest()
    success = tester.run_all_tests()
    
    if success:
        print("\n🎉 TESTS RÉUSSIS - Les fonctionnalités prioritaires sont opérationnelles!")
        exit(0)
    else:
        print("\n⚠️  TESTS PARTIELS - Certaines fonctionnalités nécessitent des corrections")
        exit(1)

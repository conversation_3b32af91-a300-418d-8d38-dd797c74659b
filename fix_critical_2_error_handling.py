#!/usr/bin/env python3
"""
BINANCE CRM - Fix Critical #2: Add Network Error Handling
Adds robust error handling to all API calls
"""

import re
from pathlib import Path

def add_error_handling():
    """Add network error handling to all HTML files"""
    
    base_dir = Path(__file__).parent
    html_files = [
        'dashboard.html',
        'clients.html', 
        'vendeurs.html',
        'emails.html',
        'reports.html'
    ]
    
    # Error handling function to inject
    error_handling_function = '''
        // ===== GESTION D'ERREURS RÉSEAU ROBUSTE =====
        async function safeApiCall(url, options = {}) {
            const defaultOptions = {
                timeout: 10000,
                headers: {
                    'Content-Type': 'application/json'
                },
                ...options
            };
            
            try {
                // Add timeout to fetch
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), defaultOptions.timeout);
                
                const response = await fetch(url, {
                    ...defaultOptions,
                    signal: controller.signal
                });
                
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (!result.success && result.error) {
                    throw new Error(result.error);
                }
                
                return result;
                
            } catch (error) {
                console.error('API Error:', error);
                
                if (error.name === 'AbortError') {
                    showNotification('⏱️ Timeout: Le serveur met trop de temps à répondre', 'error');
                } else if (error.message.includes('Failed to fetch')) {
                    showNotification('🌐 Erreur réseau: Vérifiez votre connexion internet', 'error');
                } else if (error.message.includes('HTTP 500')) {
                    showNotification('🔧 Erreur serveur: Contactez l\\'administrateur', 'error');
                } else {
                    showNotification(`❌ Erreur: ${error.message}`, 'error');
                }
                
                return { success: false, error: error.message };
            }
        }
        
        // Fonction utilitaire pour vérifier la connectivité
        async function checkServerConnectivity() {
            try {
                const result = await safeApiCall('/api/health');
                return result.success;
            } catch (error) {
                return false;
            }
        }
'''
    
    print("🔧 CORRECTION CRITIQUE #2: Gestion d'erreurs réseau")
    print("="*60)
    
    for html_file in html_files:
        file_path = base_dir / html_file
        
        if not file_path.exists():
            print(f"  ❌ {html_file} - Fichier non trouvé")
            continue
            
        try:
            # Lire le fichier
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Vérifier si la fonction existe déjà
            if 'safeApiCall' in content:
                print(f"  ✅ {html_file} - Gestion d'erreurs déjà présente")
                continue
            
            # Trouver le premier script tag et ajouter après
            script_pattern = r'(<script[^>]*>)'
            if re.search(script_pattern, content):
                new_content = re.sub(
                    script_pattern, 
                    r'\1' + error_handling_function,
                    content,
                    count=1  # Seulement le premier script tag
                )
                
                # Sauvegarder le fichier modifié
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(new_content)
                
                print(f"  ✅ {html_file} - Gestion d'erreurs ajoutée")
            else:
                print(f"  ❌ {html_file} - Balise <script> non trouvée")
                
        except Exception as e:
            print(f"  ❌ {html_file} - Erreur: {str(e)}")
    
    print(f"\n🎯 CORRECTION TERMINÉE")
    print("⚠️  ÉTAPE MANUELLE REQUISE:")
    print("   Remplacez les appels fetch() existants par safeApiCall()")
    print("   Exemple: fetch('/api/clients') → safeApiCall('/api/clients')")
    
    # Générer un guide de remplacement
    generate_replacement_guide()

def generate_replacement_guide():
    """Générer un guide pour remplacer les appels fetch"""
    
    guide_content = '''# GUIDE DE REMPLACEMENT DES APPELS FETCH

## Transformations à effectuer dans chaque fichier HTML:

### AVANT (à remplacer):
```javascript
const response = await fetch('/api/clients', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
});
const result = await response.json();
```

### APRÈS (nouveau code):
```javascript
const result = await safeApiCall('/api/clients', {
    method: 'POST',
    body: JSON.stringify(data)
});
```

## Fichiers à modifier:

### clients.html:
- Ligne ~1350: loadClientsFromStorage()
- Ligne ~850: saveClient()
- Ligne ~950: updateClient()

### vendeurs.html:
- Ligne ~700: saveVendeur()
- Ligne ~750: loadVendeurs()

### emails.html:
- Ligne ~1400: sendEmail()
- Ligne ~1450: loadTemplates()

### reports.html:
- Ligne ~1100: generateReport()

### dashboard.html:
- Ligne ~460: updateDashboardStats()

## Recherche et remplacement automatique:
1. Rechercher: `fetch\\(`
2. Remplacer par: `safeApiCall(`
3. Supprimer les lignes `await response.json()` devenues inutiles
'''
    
    guide_path = Path(__file__).parent / "GUIDE_REMPLACEMENT_FETCH.md"
    with open(guide_path, 'w', encoding='utf-8') as f:
        f.write(guide_content)
    
    print(f"📄 Guide détaillé créé: {guide_path}")

if __name__ == "__main__":
    add_error_handling()

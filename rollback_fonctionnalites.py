#!/usr/bin/env python3
"""
BINANCE CRM - Script de Rollback des Fonctionnalités
Script pour annuler les modifications des fonctionnalités prioritaires si nécessaire
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime

class FeatureRollback:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.backup_dir = None
        self.rollback_log = []
        
    def find_latest_backup(self):
        """Trouver la sauvegarde la plus récente"""
        backups_dir = self.base_dir / "backups"
        if not backups_dir.exists():
            return None
        
        backup_folders = [d for d in backups_dir.iterdir() if d.is_dir() and d.name.startswith('backup_')]
        if not backup_folders:
            return None
        
        # Trier par date (le plus récent en premier)
        backup_folders.sort(key=lambda x: x.name, reverse=True)
        return backup_folders[0]
    
    def log_action(self, action, status, details=""):
        """Enregistrer une action de rollback"""
        self.rollback_log.append({
            'timestamp': datetime.now().isoformat(),
            'action': action,
            'status': status,
            'details': details
        })
        
        status_icon = "✅" if status == "success" else "❌" if status == "error" else "⚠️"
        print(f"  {status_icon} {action}: {details}")
    
    def rollback_clients_html(self):
        """Restaurer clients.html"""
        try:
            backup_file = self.backup_dir / "clients.html"
            target_file = self.base_dir / "clients.html"
            
            if backup_file.exists():
                shutil.copy2(backup_file, target_file)
                self.log_action("Rollback clients.html", "success", "Fichier restauré")
                return True
            else:
                self.log_action("Rollback clients.html", "error", "Fichier de sauvegarde non trouvé")
                return False
        except Exception as e:
            self.log_action("Rollback clients.html", "error", str(e))
            return False
    
    def rollback_admin_config_html(self):
        """Restaurer admin_config.html"""
        try:
            backup_file = self.backup_dir / "admin_config.html"
            target_file = self.base_dir / "admin_config.html"
            
            if backup_file.exists():
                shutil.copy2(backup_file, target_file)
                self.log_action("Rollback admin_config.html", "success", "Fichier restauré")
                return True
            else:
                self.log_action("Rollback admin_config.html", "error", "Fichier de sauvegarde non trouvé")
                return False
        except Exception as e:
            self.log_action("Rollback admin_config.html", "error", str(e))
            return False
    
    def rollback_emails_html(self):
        """Restaurer emails.html"""
        try:
            backup_file = self.backup_dir / "emails.html"
            target_file = self.base_dir / "emails.html"
            
            if backup_file.exists():
                shutil.copy2(backup_file, target_file)
                self.log_action("Rollback emails.html", "success", "Fichier restauré")
                return True
            else:
                self.log_action("Rollback emails.html", "error", "Fichier de sauvegarde non trouvé")
                return False
        except Exception as e:
            self.log_action("Rollback emails.html", "error", str(e))
            return False
    
    def remove_new_files(self):
        """Supprimer les nouveaux fichiers créés"""
        new_files = [
            'test_fonctionnalites_prioritaires.py',
            'GUIDE_FONCTIONNALITES_PRIORITAIRES.md',
            'rollback_fonctionnalites.py'
        ]
        
        for filename in new_files:
            try:
                file_path = self.base_dir / filename
                if file_path.exists() and filename != 'rollback_fonctionnalites.py':  # Ne pas se supprimer soi-même
                    file_path.unlink()
                    self.log_action(f"Suppression {filename}", "success", "Fichier supprimé")
            except Exception as e:
                self.log_action(f"Suppression {filename}", "error", str(e))
    
    def create_rollback_report(self):
        """Créer un rapport de rollback"""
        report = {
            'rollback_date': datetime.now().isoformat(),
            'backup_used': str(self.backup_dir) if self.backup_dir else None,
            'actions': self.rollback_log,
            'summary': {
                'total_actions': len(self.rollback_log),
                'successful': len([a for a in self.rollback_log if a['status'] == 'success']),
                'failed': len([a for a in self.rollback_log if a['status'] == 'error'])
            }
        }
        
        report_file = self.base_dir / f"rollback_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return report_file
    
    def perform_rollback(self, backup_path=None):
        """Effectuer le rollback complet"""
        print("🔄 ROLLBACK DES FONCTIONNALITÉS PRIORITAIRES - BINANCE CRM")
        print("=" * 60)
        
        # Trouver la sauvegarde à utiliser
        if backup_path:
            self.backup_dir = Path(backup_path)
        else:
            self.backup_dir = self.find_latest_backup()
        
        if not self.backup_dir or not self.backup_dir.exists():
            print("❌ Aucune sauvegarde trouvée pour le rollback")
            print("   Vérifiez le dossier 'backups' ou spécifiez un chemin de sauvegarde")
            return False
        
        print(f"📁 Utilisation de la sauvegarde: {self.backup_dir}")
        print()
        
        # Demander confirmation
        response = input("⚠️  Êtes-vous sûr de vouloir effectuer le rollback ? (oui/non): ")
        if response.lower() not in ['oui', 'o', 'yes', 'y']:
            print("❌ Rollback annulé par l'utilisateur")
            return False
        
        print("\n🔄 Début du rollback...")
        
        # Effectuer les rollbacks
        success_count = 0
        total_count = 0
        
        # Rollback des fichiers HTML
        rollback_actions = [
            ("Clients HTML", self.rollback_clients_html),
            ("Admin Config HTML", self.rollback_admin_config_html),
            ("Emails HTML", self.rollback_emails_html)
        ]
        
        for action_name, action_func in rollback_actions:
            total_count += 1
            if action_func():
                success_count += 1
        
        # Supprimer les nouveaux fichiers (optionnel)
        remove_new = input("\n🗑️  Supprimer les nouveaux fichiers créés ? (oui/non): ")
        if remove_new.lower() in ['oui', 'o', 'yes', 'y']:
            self.remove_new_files()
        
        # Générer le rapport
        report_file = self.create_rollback_report()
        
        # Résumé final
        print(f"\n📊 RÉSUMÉ DU ROLLBACK:")
        print(f"  Actions réussies: {success_count}")
        print(f"  Actions échouées: {total_count - success_count}")
        print(f"  Rapport généré: {report_file}")
        
        if success_count == total_count:
            print("\n✅ ROLLBACK TERMINÉ AVEC SUCCÈS")
            print("   Toutes les modifications ont été annulées")
            print("   Le système est revenu à l'état précédent")
        else:
            print("\n⚠️  ROLLBACK PARTIEL")
            print("   Certaines actions ont échoué")
            print("   Vérifiez le rapport pour les détails")
        
        print(f"\n🔄 PROCHAINES ÉTAPES:")
        print("1. Redémarrez le serveur BINANCE CRM")
        print("2. Vérifiez que les pages fonctionnent correctement")
        print("3. Consultez le rapport de rollback si nécessaire")
        
        return success_count == total_count
    
    def show_backup_info(self):
        """Afficher les informations sur les sauvegardes disponibles"""
        print("📁 INFORMATIONS SUR LES SAUVEGARDES")
        print("=" * 40)
        
        backups_dir = self.base_dir / "backups"
        if not backups_dir.exists():
            print("❌ Aucun dossier de sauvegarde trouvé")
            return
        
        backup_folders = [d for d in backups_dir.iterdir() if d.is_dir() and d.name.startswith('backup_')]
        if not backup_folders:
            print("❌ Aucune sauvegarde trouvée")
            return
        
        backup_folders.sort(key=lambda x: x.name, reverse=True)
        
        print(f"📊 {len(backup_folders)} sauvegarde(s) trouvée(s):")
        
        for i, backup_folder in enumerate(backup_folders):
            # Extraire la date du nom du dossier
            date_str = backup_folder.name.replace('backup_', '')
            try:
                date_obj = datetime.strptime(date_str, '%Y%m%d_%H%M%S')
                formatted_date = date_obj.strftime('%d/%m/%Y à %H:%M:%S')
            except:
                formatted_date = date_str
            
            # Compter les fichiers dans la sauvegarde
            files = list(backup_folder.glob('*.html'))
            
            status = "📁 RÉCENTE" if i == 0 else "📂"
            print(f"  {status} {formatted_date}")
            print(f"      Chemin: {backup_folder}")
            print(f"      Fichiers: {len(files)} fichier(s)")
            
            if files:
                for file in files:
                    size_kb = file.stat().st_size / 1024
                    print(f"        - {file.name} ({size_kb:.1f} KB)")
            print()

def main():
    """Point d'entrée principal"""
    rollback = FeatureRollback()
    
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == '--info':
            rollback.show_backup_info()
            return
        elif sys.argv[1] == '--backup':
            if len(sys.argv) > 2:
                rollback.perform_rollback(sys.argv[2])
            else:
                print("❌ Veuillez spécifier le chemin de la sauvegarde")
                print("   Exemple: python rollback_fonctionnalites.py --backup backups/backup_20250120_143000")
            return
    
    # Mode interactif par défaut
    print("🔄 SCRIPT DE ROLLBACK - BINANCE CRM")
    print("=" * 40)
    print()
    print("Options disponibles:")
    print("1. Effectuer un rollback automatique (dernière sauvegarde)")
    print("2. Voir les informations sur les sauvegardes")
    print("3. Rollback avec sauvegarde spécifique")
    print("4. Quitter")
    print()
    
    while True:
        choice = input("Votre choix (1-4): ").strip()
        
        if choice == '1':
            rollback.perform_rollback()
            break
        elif choice == '2':
            rollback.show_backup_info()
            print()
        elif choice == '3':
            rollback.show_backup_info()
            backup_path = input("\nEntrez le chemin de la sauvegarde: ").strip()
            if backup_path:
                rollback.perform_rollback(backup_path)
            break
        elif choice == '4':
            print("👋 Au revoir!")
            break
        else:
            print("❌ Choix invalide, veuillez choisir entre 1 et 4")

if __name__ == "__main__":
    main()

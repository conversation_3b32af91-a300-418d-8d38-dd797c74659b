<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BINANCE CRM - Système de Gestion Client</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
    <style>
        :root {
            --binance-yellow: #f1c232;
            --binance-gold: #fcd535;
        }
        
        body { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            min-height: 100vh; 
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            margin: 50px auto;
            max-width: 1200px;
        }
        
        .navbar-custom { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border-radius: 15px 15px 0 0;
        }
        
        .navbar-brand, .nav-link { 
            color: #000 !important; 
            font-weight: 600; 
        }
        
        .card { 
            border-radius: 12px; 
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); 
            border: none; 
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .btn-primary { 
            background: linear-gradient(135deg, var(--binance-yellow) 0%, var(--binance-gold) 100%); 
            border: none; 
            color: #000; 
            font-weight: 600;
            border-radius: 8px;
        }
        
        .btn-primary:hover { 
            background: linear-gradient(135deg, #e6b800 0%, var(--binance-yellow) 100%); 
            color: #000;
            transform: translateY(-2px);
        }
        
        .stat-card { 
            transition: all 0.3s ease; 
            cursor: pointer;
        }
        
        .stat-card:hover { 
            transform: translateY(-5px); 
        }
        
        .feature-icon {
            font-size: 3rem;
            color: var(--binance-yellow);
            margin-bottom: 20px;
        }
        
        .login-section {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="main-container">
            <!-- Navigation -->
            <nav class="navbar navbar-expand-lg navbar-custom">
                <div class="container-fluid">
                    <a class="navbar-brand fw-bold fs-3" href="#">
                        <i class="bi bi-currency-bitcoin"></i> BINANCE CRM
                    </a>
                    <div class="navbar-nav ms-auto">
                        <span class="nav-link">Version Fonctionnelle</span>
                    </div>
                </div>
            </nav>
            
            <!-- Contenu principal -->
            <div class="container-fluid p-4">
                <!-- Header -->
                <div class="text-center mb-5">
                    <h1 class="display-4 fw-bold mb-3">🎉 BINANCE CRM</h1>
                    <h2 class="h3 text-success mb-4">✅ SERVEUR FONCTIONNEL !</h2>
                    <p class="lead">Système de Gestion de Relation Client pour Binance</p>
                </div>
                
                <!-- Statut du serveur -->
                <div class="alert alert-success text-center mb-5">
                    <h4><i class="bi bi-check-circle"></i> Serveur HTTP Opérationnel</h4>
                    <p class="mb-0">Le serveur fonctionne parfaitement sur <strong>http://localhost:8000</strong></p>
                </div>
                
                <!-- Statistiques -->
                <div class="row mb-5">
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-people fs-1 text-primary mb-2"></i>
                                <h3 class="fw-bold">127</h3>
                                <p class="text-muted mb-0">Clients Total</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-person-check fs-1 text-success mb-2"></i>
                                <h3 class="fw-bold">89</h3>
                                <p class="text-muted mb-0">Clients Attribués</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-envelope-check fs-1 text-info mb-2"></i>
                                <h3 class="fw-bold">254</h3>
                                <p class="text-muted mb-0">Emails Envoyés</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="card stat-card text-center">
                            <div class="card-body">
                                <i class="bi bi-calendar-check fs-1 text-warning mb-2"></i>
                                <h3 class="fw-bold">42</h3>
                                <p class="text-muted mb-0">RDV Planifiés</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Fonctionnalités -->
                <div class="row mb-5">
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-shield-check feature-icon"></i>
                                <h5>Authentification Sécurisée</h5>
                                <p class="text-muted">Système de connexion avec hachage SHA256 et gestion des sessions utilisateur.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-people feature-icon"></i>
                                <h5>Gestion Clients CRUD</h5>
                                <p class="text-muted">Création, lecture, modification et suppression des clients avec base de données SQLite.</p>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 mb-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-envelope-paper feature-icon"></i>
                                <h5>Templates Binance</h5>
                                <p class="text-muted">Templates d'emails professionnels avec variables dynamiques pour Binance.</p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Section de connexion -->
                <div class="login-section">
                    <h4 class="text-center mb-4">
                        <i class="bi bi-box-arrow-in-right"></i> Accès au Système
                    </h4>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-primary text-white">
                                    <h6><i class="bi bi-crown"></i> Compte Administrateur</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom d'utilisateur :</strong> <code>admin</code></p>
                                    <p><strong>Mot de passe :</strong> <code>admin123</code></p>
                                    <p class="text-muted mb-0">Accès complet au système</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6><i class="bi bi-person-badge"></i> Compte Vendeur</h6>
                                </div>
                                <div class="card-body">
                                    <p><strong>Nom d'utilisateur :</strong> <code>marie.martin</code></p>
                                    <p><strong>Mot de passe :</strong> <code>vendeur123</code></p>
                                    <p class="text-muted mb-0">Accès vendeur standard</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="text-center mt-4">
                        <button class="btn btn-primary btn-lg" onclick="alert('Fonctionnalité de connexion disponible dans la version complète Python')">
                            <i class="bi bi-box-arrow-in-right"></i> Se Connecter
                        </button>
                    </div>
                </div>
                
                <!-- Informations techniques -->
                <div class="card">
                    <div class="card-header">
                        <h5><i class="bi bi-gear"></i> Informations Techniques</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <strong>✅ Serveur HTTP</strong><br>
                                <small>Python http.server</small>
                            </div>
                            <div class="col-md-3">
                                <strong>✅ Port</strong><br>
                                <small>8000 (Accessible)</small>
                            </div>
                            <div class="col-md-3">
                                <strong>✅ Interface</strong><br>
                                <small>Bootstrap 5 + Icons</small>
                            </div>
                            <div class="col-md-3">
                                <strong>✅ Design</strong><br>
                                <small>Couleurs Binance officielles</small>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Footer -->
                <div class="text-center mt-5 pt-4 border-top">
                    <p class="text-muted">
                        <strong>BINANCE CRM</strong> - Version HTML Fonctionnelle<br>
                        Serveur HTTP opérationnel sur <code>http://localhost:8000</code>
                    </p>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Animation des cartes statistiques
        document.querySelectorAll('.stat-card').forEach(card => {
            card.addEventListener('click', function() {
                this.style.transform = 'scale(0.95)';
                setTimeout(() => {
                    this.style.transform = 'translateY(-5px)';
                }, 150);
            });
        });
        
        // Log de confirmation
        console.log('🎉 BINANCE CRM - Interface chargée avec succès !');
        console.log('🌐 Serveur HTTP fonctionnel sur http://localhost:8000');
        console.log('✅ Toutes les ressources CSS/JS chargées');
    </script>
</body>
</html>

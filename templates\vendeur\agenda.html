{% extends "base.html" %}

{% block title %}Mon Agenda - CRM System{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2"><i class="bi bi-calendar"></i> Mon Agenda</h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="changeView('today')">
                Aujourd'hui
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="changeView('week')">
                Cette Semaine
            </button>
            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="changeView('month')">
                <PERSON> <PERSON><PERSON>
            </button>
        </div>
        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAppointmentModal">
            <i class="bi bi-plus"></i> Nouveau RDV
        </button>
    </div>
</div>

<!-- Filtres et navigation -->
<div class="card mb-3">
    <div class="card-body">
        <div class="row g-3 align-items-center">
            <div class="col-md-3">
                <label for="filter_client" class="form-label">Client</label>
                <select class="form-select" id="filter_client" onchange="filterAppointments()">
                    <option value="">Tous les clients</option>
                    {% for client in clients %}
                    <option value="{{ client.id }}">{{ client.prenom }} {{ client.nom }}</option>
                    {% endfor %}
                </select>
            </div>
            <div class="col-md-3">
                <label for="filter_status" class="form-label">Statut</label>
                <select class="form-select" id="filter_status" onchange="filterAppointments()">
                    <option value="">Tous les statuts</option>
                    <option value="planifie">Planifié</option>
                    <option value="realise">Réalisé</option>
                    <option value="annule">Annulé</option>
                </select>
            </div>
            <div class="col-md-3">
                <label for="date_debut" class="form-label">Date début</label>
                <input type="date" class="form-control" id="date_debut" onchange="filterAppointments()">
            </div>
            <div class="col-md-3">
                <label for="date_fin" class="form-label">Date fin</label>
                <input type="date" class="form-control" id="date_fin" onchange="filterAppointments()">
            </div>
        </div>
    </div>
</div>

<!-- Statistiques rapides -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ appointments|length }}</h5>
                <p class="card-text">RDV Total</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ appointments|selectattr("statut", "equalto", "planifie")|list|length }}</h5>
                <p class="card-text">Planifiés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ appointments|selectattr("statut", "equalto", "realise")|list|length }}</h5>
                <p class="card-text">Réalisés</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-danger">{{ appointments|selectattr("statut", "equalto", "annule")|list|length }}</h5>
                <p class="card-text">Annulés</p>
            </div>
        </div>
    </div>
</div>

<!-- Vue Calendrier -->
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5><i class="bi bi-calendar-week"></i> Planning</h5>
            </div>
            <div class="card-body">
                <div id="calendar-view">
                    <!-- Vue calendrier sera générée par JavaScript -->
                    <div class="text-center py-4">
                        <div class="spinner-border" role="status">
                            <span class="visually-hidden">Chargement du calendrier...</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- RDV du jour -->
        <div class="card mb-3">
            <div class="card-header">
                <h6><i class="bi bi-calendar-day"></i> Aujourd'hui</h6>
            </div>
            <div class="card-body">
                {% set today_appointments = [] %}
                {% for appointment in appointments %}
                    {% if appointment.date_rdv.date() == moment().date() %}
                        {% set _ = today_appointments.append(appointment) %}
                    {% endif %}
                {% endfor %}
                
                {% if today_appointments %}
                    {% for appointment in today_appointments %}
                    <div class="appointment-item mb-2 p-2 border rounded">
                        <div class="d-flex justify-content-between">
                            <strong>{{ appointment.date_rdv.strftime('%H:%M') }}</strong>
                            <span class="badge bg-{{ 'success' if appointment.statut == 'realise' else 'warning' if appointment.statut == 'planifie' else 'danger' }}">
                                {{ appointment.statut|title }}
                            </span>
                        </div>
                        <div class="mt-1">
                            <strong>{{ appointment.client.prenom }} {{ appointment.client.nom }}</strong>
                        </div>
                        <div class="text-muted small">{{ appointment.titre }}</div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucun rendez-vous aujourd'hui</p>
                {% endif %}
            </div>
        </div>
        
        <!-- RDV à venir -->
        <div class="card">
            <div class="card-header">
                <h6><i class="bi bi-calendar-plus"></i> Prochains RDV</h6>
            </div>
            <div class="card-body">
                {% set upcoming = appointments|selectattr("date_rdv", "gt", moment())|selectattr("statut", "equalto", "planifie")|list|sort(attribute="date_rdv") %}
                {% if upcoming %}
                    {% for appointment in upcoming[:5] %}
                    <div class="appointment-item mb-2 p-2 border rounded">
                        <div class="d-flex justify-content-between">
                            <strong>{{ appointment.date_rdv.strftime('%d/%m %H:%M') }}</strong>
                            <button class="btn btn-sm btn-outline-primary" 
                                    onclick="editAppointment({{ appointment.id }})"
                                    data-bs-toggle="modal" data-bs-target="#editAppointmentModal">
                                <i class="bi bi-pencil"></i>
                            </button>
                        </div>
                        <div class="mt-1">
                            <strong>{{ appointment.client.prenom }} {{ appointment.client.nom }}</strong>
                        </div>
                        <div class="text-muted small">{{ appointment.titre }}</div>
                    </div>
                    {% endfor %}
                {% else %}
                    <p class="text-muted">Aucun RDV planifié</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Liste détaillée des RDV -->
<div class="card mt-4">
    <div class="card-header">
        <h5><i class="bi bi-list"></i> Tous mes Rendez-vous</h5>
    </div>
    <div class="card-body">
        {% if appointments %}
        <div class="table-responsive">
            <table class="table table-striped table-hover" id="appointments-table">
                <thead>
                    <tr>
                        <th>Date & Heure</th>
                        <th>Client</th>
                        <th>Titre</th>
                        <th>Description</th>
                        <th>Statut</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    {% for appointment in appointments|sort(attribute="date_rdv", reverse=true) %}
                    <tr data-client-id="{{ appointment.client_id }}" data-status="{{ appointment.statut }}">
                        <td>
                            <strong>{{ appointment.date_rdv.strftime('%d/%m/%Y') }}</strong><br>
                            <small class="text-muted">{{ appointment.date_rdv.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            <strong>{{ appointment.client.prenom }} {{ appointment.client.nom }}</strong><br>
                            <small class="text-muted">{{ appointment.client.telephone }}</small>
                        </td>
                        <td>{{ appointment.titre }}</td>
                        <td>
                            {% if appointment.description %}
                                <div class="text-truncate" style="max-width: 200px;" title="{{ appointment.description }}">
                                    {{ appointment.description }}
                                </div>
                            {% else %}
                                <span class="text-muted">-</span>
                            {% endif %}
                        </td>
                        <td>
                            <select class="form-select form-select-sm" 
                                    onchange="updateAppointmentStatus({{ appointment.id }}, this.value)">
                                <option value="planifie" {% if appointment.statut == "planifie" %}selected{% endif %}>Planifié</option>
                                <option value="realise" {% if appointment.statut == "realise" %}selected{% endif %}>Réalisé</option>
                                <option value="annule" {% if appointment.statut == "annule" %}selected{% endif %}>Annulé</option>
                            </select>
                        </td>
                        <td class="table-actions">
                            <div class="btn-group btn-group-sm">
                                <button type="button" class="btn btn-outline-primary" 
                                        onclick="editAppointment({{ appointment.id }})" 
                                        data-bs-toggle="modal" data-bs-target="#editAppointmentModal"
                                        title="Modifier">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" 
                                        onclick="sendReminderEmail({{ appointment.id }})"
                                        title="Envoyer rappel">
                                    <i class="bi bi-envelope"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" 
                                        onclick="deleteAppointment({{ appointment.id }})" 
                                        title="Supprimer">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
        {% else %}
        <div class="text-center py-4">
            <i class="bi bi-calendar fs-1 text-muted"></i>
            <p class="text-muted">Aucun rendez-vous planifié.</p>
            <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addAppointmentModal">
                <i class="bi bi-plus"></i> Planifier le premier RDV
            </button>
        </div>
        {% endif %}
    </div>
</div>

<!-- Modal Nouveau RDV -->
<div class="modal fade" id="addAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-plus"></i> Nouveau Rendez-vous</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="addAppointmentForm">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="client_id" class="form-label">Client *</label>
                        <select class="form-select" id="client_id" name="client_id" required>
                            <option value="">Choisir un client</option>
                            {% for client in clients %}
                            <option value="{{ client.id }}">{{ client.prenom }} {{ client.nom }} - {{ client.email }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="date_rdv" class="form-label">Date et Heure *</label>
                        <input type="datetime-local" class="form-control" id="date_rdv" name="date_rdv" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="titre" class="form-label">Titre *</label>
                        <input type="text" class="form-control" id="titre" name="titre" 
                               placeholder="Ex: Rendez-vous commercial" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="description" class="form-label">Description</label>
                        <textarea class="form-control" id="description" name="description" 
                                  rows="3" placeholder="Détails du rendez-vous..."></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="statut" class="form-label">Statut</label>
                        <select class="form-select" id="statut" name="statut">
                            <option value="planifie">Planifié</option>
                            <option value="realise">Réalisé</option>
                            <option value="annule">Annulé</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal Modifier RDV -->
<div class="modal fade" id="editAppointmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title"><i class="bi bi-pencil"></i> Modifier Rendez-vous</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form id="editAppointmentForm">
                <div class="modal-body">
                    <input type="hidden" id="edit_appointment_id" name="appointment_id">
                    
                    <div class="mb-3">
                        <label for="edit_client_id" class="form-label">Client *</label>
                        <select class="form-select" id="edit_client_id" name="client_id" required>
                            {% for client in clients %}
                            <option value="{{ client.id }}">{{ client.prenom }} {{ client.nom }} - {{ client.email }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_date_rdv" class="form-label">Date et Heure *</label>
                        <input type="datetime-local" class="form-control" id="edit_date_rdv" name="date_rdv" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_titre" class="form-label">Titre *</label>
                        <input type="text" class="form-control" id="edit_titre" name="titre" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">Description</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="edit_statut" class="form-label">Statut</label>
                        <select class="form-select" id="edit_statut" name="statut">
                            <option value="planifie">Planifié</option>
                            <option value="realise">Réalisé</option>
                            <option value="annule">Annulé</option>
                        </select>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
                    <button type="submit" class="btn btn-primary">Sauvegarder</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Données des rendez-vous pour JavaScript
const appointmentsData = {{ appointments|tojson }};
const clientsData = {{ clients|tojson }};

// Définir la date par défaut à demain 14h
document.addEventListener('DOMContentLoaded', function() {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(14, 0, 0, 0);
    
    document.getElementById('date_rdv').value = tomorrow.toISOString().slice(0, 16);
    
    // Générer la vue calendrier
    generateCalendarView();
});

function changeView(view) {
    // TODO: Implémenter les différentes vues
    console.log('Changing view to:', view);
}

function filterAppointments() {
    const clientFilter = document.getElementById('filter_client').value;
    const statusFilter = document.getElementById('filter_status').value;
    const dateDebutFilter = document.getElementById('date_debut').value;
    const dateFinFilter = document.getElementById('date_fin').value;
    
    const rows = document.querySelectorAll('#appointments-table tbody tr');
    
    rows.forEach(row => {
        let show = true;
        
        if (clientFilter && row.dataset.clientId !== clientFilter) {
            show = false;
        }
        
        if (statusFilter && row.dataset.status !== statusFilter) {
            show = false;
        }
        
        // TODO: Ajouter filtrage par date
        
        row.style.display = show ? '' : 'none';
    });
}

function updateAppointmentStatus(appointmentId, newStatus) {
    fetch(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify({
            statut: newStatus
        })
    }).then(response => {
        if (!response.ok) {
            alert('Erreur lors de la mise à jour du statut');
            location.reload(); // Recharger pour annuler le changement
        }
    });
}

function editAppointment(appointmentId) {
    const appointment = appointmentsData.find(a => a.id === appointmentId);
    if (appointment) {
        document.getElementById('edit_appointment_id').value = appointment.id;
        document.getElementById('edit_client_id').value = appointment.client_id;
        document.getElementById('edit_date_rdv').value = new Date(appointment.date_rdv).toISOString().slice(0, 16);
        document.getElementById('edit_titre').value = appointment.titre;
        document.getElementById('edit_description').value = appointment.description || '';
        document.getElementById('edit_statut').value = appointment.statut;
    }
}

function deleteAppointment(appointmentId) {
    if (confirmDelete('Êtes-vous sûr de vouloir supprimer ce rendez-vous ?')) {
        fetch(`/api/appointments/${appointmentId}`, {
            method: 'DELETE'
        }).then(response => {
            if (response.ok) {
                location.reload();
            } else {
                alert('Erreur lors de la suppression');
            }
        });
    }
}

function sendReminderEmail(appointmentId) {
    // TODO: Implémenter l'envoi de rappel
    alert('Fonctionnalité de rappel en cours de développement');
}

function generateCalendarView() {
    // Générer une vue calendrier simple
    const calendarDiv = document.getElementById('calendar-view');
    
    // Pour l'instant, afficher une vue liste simple
    let html = '<div class="row">';
    
    // Grouper les RDV par date
    const appointmentsByDate = {};
    appointmentsData.forEach(appointment => {
        const date = new Date(appointment.date_rdv).toDateString();
        if (!appointmentsByDate[date]) {
            appointmentsByDate[date] = [];
        }
        appointmentsByDate[date].push(appointment);
    });
    
    // Afficher les 7 prochains jours
    for (let i = 0; i < 7; i++) {
        const date = new Date();
        date.setDate(date.getDate() + i);
        const dateStr = date.toDateString();
        const dayAppointments = appointmentsByDate[dateStr] || [];
        
        html += `
            <div class="col-md-1 mb-3">
                <div class="card ${dayAppointments.length > 0 ? 'border-primary' : ''}">
                    <div class="card-header text-center p-2">
                        <small>${date.toLocaleDateString('fr-FR', {weekday: 'short'})}</small><br>
                        <strong>${date.getDate()}</strong>
                    </div>
                    <div class="card-body p-2">
                        ${dayAppointments.map(apt => `
                            <div class="small mb-1 p-1 bg-light rounded">
                                ${new Date(apt.date_rdv).toLocaleTimeString('fr-FR', {hour: '2-digit', minute: '2-digit'})}
                            </div>
                        `).join('')}
                    </div>
                </div>
            </div>
        `;
    }
    
    html += '</div>';
    calendarDiv.innerHTML = html;
}

// Gestion du formulaire d'ajout
document.getElementById('addAppointmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    data.client_id = parseInt(data.client_id);
    
    fetch('/api/appointments', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('Erreur lors de la création du rendez-vous');
        }
    });
});

// Gestion du formulaire de modification
document.getElementById('editAppointmentForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData.entries());
    const appointmentId = data.appointment_id;
    delete data.appointment_id;
    data.client_id = parseInt(data.client_id);
    
    fetch(`/api/appointments/${appointmentId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(data)
    }).then(response => {
        if (response.ok) {
            location.reload();
        } else {
            alert('Erreur lors de la modification');
        }
    });
});
</script>
{% endblock %}

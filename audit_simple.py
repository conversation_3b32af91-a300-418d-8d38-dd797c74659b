#!/usr/bin/env python3
"""
BINANCE CRM - Audit Simple
Version simplifiée de l'audit pour identifier les problèmes principaux
"""

import re
import json
from pathlib import Path
from datetime import datetime

def analyze_html_files():
    """Analyser les fichiers HTML pour identifier les problèmes"""
    base_dir = Path(__file__).parent
    issues = []
    
    pages = [
        'dashboard.html',
        'clients.html', 
        'vendeurs.html',
        'emails.html',
        'reports.html',
        'admin_config.html',
        'login.html'
    ]
    
    print("🔍 AUDIT SIMPLE - BINANCE CRM")
    print("="*50)
    
    for page in pages:
        file_path = base_dir / page
        if file_path.exists():
            print(f"\n📄 Analyse de {page}...")
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Analyser les fonctions JavaScript manquantes
                onclick_calls = re.findall(r'onclick="([^"]+)"', content)
                function_calls = set()
                
                for call in onclick_calls:
                    func_name = call.split('(')[0].strip()
                    if func_name:
                        function_calls.add(func_name)
                
                missing_functions = []
                for func_name in function_calls:
                    if f'function {func_name}' not in content and f'{func_name} =' not in content:
                        missing_functions.append(func_name)
                
                if missing_functions:
                    print(f"  ❌ Fonctions manquantes: {', '.join(missing_functions)}")
                    issues.append({
                        'page': page,
                        'type': 'Fonction manquante',
                        'details': missing_functions,
                        'severity': 'Critique'
                    })
                else:
                    print(f"  ✅ Toutes les fonctions JavaScript sont définies")
                
                # Vérifier les formulaires sans validation
                forms = re.findall(r'<form[^>]*>.*?</form>', content, re.DOTALL)
                for form in forms:
                    if 'required' not in form:
                        print(f"  ⚠️  Formulaire sans validation required")
                        issues.append({
                            'page': page,
                            'type': 'Validation manquante',
                            'details': 'Formulaire sans attributs required',
                            'severity': 'Moyen'
                        })
                
                # Vérifier les boutons sans feedback
                if '<button' in content and 'showNotification' not in content:
                    print(f"  ⚠️  Pas de système de notifications")
                    issues.append({
                        'page': page,
                        'type': 'Feedback manquant',
                        'details': 'Pas de système de notifications',
                        'severity': 'Moyen'
                    })
                
                # Vérifier la responsivité mobile
                if '<meta name="viewport"' not in content:
                    print(f"  ⚠️  Meta viewport manquant")
                    issues.append({
                        'page': page,
                        'type': 'Mobile non responsive',
                        'details': 'Meta viewport manquant',
                        'severity': 'Élevé'
                    })
                
            except Exception as e:
                print(f"  ❌ Erreur lors de l'analyse: {str(e)}")
                issues.append({
                    'page': page,
                    'type': 'Erreur de lecture',
                    'details': str(e),
                    'severity': 'Critique'
                })
        else:
            print(f"  ❌ Fichier {page} manquant")
            issues.append({
                'page': page,
                'type': 'Fichier manquant',
                'details': f'Le fichier {page} n\'existe pas',
                'severity': 'Critique'
            })
    
    return issues

def generate_summary_report(issues):
    """Générer un rapport de synthèse"""
    print(f"\n📊 RAPPORT DE SYNTHÈSE")
    print("="*50)
    
    # Compter par sévérité
    severity_count = {}
    for issue in issues:
        severity = issue['severity']
        severity_count[severity] = severity_count.get(severity, 0) + 1
    
    print(f"\n🎯 RÉSUMÉ:")
    print(f"  Total des problèmes: {len(issues)}")
    
    for severity in ['Critique', 'Élevé', 'Moyen', 'Faible']:
        count = severity_count.get(severity, 0)
        if count > 0:
            icon = {'Critique': '🔴', 'Élevé': '🟠', 'Moyen': '🟡', 'Faible': '🔵'}[severity]
            print(f"  {icon} {severity}: {count}")
    
    # Détailler les problèmes critiques
    critical_issues = [i for i in issues if i['severity'] == 'Critique']
    if critical_issues:
        print(f"\n🚨 PROBLÈMES CRITIQUES (Action immédiate requise):")
        for issue in critical_issues:
            print(f"  • {issue['page']}: {issue['type']}")
            if isinstance(issue['details'], list):
                print(f"    Détails: {', '.join(issue['details'])}")
            else:
                print(f"    Détails: {issue['details']}")
    
    # Recommandations prioritaires
    print(f"\n🎯 ACTIONS PRIORITAIRES:")
    
    # Fonctions manquantes
    missing_func_pages = [i['page'] for i in issues if i['type'] == 'Fonction manquante']
    if missing_func_pages:
        print(f"  1. Implémenter les fonctions JavaScript manquantes dans:")
        for page in set(missing_func_pages):
            print(f"     - {page}")
    
    # Validation manquante
    validation_pages = [i['page'] for i in issues if i['type'] == 'Validation manquante']
    if validation_pages:
        print(f"  2. Ajouter la validation des formulaires dans:")
        for page in set(validation_pages):
            print(f"     - {page}")
    
    # Mobile non responsive
    mobile_pages = [i['page'] for i in issues if i['type'] == 'Mobile non responsive']
    if mobile_pages:
        print(f"  3. Ajouter la responsivité mobile dans:")
        for page in set(mobile_pages):
            print(f"     - {page}")
    
    # Sauvegarder le rapport
    report_data = {
        'audit_date': datetime.now().isoformat(),
        'total_issues': len(issues),
        'severity_breakdown': severity_count,
        'issues': issues
    }
    
    report_file = Path(__file__).parent / f"audit_simple_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report_data, f, indent=2, ensure_ascii=False)
    
    print(f"\n📄 Rapport détaillé sauvegardé: {report_file}")
    
    return report_data

def check_database_file():
    """Vérifier l'existence et l'intégrité de la base de données"""
    print(f"\n🗄️  VÉRIFICATION BASE DE DONNÉES")
    print("-"*40)
    
    db_path = Path(__file__).parent / "binance_crm.db"
    if db_path.exists():
        size_mb = db_path.stat().st_size / (1024 * 1024)
        print(f"  ✅ Base de données trouvée: {size_mb:.2f} MB")
        
        try:
            import sqlite3
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Vérifier les tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            print(f"  📊 Tables trouvées: {len(tables)}")
            for table in tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"    - {table}: {count} enregistrements")
            
            conn.close()
            return True
            
        except Exception as e:
            print(f"  ❌ Erreur d'accès à la base: {str(e)}")
            return False
    else:
        print(f"  ❌ Base de données manquante: {db_path}")
        return False

def check_server_files():
    """Vérifier les fichiers serveur"""
    print(f"\n🖥️  VÉRIFICATION FICHIERS SERVEUR")
    print("-"*40)
    
    server_files = [
        'database_server.py',
        'email_server.py',
        'pdf_server.py'
    ]
    
    for server_file in server_files:
        file_path = Path(__file__).parent / server_file
        if file_path.exists():
            size_kb = file_path.stat().st_size / 1024
            print(f"  ✅ {server_file}: {size_kb:.1f} KB")
        else:
            print(f"  ❌ {server_file}: Manquant")

def main():
    """Fonction principale d'audit"""
    print("🚀 DÉMARRAGE AUDIT SIMPLE BINANCE CRM")
    print("="*60)
    
    # Analyser les fichiers HTML
    issues = analyze_html_files()
    
    # Vérifier la base de données
    db_ok = check_database_file()
    
    # Vérifier les serveurs
    check_server_files()
    
    # Générer le rapport
    report = generate_summary_report(issues)
    
    # Conclusion
    print(f"\n🎊 AUDIT TERMINÉ")
    print("="*30)
    
    critical_count = len([i for i in issues if i['severity'] == 'Critique'])
    
    if critical_count == 0:
        print("✅ Aucun problème critique détecté")
        print("🎯 Le système semble stable pour les tests")
    elif critical_count <= 3:
        print(f"⚠️  {critical_count} problème(s) critique(s) détecté(s)")
        print("🔧 Corrections recommandées avant production")
    else:
        print(f"🚨 {critical_count} problèmes critiques détectés")
        print("❌ Corrections urgentes requises")
    
    print(f"\n📋 PROCHAINES ÉTAPES:")
    print("1. Corriger les problèmes critiques identifiés")
    print("2. Tester toutes les fonctionnalités manuellement")
    print("3. Démarrer les serveurs et tester les API")
    print("4. Valider sur différents navigateurs et appareils")
    
    return len(issues)

if __name__ == "__main__":
    try:
        issue_count = main()
        exit(0 if issue_count < 5 else 1)
    except Exception as e:
        print(f"❌ Erreur lors de l'audit: {str(e)}")
        exit(1)

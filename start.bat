@echo off
REM Script de démarrage pour l'application CRM FastAPI sur Windows
REM Usage: start.bat [dev|prod]

setlocal enabledelayedexpansion

REM Configuration par défaut
set MODE=%1
if "%MODE%"=="" set MODE=dev
set HOST=0.0.0.0
set PORT=8000
set WORKERS=1

echo [INFO] Démarrage de l'application CRM en mode: %MODE%

REM Vérifier si Python est installé
python --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] Python n'est pas installé ou pas dans le PATH
    pause
    exit /b 1
)

REM Vérifier si pip est installé
pip --version >nul 2>&1
if errorlevel 1 (
    echo [ERROR] pip n'est pas installé
    pause
    exit /b 1
)

REM Créer un environnement virtuel s'il n'existe pas
if not exist "venv" (
    echo [INFO] Création de l'environnement virtuel...
    python -m venv venv
    echo [SUCCESS] Environnement virtuel créé
)

REM Activer l'environnement virtuel
echo [INFO] Activation de l'environnement virtuel...
call venv\Scripts\activate.bat

REM Installer les dépendances
echo [INFO] Installation des dépendances...
pip install -r requirements.txt
if errorlevel 1 (
    echo [ERROR] Erreur lors de l'installation des dépendances
    pause
    exit /b 1
)
echo [SUCCESS] Dépendances installées

REM Créer le fichier .env s'il n'existe pas
if not exist ".env" (
    echo [INFO] Création du fichier .env...
    (
        echo # Configuration de l'application CRM
        echo SECRET_KEY=your-secret-key-change-this-in-production-%RANDOM%%RANDOM%
        echo DATABASE_URL=sqlite:///./crm.db
        echo.
        echo # Configuration optionnelle
        echo DEBUG=true
        echo LOG_LEVEL=INFO
        echo.
        echo # Configuration SMTP ^(optionnel - peut être configuré via l'interface^)
        echo # SMTP_HOST=smtp.gmail.com
        echo # SMTP_PORT=587
        echo # SMTP_USERNAME=<EMAIL>
        echo # SMTP_PASSWORD=your-app-password
        echo # SMTP_FROM_EMAIL=<EMAIL>
        echo # SMTP_FROM_NAME=CRM System
    ) > .env
    echo [SUCCESS] Fichier .env créé
)

REM Créer les dossiers nécessaires s'ils n'existent pas
if not exist "static\uploads" mkdir static\uploads
if not exist "logs" mkdir logs

REM Initialiser la base de données
echo [INFO] Initialisation de la base de données...
python -c "
from database import create_tables
from crud import create_user, get_user_by_username
from schemas import UserCreate
from database import get_db

# Créer les tables
create_tables()

# Créer un admin par défaut s'il n'existe pas
db = next(get_db())
if not get_user_by_username(db, 'admin'):
    admin_user = UserCreate(
        username='admin',
        email='<EMAIL>',
        password='admin123',
        role='admin'
    )
    create_user(db, admin_user)
    print('Utilisateur admin créé: admin / admin123')
else:
    print('Utilisateur admin existe déjà')
"
if errorlevel 1 (
    echo [ERROR] Erreur lors de l'initialisation de la base de données
    pause
    exit /b 1
)
echo [SUCCESS] Base de données initialisée

REM Afficher les informations de connexion
echo.
echo ===================================
echo [INFO] INFORMATIONS DE CONNEXION
echo [SUCCESS] URL: http://%HOST%:%PORT%
echo [SUCCESS] Utilisateur admin: admin
echo [SUCCESS] Mot de passe admin: admin123
echo ===================================
echo.

REM Démarrer l'application selon le mode
if "%MODE%"=="prod" (
    echo [INFO] Démarrage en mode production avec Gunicorn...
    
    REM Installer Gunicorn si pas présent
    pip install gunicorn
    
    REM Démarrer avec Gunicorn
    gunicorn main:app --host %HOST% --port %PORT% --workers %WORKERS% --worker-class uvicorn.workers.UvicornWorker --access-logfile logs/access.log --error-logfile logs/error.log --log-level info
) else (
    echo [INFO] Démarrage en mode développement avec Uvicorn...
    echo [INFO] Appuyez sur Ctrl+C pour arrêter le serveur
    echo.
    
    REM Démarrer avec Uvicorn en mode développement
    uvicorn main:app --host %HOST% --port %PORT% --reload --log-level info
)

pause

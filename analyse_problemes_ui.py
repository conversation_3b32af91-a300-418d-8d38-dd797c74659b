#!/usr/bin/env python3
"""
BINANCE CRM - Analyse des Problèmes UI
Analyse spécifique des problèmes d'interface utilisateur et d'expérience utilisateur
"""

import re
import json
from pathlib import Path
from datetime import datetime

class UIProblemsAnalyzer:
    def __init__(self):
        self.base_dir = Path(__file__).parent
        self.ui_issues = {
            'broken_functionality': [],
            'missing_validation': [],
            'poor_ux': [],
            'inconsistencies': [],
            'accessibility': [],
            'mobile_issues': []
        }
        
    def analyze_html_file(self, file_path, page_name):
        """Analyser un fichier HTML pour les problèmes UI"""
        print(f"🔍 Analyse de {page_name}...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Analyser les différents aspects
            self.check_broken_functions(content, page_name)
            self.check_form_validation(content, page_name)
            self.check_user_feedback(content, page_name)
            self.check_accessibility(content, page_name)
            self.check_mobile_responsiveness(content, page_name)
            self.check_consistency(content, page_name)
            
        except Exception as e:
            self.log_issue('broken_functionality', page_name, 
                         f'Impossible de lire le fichier: {str(e)}',
                         'Vérifier l\'existence et les permissions du fichier', 'Critique')
    
    def check_broken_functions(self, content, page_name):
        """Vérifier les fonctions JavaScript cassées ou manquantes"""
        
        # Trouver toutes les fonctions appelées
        onclick_calls = re.findall(r'onclick="([^"]+)"', content)
        function_calls = set()
        
        for call in onclick_calls:
            # Extraire le nom de la fonction
            func_name = call.split('(')[0].strip()
            if func_name:
                function_calls.add(func_name)
        
        # Vérifier si les fonctions sont définies
        for func_name in function_calls:
            if f'function {func_name}' not in content and f'{func_name} =' not in content:
                self.log_issue('broken_functionality', page_name,
                             f'Fonction JavaScript manquante: {func_name}()',
                             f'Implémenter la fonction {func_name} ou supprimer l\'appel',
                             'Élevé')
        
        # Vérifier les boutons sans action
        button_pattern = r'<button[^>]*>.*?</button>'
        buttons = re.findall(button_pattern, content, re.DOTALL)
        
        for button in buttons:
            if 'onclick=' not in button and 'type="submit"' not in button and 'data-bs-' not in button:
                button_text = re.sub(r'<[^>]+>', '', button).strip()
                if button_text and len(button_text) < 50:  # Éviter les faux positifs
                    self.log_issue('broken_functionality', page_name,
                                 f'Bouton sans action: "{button_text}"',
                                 'Ajouter un onclick ou une action appropriée',
                                 'Moyen')
    
    def check_form_validation(self, content, page_name):
        """Vérifier la validation des formulaires"""
        
        # Chercher les formulaires
        forms = re.findall(r'<form[^>]*>.*?</form>', content, re.DOTALL)
        
        for form in forms:
            # Vérifier les champs email sans validation
            email_inputs = re.findall(r'<input[^>]*type="email"[^>]*>', form)
            for email_input in email_inputs:
                if 'required' not in email_input:
                    self.log_issue('missing_validation', page_name,
                                 'Champ email sans validation required',
                                 'Ajouter l\'attribut required aux champs email',
                                 'Moyen')
            
            # Vérifier les champs texte sans validation
            text_inputs = re.findall(r'<input[^>]*type="text"[^>]*>', form)
            for text_input in text_inputs:
                if 'name=' in text_input and ('nom' in text_input.lower() or 'prenom' in text_input.lower()):
                    if 'required' not in text_input:
                        self.log_issue('missing_validation', page_name,
                                     'Champ nom/prénom sans validation required',
                                     'Ajouter l\'attribut required aux champs obligatoires',
                                     'Moyen')
            
            # Vérifier les formulaires sans feedback d'erreur
            if 'showNotification' not in content and 'alert(' not in content:
                self.log_issue('poor_ux', page_name,
                             'Formulaire sans feedback d\'erreur',
                             'Ajouter des notifications pour les erreurs de validation',
                             'Moyen')
    
    def check_user_feedback(self, content, page_name):
        """Vérifier le feedback utilisateur"""
        
        # Vérifier les boutons sans loading state
        if '<button' in content and 'spinner' not in content and 'loading' not in content:
            self.log_issue('poor_ux', page_name,
                         'Boutons sans état de chargement',
                         'Ajouter des spinners ou états de chargement pour les actions longues',
                         'Faible')
        
        # Vérifier les actions sans confirmation
        delete_buttons = re.findall(r'onclick="[^"]*delete[^"]*"', content, re.IGNORECASE)
        for delete_btn in delete_buttons:
            if 'confirm(' not in delete_btn:
                self.log_issue('poor_ux', page_name,
                             'Action de suppression sans confirmation',
                             'Ajouter une confirmation avant suppression',
                             'Élevé')
        
        # Vérifier la présence de notifications
        if 'showNotification' not in content and len(re.findall(r'<button', content)) > 3:
            self.log_issue('poor_ux', page_name,
                         'Page sans système de notifications',
                         'Implémenter un système de notifications utilisateur',
                         'Moyen')
    
    def check_accessibility(self, content, page_name):
        """Vérifier l'accessibilité"""
        
        # Vérifier les images sans alt
        img_tags = re.findall(r'<img[^>]*>', content)
        for img in img_tags:
            if 'alt=' not in img:
                self.log_issue('accessibility', page_name,
                             'Image sans attribut alt',
                             'Ajouter des attributs alt descriptifs aux images',
                             'Moyen')
        
        # Vérifier les boutons sans aria-label
        icon_buttons = re.findall(r'<button[^>]*><i[^>]*bi-[^>]*></i></button>', content)
        for btn in icon_buttons:
            if 'aria-label=' not in btn and 'title=' not in btn:
                self.log_issue('accessibility', page_name,
                             'Bouton icône sans label accessible',
                             'Ajouter aria-label ou title aux boutons icônes',
                             'Faible')
        
        # Vérifier les contrastes (basique)
        if 'color: #fff' in content and 'background' not in content:
            self.log_issue('accessibility', page_name,
                         'Texte blanc potentiellement sans arrière-plan',
                         'Vérifier les contrastes de couleurs',
                         'Faible')
    
    def check_mobile_responsiveness(self, content, page_name):
        """Vérifier la responsivité mobile"""
        
        # Vérifier la présence de viewport meta
        if '<meta name="viewport"' not in content:
            self.log_issue('mobile_issues', page_name,
                         'Meta viewport manquant',
                         'Ajouter <meta name="viewport" content="width=device-width, initial-scale=1">',
                         'Élevé')
        
        # Vérifier l'utilisation de classes Bootstrap responsive
        if 'col-' in content and 'col-md-' not in content and 'col-lg-' not in content:
            self.log_issue('mobile_issues', page_name,
                         'Colonnes Bootstrap non responsives',
                         'Utiliser col-sm-, col-md-, col-lg- pour la responsivité',
                         'Moyen')
        
        # Vérifier les tableaux non responsives
        if '<table' in content and 'table-responsive' not in content:
            self.log_issue('mobile_issues', page_name,
                         'Tableau non responsive',
                         'Entourer les tableaux avec div.table-responsive',
                         'Moyen')
    
    def check_consistency(self, content, page_name):
        """Vérifier la cohérence de l'interface"""
        
        # Vérifier la cohérence des classes de boutons
        button_classes = re.findall(r'class="[^"]*btn[^"]*"', content)
        primary_buttons = [b for b in button_classes if 'btn-primary' in b]
        
        if len(primary_buttons) > 3:
            self.log_issue('inconsistencies', page_name,
                         'Trop de boutons primaires',
                         'Limiter à 1-2 boutons primaires par page pour la hiérarchie visuelle',
                         'Faible')
        
        # Vérifier la cohérence des icônes
        icons = re.findall(r'<i[^>]*class="[^"]*bi-([^"]*)"', content)
        if len(set(icons)) > 20:  # Trop d'icônes différentes
            self.log_issue('inconsistencies', page_name,
                         'Trop d\'icônes différentes',
                         'Standardiser l\'utilisation des icônes',
                         'Faible')
    
    def log_issue(self, category, page, title, solution, severity):
        """Enregistrer un problème UI"""
        issue = {
            'page': page,
            'title': title,
            'solution': solution,
            'severity': severity,
            'timestamp': datetime.now().isoformat()
        }
        
        self.ui_issues[category].append(issue)
        
        severity_icons = {
            'Critique': '🔴',
            'Élevé': '🟠',
            'Moyen': '🟡',
            'Faible': '🔵'
        }
        
        print(f"  {severity_icons.get(severity, '⚪')} {title}")
    
    def analyze_all_pages(self):
        """Analyser toutes les pages HTML"""
        print("🎨 ANALYSE DES PROBLÈMES D'INTERFACE UTILISATEUR")
        print("="*60)
        
        pages = [
            ('dashboard.html', 'Dashboard'),
            ('clients.html', 'Gestion Clients'),
            ('vendeurs.html', 'Gestion Vendeurs'),
            ('emails.html', 'Gestion Emails'),
            ('reports.html', 'Rapports'),
            ('admin_config.html', 'Configuration Admin'),
            ('login.html', 'Page de Connexion')
        ]
        
        for page_file, page_name in pages:
            file_path = self.base_dir / page_file
            if file_path.exists():
                self.analyze_html_file(file_path, page_name)
            else:
                self.log_issue('broken_functionality', page_name,
                             f'Fichier {page_file} manquant',
                             f'Créer le fichier {page_file}',
                             'Critique')
            print()
    
    def generate_ui_report(self):
        """Générer le rapport des problèmes UI"""
        print("📊 RAPPORT DES PROBLÈMES D'INTERFACE UTILISATEUR")
        print("="*60)
        
        category_names = {
            'broken_functionality': '🔧 Fonctionnalités Cassées',
            'missing_validation': '✅ Validation Manquante',
            'poor_ux': '😕 Expérience Utilisateur Pauvre',
            'inconsistencies': '🔄 Incohérences',
            'accessibility': '♿ Accessibilité',
            'mobile_issues': '📱 Problèmes Mobile'
        }
        
        total_issues = sum(len(issues) for issues in self.ui_issues.values())
        print(f"\n🎯 RÉSUMÉ: {total_issues} problèmes UI identifiés")
        
        for category, name in category_names.items():
            issues = self.ui_issues[category]
            if issues:
                print(f"\n{name} ({len(issues)}):")
                
                # Grouper par sévérité
                by_severity = {}
                for issue in issues:
                    severity = issue['severity']
                    if severity not in by_severity:
                        by_severity[severity] = []
                    by_severity[severity].append(issue)
                
                # Afficher par ordre de sévérité
                for severity in ['Critique', 'Élevé', 'Moyen', 'Faible']:
                    if severity in by_severity:
                        print(f"  {severity}:")
                        for issue in by_severity[severity][:3]:  # Limiter l'affichage
                            print(f"    • {issue['page']}: {issue['title']}")
                            print(f"      → {issue['solution']}")
                        if len(by_severity[severity]) > 3:
                            print(f"    ... et {len(by_severity[severity]) - 3} autres")
        
        # Recommandations prioritaires
        self.generate_ui_recommendations()
        
        # Sauvegarder le rapport
        report_data = {
            'analysis_date': datetime.now().isoformat(),
            'total_issues': total_issues,
            'issues_by_category': {k: len(v) for k, v in self.ui_issues.items()},
            'detailed_issues': self.ui_issues
        }
        
        report_file = self.base_dir / f"ui_problems_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Rapport détaillé sauvegardé: {report_file}")
        return report_data
    
    def generate_ui_recommendations(self):
        """Générer les recommandations UI prioritaires"""
        print(f"\n🎯 RECOMMANDATIONS UI PRIORITAIRES:")
        
        # Compter les problèmes critiques et élevés
        critical_issues = []
        high_issues = []
        
        for category, issues in self.ui_issues.items():
            for issue in issues:
                if issue['severity'] == 'Critique':
                    critical_issues.append(issue)
                elif issue['severity'] == 'Élevé':
                    high_issues.append(issue)
        
        if critical_issues:
            print(f"\n🚨 PROBLÈMES CRITIQUES (Action immédiate requise):")
            for i, issue in enumerate(critical_issues[:5], 1):
                print(f"  {i}. {issue['page']}: {issue['title']}")
                print(f"     Solution: {issue['solution']}")
        
        if high_issues:
            print(f"\n⚠️  PROBLÈMES ÉLEVÉS (À corriger cette semaine):")
            for i, issue in enumerate(high_issues[:5], 1):
                print(f"  {i}. {issue['page']}: {issue['title']}")
                print(f"     Solution: {issue['solution']}")
        
        # Recommandations générales
        print(f"\n💡 RECOMMANDATIONS GÉNÉRALES:")
        print("  1. Implémenter un système de notifications cohérent sur toutes les pages")
        print("  2. Ajouter des états de chargement pour toutes les actions asynchrones")
        print("  3. Standardiser la validation des formulaires avec HTML5 et JavaScript")
        print("  4. Améliorer l'accessibilité avec des labels et descriptions appropriés")
        print("  5. Optimiser la responsivité mobile avec Bootstrap responsive")
    
    def run_analysis(self):
        """Exécuter l'analyse complète"""
        self.analyze_all_pages()
        return self.generate_ui_report()

if __name__ == "__main__":
    analyzer = UIProblemsAnalyzer()
    report = analyzer.run_analysis()

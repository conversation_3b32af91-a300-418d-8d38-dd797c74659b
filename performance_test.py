#!/usr/bin/env python3
"""
BINANCE CRM - Test de Performance Complet
Analyse des performances API et base de données
"""

import requests
import time
import json
import sqlite3
import threading
from concurrent.futures import ThreadPoolExecutor
import statistics

class PerformanceAnalyzer:
    def __init__(self, base_url='http://localhost:8000'):
        self.base_url = base_url
        self.results = {}
        
    def measure_time(self, func, *args, **kwargs):
        """Mesurer le temps d'exécution d'une fonction"""
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            end_time = time.time()
            return {
                'success': True,
                'duration': end_time - start_time,
                'result': result
            }
        except Exception as e:
            end_time = time.time()
            return {
                'success': False,
                'duration': end_time - start_time,
                'error': str(e)
            }
    
    def test_api_endpoint(self, endpoint, method='GET', data=None):
        """Tester un endpoint API"""
        url = f"{self.base_url}{endpoint}"
        
        if method == 'GET':
            response = requests.get(url)
        elif method == 'POST':
            response = requests.post(url, json=data)
        
        return {
            'status_code': response.status_code,
            'response_time': response.elapsed.total_seconds(),
            'content_length': len(response.content),
            'success': response.status_code == 200
        }
    
    def test_database_performance(self):
        """Tester les performances de la base de données"""
        print("🔍 Test des performances de la base de données...")
        
        try:
            conn = sqlite3.connect('binance_crm.db')
            cursor = conn.cursor()
            
            # Test 1: Requête simple
            result1 = self.measure_time(cursor.execute, "SELECT COUNT(*) FROM clients")
            
            # Test 2: Requête avec jointure
            result2 = self.measure_time(cursor.execute, """
                SELECT c.*, u.first_name, u.last_name 
                FROM clients c 
                LEFT JOIN users u ON c.assigned_to = u.id
            """)
            
            # Test 3: Requête complexe
            result3 = self.measure_time(cursor.execute, """
                SELECT u.first_name, u.last_name, COUNT(c.id) as client_count
                FROM users u
                LEFT JOIN clients c ON u.id = c.assigned_to
                WHERE u.role = 'vendeur'
                GROUP BY u.id
                ORDER BY client_count DESC
            """)
            
            conn.close()
            
            return {
                'simple_query': result1['duration'],
                'join_query': result2['duration'],
                'complex_query': result3['duration']
            }
            
        except Exception as e:
            return {'error': str(e)}
    
    def test_api_endpoints(self):
        """Tester tous les endpoints API"""
        print("🌐 Test des endpoints API...")
        
        endpoints = [
            {'path': '/api/clients', 'method': 'GET'},
            {'path': '/api/users', 'method': 'GET'},
            {'path': '/api/vendeurs', 'method': 'GET'},
            {'path': '/api/email-templates', 'method': 'GET'},
            {'path': '/api/import-template', 'method': 'GET'},
            {'path': '/api/import-history', 'method': 'GET'},
            {'path': '/api/dashboard-stats', 'method': 'GET'}
        ]
        
        results = {}
        
        for endpoint in endpoints:
            print(f"  Testing {endpoint['path']}...")
            try:
                result = self.measure_time(
                    self.test_api_endpoint, 
                    endpoint['path'], 
                    endpoint['method']
                )
                results[endpoint['path']] = result
            except Exception as e:
                results[endpoint['path']] = {'error': str(e)}
        
        return results
    
    def test_concurrent_load(self, endpoint='/api/clients', num_requests=10):
        """Tester la charge concurrente"""
        print(f"⚡ Test de charge concurrente ({num_requests} requêtes simultanées)...")
        
        def make_request():
            return self.test_api_endpoint(endpoint)
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_requests) as executor:
            futures = [executor.submit(make_request) for _ in range(num_requests)]
            results = [future.result() for future in futures]
        
        end_time = time.time()
        
        response_times = [r['response_time'] for r in results if 'response_time' in r]
        success_count = sum(1 for r in results if r.get('success', False))
        
        return {
            'total_time': end_time - start_time,
            'success_rate': success_count / num_requests * 100,
            'avg_response_time': statistics.mean(response_times) if response_times else 0,
            'min_response_time': min(response_times) if response_times else 0,
            'max_response_time': max(response_times) if response_times else 0,
            'requests_per_second': num_requests / (end_time - start_time)
        }
    
    def test_csv_import_performance(self):
        """Tester les performances d'import CSV"""
        print("📊 Test des performances d'import CSV...")
        
        # Créer des données de test
        test_data = {
            'csv_data': [
                {
                    'nom': f'TEST{i}',
                    'prenom': f'User{i}',
                    'email': f'test{i}@example.com',
                    'telephone': f'012345678{i%10}',
                    'statut': 'prospect'
                }
                for i in range(100)  # 100 clients de test
            ],
            'filename': 'performance_test.csv',
            'user_id': 1,
            'skip_errors': True
        }
        
        try:
            result = self.measure_time(
                self.test_api_endpoint,
                '/api/import-clients',
                'POST',
                test_data
            )
            return result
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_memory_usage(self):
        """Analyser l'utilisation mémoire"""
        print("💾 Analyse de l'utilisation mémoire...")
        
        try:
            import psutil
            import os
            
            # Trouver le processus du serveur
            current_process = psutil.Process(os.getpid())
            
            return {
                'memory_usage_mb': current_process.memory_info().rss / 1024 / 1024,
                'cpu_percent': current_process.cpu_percent(),
                'num_threads': current_process.num_threads()
            }
        except ImportError:
            return {'error': 'psutil non installé - impossible d\'analyser la mémoire'}
        except Exception as e:
            return {'error': str(e)}
    
    def run_full_analysis(self):
        """Exécuter l'analyse complète"""
        print("🚀 ANALYSE COMPLÈTE DE PERFORMANCE - BINANCE CRM")
        print("=" * 60)
        
        # Test 1: Base de données
        db_results = self.test_database_performance()
        
        # Test 2: Endpoints API
        api_results = self.test_api_endpoints()
        
        # Test 3: Charge concurrente
        load_results = self.test_concurrent_load()
        
        # Test 4: Import CSV
        csv_results = self.test_csv_import_performance()
        
        # Test 5: Mémoire
        memory_results = self.analyze_memory_usage()
        
        # Générer le rapport
        self.generate_report({
            'database': db_results,
            'api_endpoints': api_results,
            'concurrent_load': load_results,
            'csv_import': csv_results,
            'memory': memory_results
        })
    
    def generate_report(self, results):
        """Générer le rapport de performance"""
        print("\n📊 RAPPORT DE PERFORMANCE")
        print("=" * 40)
        
        # Base de données
        if 'database' in results and 'error' not in results['database']:
            db = results['database']
            print(f"\n🗄️  BASE DE DONNÉES:")
            print(f"   Requête simple: {db['simple_query']:.3f}s")
            print(f"   Requête jointure: {db['join_query']:.3f}s")
            print(f"   Requête complexe: {db['complex_query']:.3f}s")
            
            # Évaluation
            if max(db.values()) > 1.0:
                print("   ⚠️  ATTENTION: Requêtes lentes détectées")
            elif max(db.values()) > 0.5:
                print("   ⚡ Performance acceptable")
            else:
                print("   ✅ Excellente performance")
        
        # API Endpoints
        if 'api_endpoints' in results:
            print(f"\n🌐 ENDPOINTS API:")
            api_success = 0
            api_total = 0
            avg_response_time = 0
            
            for endpoint, result in results['api_endpoints'].items():
                api_total += 1
                if result.get('success', False):
                    api_success += 1
                    duration = result.get('duration', 0)
                    avg_response_time += duration
                    status = "✅" if duration < 0.5 else "⚠️" if duration < 1.0 else "❌"
                    print(f"   {endpoint}: {duration:.3f}s {status}")
                else:
                    print(f"   {endpoint}: ERREUR ❌")
            
            success_rate = (api_success / api_total * 100) if api_total > 0 else 0
            avg_response_time = (avg_response_time / api_success) if api_success > 0 else 0
            
            print(f"   Taux de succès: {success_rate:.1f}%")
            print(f"   Temps de réponse moyen: {avg_response_time:.3f}s")
        
        # Charge concurrente
        if 'concurrent_load' in results:
            load = results['concurrent_load']
            print(f"\n⚡ CHARGE CONCURRENTE:")
            print(f"   Taux de succès: {load['success_rate']:.1f}%")
            print(f"   Requêtes/seconde: {load['requests_per_second']:.1f}")
            print(f"   Temps de réponse moyen: {load['avg_response_time']:.3f}s")
            print(f"   Temps de réponse max: {load['max_response_time']:.3f}s")
        
        # Import CSV
        if 'csv_import' in results and results['csv_import'].get('success', False):
            csv = results['csv_import']
            print(f"\n📊 IMPORT CSV (100 clients):")
            print(f"   Durée: {csv['duration']:.3f}s")
            print(f"   Vitesse: {100/csv['duration']:.1f} clients/seconde")
            
            if csv['duration'] > 10:
                print("   ⚠️  Import lent - optimisation recommandée")
            else:
                print("   ✅ Performance d'import acceptable")
        
        # Mémoire
        if 'memory' in results and 'error' not in results['memory']:
            mem = results['memory']
            print(f"\n💾 UTILISATION MÉMOIRE:")
            print(f"   RAM utilisée: {mem['memory_usage_mb']:.1f} MB")
            print(f"   CPU: {mem['cpu_percent']:.1f}%")
            print(f"   Threads: {mem['num_threads']}")
        
        # Recommandations
        self.generate_recommendations(results)
    
    def generate_recommendations(self, results):
        """Générer des recommandations d'optimisation"""
        print(f"\n🔧 RECOMMANDATIONS D'OPTIMISATION:")
        print("-" * 40)
        
        recommendations = []
        
        # Analyser les résultats et générer des recommandations
        if 'database' in results and 'error' not in results['database']:
            db = results['database']
            if max(db.values()) > 1.0:
                recommendations.append("🗄️  Ajouter des index sur les colonnes fréquemment utilisées")
                recommendations.append("🗄️  Optimiser les requêtes avec jointures")
        
        if 'concurrent_load' in results:
            load = results['concurrent_load']
            if load['success_rate'] < 95:
                recommendations.append("⚡ Implémenter un pool de connexions")
                recommendations.append("⚡ Ajouter un système de cache")
        
        if 'csv_import' in results and results['csv_import'].get('success', False):
            csv = results['csv_import']
            if csv['duration'] > 5:
                recommendations.append("📊 Optimiser l'import CSV avec des transactions batch")
                recommendations.append("📊 Implémenter un système de queue pour les gros imports")
        
        if 'memory' in results and 'error' not in results['memory']:
            mem = results['memory']
            if mem['memory_usage_mb'] > 100:
                recommendations.append("💾 Optimiser la gestion mémoire")
        
        if not recommendations:
            recommendations.append("✅ Aucune optimisation critique nécessaire")
        
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")

if __name__ == "__main__":
    analyzer = PerformanceAnalyzer()
    analyzer.run_full_analysis()

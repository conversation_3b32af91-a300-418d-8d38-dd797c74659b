#!/usr/bin/env python3
"""
BINANCE CRM - Audit Complet du Système
Analyse exhaustive de tous les problèmes et opportunités d'amélioration
"""

import requests
import json
import time
import sqlite3
from pathlib import Path
from datetime import datetime
import re

class ComprehensiveCRMAudit:
    def __init__(self):
        self.base_url = 'http://localhost:8000'
        self.email_server_url = 'http://localhost:8001'
        self.pdf_server_url = 'http://localhost:8002'
        self.base_dir = Path(__file__).parent
        
        self.issues = {
            'critical': [],
            'high': [],
            'medium': [],
            'low': []
        }
        
        self.opportunities = {
            'ui_improvements': [],
            'feature_completeness': [],
            'api_enhancements': [],
            'performance_optimizations': []
        }
        
    def log_issue(self, severity, category, title, description, solution="", effort=""):
        """Enregistrer un problème identifié"""
        issue = {
            'category': category,
            'title': title,
            'description': description,
            'solution': solution,
            'effort': effort,
            'timestamp': datetime.now().isoformat()
        }
        
        self.issues[severity].append(issue)
        
        severity_icons = {
            'critical': '🔴',
            'high': '🟠', 
            'medium': '🟡',
            'low': '🔵'
        }
        
        print(f"  {severity_icons[severity]} {title}")
        if description:
            print(f"    {description}")
    
    def log_opportunity(self, category, title, description, benefit="", effort=""):
        """Enregistrer une opportunité d'amélioration"""
        opportunity = {
            'title': title,
            'description': description,
            'benefit': benefit,
            'effort': effort,
            'timestamp': datetime.now().isoformat()
        }
        
        self.opportunities[category].append(opportunity)
        print(f"  💡 {title}")
    
    def test_user_interface_elements(self):
        """Tester tous les éléments d'interface utilisateur"""
        print("🌐 Test des éléments d'interface utilisateur...")
        
        # Test des pages principales
        pages = [
            ('dashboard.html', 'Dashboard'),
            ('clients.html', 'Gestion Clients'),
            ('vendeurs.html', 'Gestion Vendeurs'),
            ('emails.html', 'Gestion Emails'),
            ('reports.html', 'Rapports'),
            ('admin_config.html', 'Configuration Admin'),
            ('login.html', 'Page de Connexion')
        ]
        
        for page_file, page_name in pages:
            try:
                response = requests.get(f"{self.base_url}/{page_file}", timeout=10)
                if response.status_code == 200:
                    content = response.text
                    self.analyze_page_content(page_file, page_name, content)
                else:
                    self.log_issue('high', 'UI', f'Page {page_name} inaccessible', 
                                 f'Code de réponse: {response.status_code}',
                                 'Vérifier la configuration du serveur', 'Facile')
            except Exception as e:
                self.log_issue('critical', 'UI', f'Page {page_name} non accessible',
                             f'Erreur: {str(e)}',
                             'Vérifier que le serveur est démarré', 'Facile')
    
    def analyze_page_content(self, page_file, page_name, content):
        """Analyser le contenu d'une page pour identifier les problèmes"""
        
        # Vérifier les éléments JavaScript manquants
        if 'function' in content:
            # Chercher les fonctions appelées mais non définies
            function_calls = re.findall(r'onclick="([^"]+)"', content)
            for call in function_calls:
                func_name = call.split('(')[0]
                if f'function {func_name}' not in content and f'{func_name} =' not in content:
                    self.log_issue('medium', 'UI', f'Fonction JavaScript manquante: {func_name}',
                                 f'Dans {page_name}, fonction {func_name} appelée mais non définie',
                                 f'Implémenter la fonction {func_name}', 'Moyen')
        
        # Vérifier les liens vers des ressources externes
        if 'bootstrap' not in content.lower():
            self.log_issue('low', 'UI', f'Bootstrap manquant dans {page_name}',
                         'La page pourrait ne pas avoir de styles Bootstrap',
                         'Ajouter les liens Bootstrap CDN', 'Facile')
        
        # Vérifier les formulaires sans validation
        if '<form' in content and 'required' not in content:
            self.log_issue('medium', 'UI', f'Validation formulaire manquante: {page_name}',
                         'Les formulaires n\'ont pas de validation HTML5',
                         'Ajouter des attributs required et pattern', 'Moyen')
        
        # Vérifier les boutons sans feedback
        button_count = content.count('<button')
        if button_count > 0 and 'showNotification' not in content:
            self.log_opportunity('ui_improvements', f'Feedback utilisateur manquant: {page_name}',
                               'Les boutons n\'ont pas de feedback visuel',
                               'Amélioration de l\'expérience utilisateur', 'Moyen')
    
    def test_api_endpoints(self):
        """Tester tous les endpoints API"""
        print("🔌 Test des endpoints API...")
        
        # Endpoints principaux à tester
        endpoints = [
            ('GET', '/api/clients', 'Liste des clients'),
            ('GET', '/api/users', 'Liste des utilisateurs'),
            ('GET', '/api/vendeurs', 'Liste des vendeurs'),
            ('GET', '/api/dashboard-stats', 'Statistiques dashboard'),
            ('GET', '/api/email-templates', 'Templates email'),
            ('GET', '/api/import-template', 'Template CSV'),
            ('GET', '/api/import-history', 'Historique imports'),
            ('GET', '/api/health', 'Santé système'),
            ('POST', '/api/clients', 'Création client'),
            ('POST', '/api/import-clients', 'Import clients'),
            ('POST', '/api/validate-csv', 'Validation CSV')
        ]
        
        for method, endpoint, description in endpoints:
            try:
                if method == 'GET':
                    response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                elif method == 'POST':
                    # Test avec données minimales
                    test_data = self.get_test_data_for_endpoint(endpoint)
                    response = requests.post(f"{self.base_url}{endpoint}", 
                                           json=test_data, timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if not result.get('success', True):
                        self.log_issue('medium', 'API', f'Endpoint {endpoint} retourne une erreur',
                                     f'{description}: {result.get("error", "Erreur inconnue")}',
                                     'Vérifier la logique métier de l\'endpoint', 'Moyen')
                elif response.status_code == 404:
                    self.log_issue('high', 'API', f'Endpoint {endpoint} non trouvé',
                                 f'{description} n\'est pas implémenté',
                                 f'Implémenter l\'endpoint {endpoint}', 'Difficile')
                elif response.status_code == 500:
                    self.log_issue('critical', 'API', f'Erreur serveur {endpoint}',
                                 f'{description} génère une erreur serveur',
                                 'Déboguer et corriger l\'erreur serveur', 'Moyen')
                else:
                    self.log_issue('medium', 'API', f'Réponse inattendue {endpoint}',
                                 f'{description} retourne le code {response.status_code}',
                                 'Vérifier la logique de l\'endpoint', 'Moyen')
                    
            except requests.exceptions.Timeout:
                self.log_issue('high', 'API', f'Timeout {endpoint}',
                             f'{description} prend trop de temps à répondre',
                             'Optimiser les performances de l\'endpoint', 'Moyen')
            except Exception as e:
                self.log_issue('high', 'API', f'Erreur {endpoint}',
                             f'{description}: {str(e)}',
                             'Vérifier la connectivité et l\'implémentation', 'Moyen')
    
    def get_test_data_for_endpoint(self, endpoint):
        """Obtenir des données de test pour un endpoint"""
        test_data = {
            '/api/clients': {
                'first_name': 'Test',
                'last_name': 'User',
                'email': f'test.{int(time.time())}@example.com',
                'phone': '0123456789',
                'status': 'prospect'
            },
            '/api/import-clients': {
                'csv_data': [{'nom': 'Test', 'prenom': 'User', 'email': '<EMAIL>'}],
                'filename': 'test.csv',
                'user_id': 1
            },
            '/api/validate-csv': {
                'csv_data': [{'nom': 'Test', 'prenom': 'User', 'email': '<EMAIL>'}]
            }
        }
        return test_data.get(endpoint, {})
    
    def test_database_integrity(self):
        """Tester l'intégrité de la base de données"""
        print("🗄️  Test de l'intégrité de la base de données...")
        
        db_path = self.base_dir / "binance_crm.db"
        if not db_path.exists():
            self.log_issue('critical', 'Database', 'Base de données manquante',
                         'Le fichier binance_crm.db n\'existe pas',
                         'Initialiser la base de données', 'Facile')
            return
        
        try:
            conn = sqlite3.connect(str(db_path))
            cursor = conn.cursor()
            
            # Vérifier les tables principales
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]
            
            required_tables = ['users', 'clients', 'email_templates', 'import_history']
            for table in required_tables:
                if table not in tables:
                    self.log_issue('high', 'Database', f'Table {table} manquante',
                                 f'La table {table} n\'existe pas dans la base',
                                 f'Créer la table {table} avec le schéma approprié', 'Moyen')
            
            # Vérifier l'intégrité des données
            for table in tables:
                if table in required_tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table}")
                    count = cursor.fetchone()[0]
                    
                    if table == 'users' and count == 0:
                        self.log_issue('medium', 'Database', 'Aucun utilisateur dans la base',
                                     'La table users est vide',
                                     'Créer au moins un utilisateur administrateur', 'Facile')
            
            # Vérifier les index de performance
            cursor.execute("SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND name LIKE 'idx_%'")
            index_count = cursor.fetchone()[0]
            
            if index_count < 5:
                self.log_opportunity('performance_optimizations', 'Index de performance manquants',
                                   f'Seulement {index_count} index trouvés',
                                   'Amélioration des performances des requêtes', 'Moyen')
            
            conn.close()
            
        except Exception as e:
            self.log_issue('critical', 'Database', 'Erreur d\'accès à la base',
                         f'Impossible d\'accéder à la base: {str(e)}',
                         'Vérifier les permissions et l\'intégrité du fichier', 'Moyen')
    
    def test_feature_completeness(self):
        """Tester la complétude des fonctionnalités"""
        print("🔧 Test de la complétude des fonctionnalités...")
        
        # Fonctionnalités CRM standard attendues
        expected_features = {
            'Gestion des contacts': {
                'create': True,
                'read': True, 
                'update': True,
                'delete': True,
                'search': False,
                'export': True,
                'import': True
            },
            'Gestion des vendeurs': {
                'create': True,
                'read': True,
                'update': True,
                'delete': True,
                'performance_tracking': False,
                'territory_management': False
            },
            'Email marketing': {
                'templates': True,
                'send_bulk': False,
                'tracking': False,
                'automation': False
            },
            'Rapports': {
                'dashboard': True,
                'custom_reports': False,
                'export_pdf': False,
                'scheduled_reports': False
            }
        }
        
        for feature_category, features in expected_features.items():
            missing_features = [f for f, implemented in features.items() if not implemented]
            if missing_features:
                for feature in missing_features:
                    self.log_opportunity('feature_completeness', 
                                       f'{feature_category}: {feature} manquant',
                                       f'Fonctionnalité {feature} non implémentée',
                                       'Complétude du CRM selon les standards', 'Variable')
    
    def test_error_handling(self):
        """Tester la gestion d'erreurs"""
        print("⚠️  Test de la gestion d'erreurs...")
        
        # Test avec des données invalides
        error_test_cases = [
            {
                'endpoint': '/api/clients',
                'data': {'email': 'invalid-email'},
                'expected': 'Validation email'
            },
            {
                'endpoint': '/api/clients',
                'data': {'first_name': ''},
                'expected': 'Champ requis'
            },
            {
                'endpoint': '/api/import-clients',
                'data': {'csv_data': []},
                'expected': 'Données vides'
            }
        ]
        
        for test_case in error_test_cases:
            try:
                response = requests.post(f"{self.base_url}{test_case['endpoint']}", 
                                       json=test_case['data'], timeout=10)
                
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success', True):
                        self.log_issue('medium', 'Validation', 
                                     f'Validation manquante: {test_case["expected"]}',
                                     f'L\'endpoint accepte des données invalides',
                                     'Ajouter la validation côté serveur', 'Moyen')
                
            except Exception as e:
                # Les erreurs de connexion sont normales ici
                pass
    
    def test_performance(self):
        """Tester les performances du système"""
        print("⚡ Test des performances...")
        
        # Test de temps de réponse
        performance_endpoints = [
            '/api/clients',
            '/api/dashboard-stats',
            '/api/health'
        ]
        
        for endpoint in performance_endpoints:
            try:
                start_time = time.time()
                response = requests.get(f"{self.base_url}{endpoint}", timeout=10)
                duration = time.time() - start_time
                
                if duration > 2.0:
                    self.log_issue('medium', 'Performance', f'Endpoint lent: {endpoint}',
                                 f'Temps de réponse: {duration:.2f}s',
                                 'Optimiser les requêtes et ajouter du cache', 'Moyen')
                elif duration > 5.0:
                    self.log_issue('high', 'Performance', f'Endpoint très lent: {endpoint}',
                                 f'Temps de réponse: {duration:.2f}s',
                                 'Optimisation urgente requise', 'Difficile')
                    
            except Exception as e:
                pass
    
    def test_external_services(self):
        """Tester les services externes"""
        print("🌐 Test des services externes...")
        
        # Test serveur email
        try:
            response = requests.get(f"{self.email_server_url}/health", timeout=5)
            if response.status_code != 200:
                self.log_issue('medium', 'Services', 'Serveur email non accessible',
                             'Le serveur email (port 8001) ne répond pas',
                             'Démarrer le serveur email ou le rendre optionnel', 'Facile')
        except:
            self.log_opportunity('feature_completeness', 'Serveur email optionnel',
                               'Le serveur email n\'est pas démarré par défaut',
                               'Amélioration de la robustesse du système', 'Moyen')
        
        # Test serveur PDF
        try:
            response = requests.get(f"{self.pdf_server_url}/health", timeout=5)
            if response.status_code != 200:
                self.log_issue('low', 'Services', 'Serveur PDF non accessible',
                             'Le serveur PDF (port 8002) ne répond pas',
                             'Démarrer le serveur PDF ou l\'intégrer au serveur principal', 'Moyen')
        except:
            self.log_opportunity('feature_completeness', 'Génération PDF intégrée',
                               'Le serveur PDF pourrait être intégré au serveur principal',
                               'Simplification de l\'architecture', 'Difficile')
    
    def generate_comprehensive_report(self):
        """Générer le rapport complet d'audit"""
        print("\n" + "="*80)
        print("📊 RAPPORT COMPLET D'AUDIT - BINANCE CRM")
        print("="*80)
        
        # Compter les problèmes par sévérité
        total_issues = sum(len(issues) for issues in self.issues.values())
        total_opportunities = sum(len(opps) for opps in self.opportunities.values())
        
        print(f"\n🔍 RÉSUMÉ EXÉCUTIF:")
        print(f"  Problèmes identifiés: {total_issues}")
        print(f"  Opportunités d'amélioration: {total_opportunities}")
        
        # Détail des problèmes par sévérité
        severity_names = {
            'critical': '🔴 CRITIQUES',
            'high': '🟠 ÉLEVÉS', 
            'medium': '🟡 MOYENS',
            'low': '🔵 FAIBLES'
        }
        
        for severity, name in severity_names.items():
            issues = self.issues[severity]
            if issues:
                print(f"\n{name} ({len(issues)}):")
                for issue in issues[:5]:  # Limiter l'affichage
                    print(f"  • {issue['title']}")
                    if issue['solution']:
                        print(f"    → Solution: {issue['solution']}")
                if len(issues) > 5:
                    print(f"  ... et {len(issues) - 5} autres")
        
        # Opportunités d'amélioration
        opportunity_names = {
            'ui_improvements': '🎨 Améliorations UI',
            'feature_completeness': '🔧 Complétude fonctionnelle',
            'api_enhancements': '🔌 Améliorations API',
            'performance_optimizations': '⚡ Optimisations performance'
        }
        
        print(f"\n💡 OPPORTUNITÉS D'AMÉLIORATION:")
        for category, name in opportunity_names.items():
            opportunities = self.opportunities[category]
            if opportunities:
                print(f"\n{name} ({len(opportunities)}):")
                for opp in opportunities[:3]:
                    print(f"  • {opp['title']}")
        
        # Recommandations prioritaires
        self.generate_priority_recommendations()
        
        # Sauvegarder le rapport détaillé
        report_data = {
            'audit_date': datetime.now().isoformat(),
            'summary': {
                'total_issues': total_issues,
                'total_opportunities': total_opportunities,
                'critical_issues': len(self.issues['critical']),
                'high_issues': len(self.issues['high'])
            },
            'issues': self.issues,
            'opportunities': self.opportunities
        }
        
        report_file = self.base_dir / f"audit_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"\n📄 Rapport détaillé sauvegardé: {report_file}")
        
        return report_data
    
    def generate_priority_recommendations(self):
        """Générer les recommandations prioritaires"""
        print(f"\n🎯 RECOMMANDATIONS PRIORITAIRES:")
        
        # Problèmes critiques en premier
        if self.issues['critical']:
            print(f"\n🚨 ACTIONS IMMÉDIATES REQUISES:")
            for issue in self.issues['critical']:
                print(f"  1. {issue['title']}")
                print(f"     {issue['solution']}")
        
        # Problèmes élevés
        if self.issues['high']:
            print(f"\n⚠️  ACTIONS PRIORITAIRES (Cette semaine):")
            for i, issue in enumerate(self.issues['high'][:3], 1):
                print(f"  {i}. {issue['title']}")
                print(f"     {issue['solution']}")
        
        # Opportunités à fort impact
        high_impact_opportunities = []
        for category, opportunities in self.opportunities.items():
            for opp in opportunities:
                if 'élevé' in opp.get('benefit', '').lower() or 'critique' in opp.get('benefit', '').lower():
                    high_impact_opportunities.append(opp)
        
        if high_impact_opportunities:
            print(f"\n💎 OPPORTUNITÉS À FORT IMPACT:")
            for i, opp in enumerate(high_impact_opportunities[:3], 1):
                print(f"  {i}. {opp['title']}")
                print(f"     Bénéfice: {opp['benefit']}")
    
    def run_comprehensive_audit(self):
        """Exécuter l'audit complet"""
        print("🔍 DÉMARRAGE DE L'AUDIT COMPLET - BINANCE CRM")
        print("="*60)
        
        # Exécuter tous les tests
        self.test_user_interface_elements()
        print()
        self.test_api_endpoints()
        print()
        self.test_database_integrity()
        print()
        self.test_feature_completeness()
        print()
        self.test_error_handling()
        print()
        self.test_performance()
        print()
        self.test_external_services()
        
        # Générer le rapport
        report = self.generate_comprehensive_report()
        
        return report

if __name__ == "__main__":
    auditor = ComprehensiveCRMAudit()
    report = auditor.run_comprehensive_audit()
